<!-- 派发人员弹窗 -->
<template>
  <div>
    <CommonDialog
      v-model:visible="visible"
      title="选择派发人员"
      width="500px"
      :action-disabled="unitLoading || staffLoading"
      :btn-loading="actionLoading"
      :confirm-callback="handleConfirm"
    >
      <el-form
        ref="formRef"
        v-loading="unitLoading || staffLoading"
        :model="formData"
        :rules="rules"
        label-suffix="："
        label-width="100px"
      >
        <el-form-item label="派发单位" prop="unit">
          <el-select
            v-model="formData.unit"
            placeholder="请选择派发单位"
            style="width: 100%"
            :loading="unitLoading"
            @change="handleUnitChange"
          >
            <el-option
              v-for="item in unitList"
              :key="item.id"
              :label="item.companyName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="派发人员" prop="userId">
          <el-select
            v-model="formData.userId"
            class="user-select"
            placeholder="请选择派发人员"
            style="width: 100%"
            :loading="staffLoading"
            filterable
          >
            <template v-if="formData.unit">
              <el-option label="维保公司调度" value="dispatchByEnterprise">
              </el-option>
              <el-option
                v-for="item in staffList"
                :key="item.userId"
                :label="item.username"
                :value="item.userId"
              >
                <div class="user-option-item">
                  <div class="user-info">
                    <span>{{ item.username }}</span>
                    <span style="font-size: 13px">{{ `(${item.phone})` }}</span>
                  </div>

                  <div class="user-qualifications" @click.stop>
                    <div
                      class="qualification-item"
                      v-for="qualification in item.qualifications"
                      :key="qualification.fileId"
                    >
                      <el-icon :size="16" style="margin-right: 6px">
                        <Link />
                      </el-icon>
                      <span>{{ qualification.fileName }}</span>
                    </div>
                  </div>
                </div>
              </el-option>
            </template>
          </el-select>
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { CommonDialog } from "@/base-components";
import { cloneDeep } from "lodash";
import {
  getMaintenanceEnterpriseList,
  getMaintenanceEnterpriseStaffList,
  dispatchWorkOrderApi,
} from "@/api";
import { Message, toastError } from "@/utils";
import type { MaintenanceEnterprise, MaintenanceUser } from "@/types";
import { Link } from "@element-plus/icons-vue";
import { useUserStore } from "@/store";
import { UserTypeEnum } from "@/configs";

const userStore = useUserStore();

const props = defineProps({
  isTransfer: {
    type: Boolean,
    default: false,
  },
  workOrderId: {
    type: String,
    required: true,
  },
});

const visible = ref(false);
const actionLoading = ref(false);

const emit = defineEmits(["submit"]);

/* =================================== 表单操作 =================================== */
const formRef = ref<FormInstance>();
const initFormData = {
  unit: "",
  userId: "",
};
const formData = ref(cloneDeep(initFormData));

const oldData = ref(cloneDeep(initFormData));

// 表单校验规则
const rules = ref<FormRules>({
  unit: [{ required: true, message: "请选择派发单位", trigger: "change" }],
  userId: [{ required: true, message: "请选择派发人员", trigger: "change" }],
});

/* =================================== 数据加载 =================================== */
const unitLoading = ref(false);
const unitList = ref<MaintenanceEnterprise[]>([]);

// 获取维修企业列表
async function getUnitList() {
  unitLoading.value = true;
  try {
    const res = await getMaintenanceEnterpriseList({
      limit: 1000,
      offset: 0,
      filters: "status=ENABLED",
    });
    unitList.value = res.data.data.rows;
    if (userStore.unit) {
      formData.value.unit = userStore.unit;
      getStaffList(userStore.unit);
    }
  } catch (err) {
    toastError(err, "获取维修企业列表失败");
  } finally {
    unitLoading.value = false;
  }
}

const staffLoading = ref(false);
const staffList = ref<MaintenanceUser[]>([]);

// 获取维修人员列表
async function getStaffList(unitId: string) {
  staffLoading.value = true;
  try {
    const res = await getMaintenanceEnterpriseStaffList({
      limit: 10000,
      offset: 0,
      unitId,
      filters: "status=ENABLED",
      showMask: "false",
    });
    staffList.value = res.data.data.rows;
  } catch (err) {
    toastError(err, "获取维修人员列表失败");
  } finally {
    staffLoading.value = false;
  }
}

// 单位变更
function handleUnitChange(value: string) {
  formData.value.userId = "";
  if (value) {
    getStaffList(value);
  } else {
    staffList.value = [];
  }
}

function openDialog(data = null) {
  visible.value = true;
  if (data) {
    formData.value = cloneDeep(data);
    oldData.value = cloneDeep(data);
    if (!data.userId) {
      formData.value.userId = "dispatchByEnterprise";
    }
  } else {
    formData.value = cloneDeep(initFormData);
    oldData.value = cloneDeep(initFormData);
  }
  formRef.value?.resetFields();

  getUnitList();
  if (data?.unit) {
    getStaffList(data.unit);
  }
}

// 提交表单
const handleConfirm = async () => {
  if (!formRef.value) return;

  if (
    formData.value.unit &&
    oldData.value.unit === formData.value.unit &&
    (oldData.value.userId === formData.value.userId ||
      (!oldData.value.userId &&
        formData.value.userId === "dispatchByEnterprise"))
  ) {
    return Message.warning("当前派发人员未变更");
  }

  await formRef.value.validate((valid) => {
    if (!valid) return;

    actionLoading.value = true;
    dispatchWorkOrderApi({
      maintenanceUnitId: formData.value.unit,
      maintenanceUserId:
        formData.value.userId === "dispatchByEnterprise"
          ? ""
          : formData.value.userId,
      isTransfer: props.isTransfer,
      isPlatform: userStore.userType === "PLATFORM",
      workOrderId: props.workOrderId,
    })
      .then((res) => {
        Message.success(`${props.isTransfer ? "转派" : "派单"}成功`);
        visible.value = false;
        emit("submit");
      })
      .catch((err) => {
        toastError(err, "派发失败");
      })
      .finally(() => {
        actionLoading.value = false;
      });
  });
};

defineExpose({
  openDialog,
});
</script>

<style lang="less" scoped>
.el-select-dropdown__item {
  height: fit-content;
  padding: 0px 20px;
  box-sizing: border-box;
}
.el-select {
  width: 100%;
}
.user-option-item {
  display: flex;
  flex-direction: column;
  .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .qualification-title {
    font-weight: 500;
    color: #999;
    line-height: 1;
  }
  .user-qualifications {
    cursor: default;
    .qualification-item {
      max-width: 338px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: 500;
      color: #999;
    }
  }
}
</style>
