const jwt = require("jsonwebtoken");
const { PlatformUser } = require("../models/PlatformUser");
const { MaintenanceUser } = require("../models/MaintenanceUser");
const { RepairUser } = require("../models/RepairUser");

// 保护路由
exports.protect = async (req, res, next) => {
  let token;

  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer")
  ) {
    // 从请求头中获取token
    token = req.headers.authorization.split(" ")[1];
  }

  // 检查token是否存在
  if (!token) {
    return res.status(401).json({
      success: false,
      message: "无权限访问此路由",
    });
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    let UserModel;
    switch (decoded.userType) {
      case "PLATFORM":
        UserModel = PlatformUser;
        break;
      case "MAINTENANCE_UNIT":
        UserModel = MaintenanceUser;
        break;
      case "REPAIR_UNIT":
        UserModel = RepairUser;
        break;
    }

    req.user = await UserModel.findById(decoded.id);
    next();
  } catch (err) {
    return res.status(401).json({
      success: false,
      message: "无权限访问此路由",
    });
  }
};
