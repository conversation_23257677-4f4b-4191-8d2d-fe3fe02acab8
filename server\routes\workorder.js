const express = require("express");
const router = express.Router();
const workOrderController = require("../controllers/workorder");
const { protect } = require("../middleware/auth");
const upload = require("../middleware/upload");

// 获取工单列表
router.get("/work-order-list", protect, workOrderController.getWorkOrderList);

// 报修管理员获取工单列表
router.get(
  "/work-order-list-by-repair-manager",
  protect,
  workOrderController.getWorkOrderListByRepairManager
);

// 获取工单详情
router.get(
  "/work-order-detail",
  protect,
  workOrderController.getWorkOrderDetail
);

// 创建工单
router.post("/create-work-order", protect, workOrderController.createWorkOrder);

// 编辑工单
router.put("/edit-work-order", protect, workOrderController.editWorkOrder);

// 上传附件
router.post(
  "/upload-work-order-attachment",
  protect,
  upload.single("file"),
  workOrderController.uploadWorkOrderAttachment
);

// 派单/转派
router.post("/dispatch", protect, workOrderController.dispatchWorkOrder);

// 更新工单状态
router.put("/:id/status", protect, workOrderController.updateWorkOrderStatus);

// 获取工单数量
router.get("/work-order-count", protect, workOrderController.getWorkOrderCount);

// 工单无需维修
router.post("/maintenance-free", protect, workOrderController.MaintenanceFree);

// 确认到达
router.post("/confirm-arrival", protect, workOrderController.confirmArrival);

// 处理与报价
router.post("/process-and-price", protect, workOrderController.processAndPrice);

// 撤回报价
router.post(
  "/cancel-process-and-price",
  protect,
  workOrderController.cancelProcessAndPrice
);

// 平台审核方案
router.post("/platform-audit", protect, workOrderController.platformAudit);

// 报修方确认维修方案
router.post("/repair-confirm", protect, workOrderController.repairConfirm);

// 退单
router.post("/return-order", protect, workOrderController.returnOrder);

// 完成维修
router.post("/complete-repair", protect, workOrderController.completeRepair);

// 报修方确认完成
router.post(
  "/repair-finish-confirm",
  protect,
  workOrderController.repairFinishConfirm
);

// 平台确认完成
router.post(
  "/platform-finish-confirm",
  protect,
  workOrderController.platformFinishConfirm
);

// 取消工单
router.post("/cancel-work-order", protect, workOrderController.cancelWorkOrder);

// 选择报修管理人确认方案
router.post(
  "/select-repair-manager-confirm",
  protect,
  workOrderController.selectRepairManagerConfirm
);

// 选择确认完成报修管理人
router.post(
  "/select-finish-manager-confirm",
  protect,
  workOrderController.selectFinishManagerConfirm
);

// 获取报修方价格
router.post("/get-repair-price", protect, workOrderController.getRepairPrice);

module.exports = router;
