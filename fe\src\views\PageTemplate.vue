<template>
  <div class="page-container">
    <el-card class="page-card">
      <template #header>
        <div class="page-header">
          <div class="page-title">{{ title }}</div>
        </div>
      </template>
      <div class="page-content">
        <div class="placeholder-container">
          <el-empty :description="placeholder" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  placeholder: {
    type: String,
    default: "该页面正在开发中...",
  },
});
</script>

<style scoped>
.page-container {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
}

.page-card {
  height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
}

.page-content {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
}

.placeholder-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
