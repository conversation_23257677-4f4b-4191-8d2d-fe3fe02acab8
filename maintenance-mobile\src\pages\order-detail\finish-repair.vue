<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar title="完成维修" :back-url="`/pages/order-detail/index?workOrderId=${workOrderId}`" />

    <!-- 页面内容 -->
    <view class="page-content-with-nav">
      <!-- 表单内容 -->

      <view class="form-section">
        <view class="form-item">
          <view class="label"><span class="required">*</span>上传图片/视频</view>
          <view class="section-hint">请注意上传的数据中是否包含敏感信息</view>

          <view class="upload-area">
            <view class="upload-list">
              <view class="image-item" v-for="(item, index) in showAttachment" :key="index">
                <template v-if="isImage(item.fileType)">
                  <BlobImage
                    v-if="item.status === 'success'"
                    style="width: 200rpx; height: 200rpx"
                    :file-id="item.fileId" />
                  <view v-else-if="item.status !== 'success'" class="mask">{{ item.message }}</view>
                </template>
                <template v-else-if="isVideo(item.fileType)">
                  <BlobVideo style="width: 200rpx; height: 200rpx" :file-id="item.fileId" />
                  <view v-if="item.status !== 'success'" class="mask">{{ item.message }}</view>
                </template>
                <view class="file-name">{{ item.fileName }}</view>
                <text class="delete-icon" @click.stop="deleteImage(item)">×</text>
              </view>
              <view class="upload-item" @click="chooseAttachment">
                <text class="upload-icon">+</text>
              </view>
            </view>
          </view>
        </view>
        <view class="form-item">
          <view class="label"><span class="required">*</span>备注</view>
          <textarea
            class="textarea"
            v-model="repairResult"
            placeholder="请输入备注"
            maxlength="500"
            auto-height
            @input="updateOrderStore"></textarea>
        </view>

        <view class="form-item">
          <view class="label"><span class="required">*</span>维修完成签名</view>
          <view class="signature-area" @click="goToSignature" v-if="!signatureBase64">
            <text class="signature-placeholder">点击进行签名</text>
          </view>
          <view class="signature-preview" v-else>
            <image class="signature-image" :src="signatureBase64" mode="aspectFit"></image>
            <view class="delete-icon" @click="clearSignature">
              <text class="iconfont icon-close">X</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="fixed-bottom">
        <view class="btn btn-primary" @click="submitForm">提交</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import { NavBar, BlobImage, BlobVideo } from "@/components"
import { showToast, showLoading, hideLoading, navigateBack } from "@/utils"
import { completeRepair, uploadFile, deleteFile } from "@/api"
import { useUserStore, useSystemStore, useWorkOrderDetailStore } from "@/store"

// 存储
const userStore = useUserStore()
const systemStore = useSystemStore()
const workOrderDetailStore = useWorkOrderDetailStore()

// 页面数据
const workOrderId = ref("")
const repairResult = ref("")
const signatureBase64 = ref("")

// 页面加载
onLoad(option => {
  if (option?.workOrderId) {
    workOrderId.value = option.workOrderId
  }

  if (!systemStore.haveOtherPages()) recoverOrderStore()
  else updateOrderStore()
})

function recoverOrderStore() {
  showAttachment.value = workOrderDetailStore.finishAttachments
  repairResult.value = workOrderDetailStore.finishDesc
  signatureBase64.value = workOrderDetailStore.signatureBase64
}

function updateOrderStore() {
  console.log("更新维修方案数据")
  workOrderDetailStore.setSpecifiedInfo({
    finishAttachments: showAttachment.value.filter(item => item.status === "success"),
    finishDesc: repairResult.value
  })
}

watch(
  [() => workOrderDetailStore.signatureConfirm, () => workOrderDetailStore.signatureBase64],
  val => {
    if (workOrderDetailStore.signatureConfirm && workOrderDetailStore.signatureBase64) {
      console.log(workOrderDetailStore.signatureBase64)
      signatureBase64.value = workOrderDetailStore.signatureBase64
    }
  },
  { immediate: true }
)

/* ======================================= 附件 ======================================= */
const showAttachment = ref<any[]>([])
function isImage(fileType: string) {
  return fileType.startsWith("image/")
}

function isVideo(fileType: string) {
  return fileType.startsWith("video/")
}

function chooseAttachment() {
  // 检查已上传数量
  if (showAttachment.value.length >= 9) {
    showToast("最多只能上传9个文件")
    return
  }

  uni.showActionSheet({
    itemList: ["上传图片", "上传视频"],
    title: "",
    success: res => {
      if (res.tapIndex === 0) {
        chooseImage()
      } else if (res.tapIndex === 1) {
        chooseVideo()
      }
    }
  })
}
// 选择图片或视频
const chooseImage = () => {
  // 计算还可以上传的数量
  const remainingCount = 9 - showAttachment.value.length
  if (remainingCount <= 0) {
    showToast("最多只能上传9个文件")
    return
  }

  uni.chooseImage({
    count: Math.min(5, remainingCount), // 限制每次最多选择5张，且不超过剩余可上传数量
    success: res => {
      const tempFiles = Array.isArray(res.tempFiles) ? res.tempFiles : [res.tempFiles]
      tempFiles.forEach(file => {
        // 检查文件大小（10M = 10 * 1024 * 1024 bytes）
        if (file.size > 10 * 1024 * 1024) {
          showToast("文件大小不能超过10M")
          return
        }
        // #ifdef APP
        file.type = "image/png"
        file.name = `附件${new Date().getTime()}.png`
        // #endif
        let data = {
          tempPath: file.path,
          fileType: file.type,
          fileName: file.name,
          fileId: "",
          status: "uploading",
          message: "上传中..."
        }

        showAttachment.value.push(data)
        uploadAttachment(file)
      })
    },
    fail: () => {
      showToast("选择图片失败")
    }
  })
}

const chooseVideo = () => {
  // 检查已上传数量
  if (showAttachment.value.length >= 9) {
    showToast("最多只能上传9个文件")
    return
  }

  uni.chooseVideo({
    count: 1,
    success: res => {
      let file = res.tempFile
      file.path = res.tempFilePath
      // #ifdef APP
      file = {
        path: res.tempFilePath,
        type: "video/mp4",
        name: `附件${new Date().getTime()}.mp4`,
        size: res.size
      }
      file.path = res.tempFilePath
      // #endif
      if (file.size > 10 * 1024 * 1024) {
        showToast("文件大小不能超过10M")
        return
      }
      let data = {
        tempPath: file.path,
        fileType: file.type,
        fileName: file.name,
        fileId: "",
        status: "uploading",
        message: "上传中..."
      }

      showAttachment.value.push(data)
      uploadAttachment(file)
    },
    fail: () => {
      showToast("选择视频失败")
    }
  })
}
// 上传附件
const uploadAttachment = file => {
  uploadFile(file.path)
    .then(res => {
      showAttachment.value.forEach(item => {
        if (item.tempPath === file.path) {
          item.fileId = res.fileId
          item.status = "success"
          item.message = ""
        }
      })
      updateOrderStore()
    })
    .catch(err => {
      showToast(err.message || err.errMsg)
      showAttachment.value.forEach(item => {
        if (item.tempPath === file.path) {
          item.status = "error"
          item.message = err.message || err.errMsg
        }
      })
    })
}

// 删除图片
const deleteImage = async file => {
  try {
    if (file.fileId && file.tempPath) {
      await deleteFile(file.fileId)
    }
    showAttachment.value = showAttachment.value.filter(
      item => (!item.fileId && item.tempPath !== file.tempPath) || (item.fileId && item.fileId !== file.fileId)
    )
    updateOrderStore()
  } catch (error: any) {
    showToast(error.message)
  }
}

// 前往签名页面
const goToSignature = () => {
  uni.navigateTo({
    url: "/pages/signature-draw/signature-draw"
  })
  workOrderDetailStore.setSpecifiedInfo({
    signatureFrom: `/pages/order-detail/finish-repair?workOrderId=${workOrderId.value}`
  })
}

// 清除签名
const clearSignature = () => {
  signatureBase64.value = ""
}

// 提交表单
const submitForm = async () => {
  const uploadingFile = showAttachment.value.filter(file => file.status === "uploading")
  if (uploadingFile.length > 0) return showToast("文件上传中，请等待文件上传后提交")
  if (!repairResult.value) {
    return showToast("请输入备注")
  }

  if (!signatureBase64.value) {
    return showToast("请完成签名确认")
  }

  try {
    const attachments = showAttachment.value
      .filter(item => item.status === "success")
      .map(item => {
        return {
          fileName: item.fileName,
          fileId: item.fileId,
          fileType: item.fileType
        }
      })

    if (attachments.length === 0) {
      return showToast("请上传图片或视频")
    }

    showLoading("提交中...")

    await completeRepair({
      workOrderId: workOrderId.value,
      maintenanceUnitId: userStore.unit,
      remark: repairResult.value,
      signatureBase64: signatureBase64.value,
      attachments: attachments
    })

    hideLoading()
    showToast("提交成功")
    workOrderDetailStore.setSpecifiedInfo({
      signatureBase64: "",
      signatureConfirm: false,
      signatureFrom: ""
    })

    // 触发刷新工单详情事件
    uni.$emit("refreshOrderDetail")
    uni.$emit("repairOrderListRefresh")

    // 返回上一页
    navigateBack(`/pages/order-detail/index?workOrderId=${workOrderId.value}`)
  } catch (error: any) {
    hideLoading()
    showToast(error.message || "提交失败")
  }
}
</script>

<style lang="scss" scoped>
.page-content-with-nav {
  padding: 20rpx;
  background-color: #f5f5f5;
  height: calc(100vh - var(--window-top));
  box-sizing: border-box;
}

.form-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 100rpx;
}

.form-item {
  margin-bottom: 30rpx;
  .section-hint {
    font-size: 26rpx;
    color: #aaa;
    margin-top: 10rpx;
  }

  .label {
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    .required {
      color: #f56c6c;
      margin-right: 10rpx;
    }
  }

  .textarea {
    width: 100%;
    min-height: 180rpx;
    border: 1px solid #eee;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    box-sizing: border-box;
    background-color: #f9f9f9;
  }
}

.upload-area {
  .upload-list {
    display: flex;
    flex-wrap: wrap;

    .upload-item {
      width: 200rpx;
      height: 200rpx;
      border-radius: 12rpx;
      border: 2rpx dashed #ddd;
    }
    .image-item {
      width: 200rpx;
      margin-right: 20rpx;
      margin-bottom: 20rpx;
      border-radius: 8rpx;
      position: relative;
      .file-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        font-size: 28rpx;
        color: #999;
        text-align: center;
        margin-top: 10rpx;
      }
      .mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        color: #fff;
        z-index: 1;
      }
    }

    .upload-item {
      border: 2rpx dashed #ddd;
      display: flex;
      justify-content: center;
      align-items: center;

      .upload-icon {
        font-size: 60rpx;
        color: #999;
      }
    }

    .image-item {
      image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }

      .delete-icon {
        position: absolute;
        top: -15rpx;
        right: -15rpx;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        z-index: 2;
      }
    }
  }
}

.signature-area {
  width: 100%;
  height: 200rpx;
  border: 1px dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;

  .signature-placeholder {
    font-size: 28rpx;
    color: #999;
  }
}

.signature-preview {
  position: relative;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 20rpx;
  background-color: #f9f9f9;

  .signature-image {
    width: 100%;
    max-height: 200rpx;
    object-fit: contain;
  }

  .delete-icon {
    position: absolute;
    top: 10rpx;
    right: 10rpx;
    width: 40rpx;
    height: 40rpx;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 24rpx;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;

  .btn {
    width: 100%;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30rpx;

    &.btn-primary {
      background-color: $uni-color-primary;
      color: #fff;
    }
  }
}
</style>
