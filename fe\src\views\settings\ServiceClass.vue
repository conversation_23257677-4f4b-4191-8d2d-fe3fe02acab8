<template>
  <div class="service-class-root">
    <div class="service-class-header">
      <div class="service-class-header-title">
        <span>服务内容项目配置</span>
      </div>
      <div class="service-class-header-action">
        <el-button
          type="primary"
          :icon="Plus"
          @click="(e) => handleChangeServiceItem('add', null)"
        >
          新建服务项目
        </el-button>
      </div>
    </div>
    <div class="service-class-container">
      <div class="service-class-content">
        <div class="service-class-sidebar">
          <div class="sidebar-header">
            <span>全部服务类型</span>
            <el-button
              type="primary"
              link
              @click="(e) => handleChangeServiceClass('add', null)"
            >
              + 新增类型
            </el-button>
          </div>
          <div class="sidebar-content">
            <template v-if="serviceClassList.length > 0">
              <div
                v-for="(item, index) in serviceClassList"
                :key="index"
                class="sidebar-item-container"
                :class="[
                  'sidebar-item',
                  currentServiceClass?.id === item.id ? 'active' : '',
                ]"
                @click="handleSelectServiceClass(item)"
              >
                <OverflowTooltip
                  :content="item.serviceClass"
                  style="margin-right: 6px"
                />
                <el-dropdown>
                  <el-icon
                    :color="
                      currentServiceClass?.id === item.id ? '#1890ff' : 'grey'
                    "
                  >
                    <MoreFilled />
                  </el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        @click.stop="
                          (e) => handleChangeServiceClass('edit', item)
                        "
                      >
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item
                        @click.stop="(e) => handleDeleteServiceClass(item)"
                      >
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
            <EmptyContent v-else />
          </div>
        </div>
        <div class="service-class-table-container">
          <template v-if="currentServiceClass">
            <div class="service-class-search">
              <SearchContainer
                @queryBtnClick="handleSearch"
                @resetBtnClick="handleReset"
              >
                <el-form-item label="服务项目">
                  <el-input
                    v-model.trim="searchForm.serviceItem"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
              </SearchContainer>
            </div>

            <CommonTable
              ref="tableRef"
              class="service-class-table"
              :columns="serviceClassColumns"
              :requestApi="getServiceItemApi"
              :requestParams="requestParams"
              :dataCallback="dataCallback"
            >
              <template #operations="{ row }">
                <el-button
                  link
                  type="primary"
                  @click="(e) => handleChangeServiceItem('edit', row)"
                >
                  查看
                </el-button>
                <el-button link type="danger" @click="handleDelete(row)">
                  删除
                </el-button>
              </template>
            </CommonTable>
          </template>
          <EmptyContent v-else desc="请先在左侧选择服务类型" />
        </div>
      </div>
    </div>

    <!-- 新增/编辑服务项目弹窗 -->
    <CommonDialog
      v-model:visible="serviceItemDialog.visible"
      :title="
        serviceItemDialog.type === 'edit' ? '编辑服务项目' : '新增服务项目'
      "
      :confirm-callback="submitServiceItem"
      :btn-loading="submitLoading"
    >
      <el-form
        class="service-item-form"
        ref="serviceItemFormRef"
        :model="serviceItemForm"
        :rules="serviceItemRules"
        label-width="110px"
      >
        <el-form-item label="服务类型" prop="serviceClass">
          <el-select
            v-model="serviceItemForm.serviceClass"
            placeholder="请选择服务类型"
          >
            <el-option
              v-for="item in serviceClassList"
              :key="item.serviceClass"
              :label="item.serviceClass"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="服务项目" prop="serviceItem">
          <el-input
            v-model.trim="serviceItemForm.serviceItem"
            placeholder="请输入服务项目名称"
          />
        </el-form-item>
        <el-form-item label="服务项目编号" prop="code">
          <el-input
            v-model.trim="serviceItemForm.code"
            placeholder="请输入服务项目编号"
          >
            <template #prepend>{{
              serviceItemForm.serviceClass + "_"
            }}</template>
          </el-input>
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input
            v-model.trim="serviceItemForm.unit"
            placeholder="请输入单位"
          />
        </el-form-item>
        <el-form-item label="售价" prop="unitPrice">
          <el-input-number
            v-model="serviceItemForm.unitPrice"
            :precision="2"
            :min="0"
            :step="0.01"
            controls-position="right"
            placeholder="请输入售价"
            style="width: 100%; text-align: left"
          />
        </el-form-item>
        <el-form-item label="成本最高限价" prop="maxPrice">
          <el-input-number
            v-model="serviceItemForm.maxPrice"
            :precision="2"
            :min="0"
            :step="0.01"
            controls-position="right"
            placeholder="请输入成本最高限价"
            style="width: 100%; text-align: left"
          />
        </el-form-item>
        <el-form-item label="税率" prop="taxRate">
          <el-input-number
            v-model="serviceItemForm.taxRate"
            :precision="2"
            :min="0"
            :step="0.01"
            controls-position="right"
            placeholder="请输入税率"
            style="width: 100%; text-align: left"
          >
            <template #suffix>
              <span>%</span>
            </template></el-input-number
          >
        </el-form-item>
      </el-form>
    </CommonDialog>

    <!-- 新增/编辑服务类型弹窗 -->
    <CommonDialog
      v-model:visible="serviceClassDialog.visible"
      :title="
        serviceClassDialog.type === 'add' ? '新增服务类型' : '编辑服务类型'
      "
      :confirm-callback="submitServiceClass"
      :btn-loading="submitClassLoading"
    >
      <el-form
        ref="serviceClassFormRef"
        :model="serviceClassForm"
        :rules="serviceClassRules"
        label-width="120px"
      >
        <el-form-item label="服务类型编号" prop="id">
          <el-input
            v-model.trim="serviceClassForm.id"
            :disabled="serviceClassDialog.type === 'edit'"
            placeholder="请输入服务类型编号"
          />
        </el-form-item>
        <el-form-item label="服务类型" prop="serviceClass">
          <el-input
            v-model.trim="serviceClassForm.serviceClass"
            placeholder="请输入服务类型"
          />
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { Plus, MoreFilled } from "@element-plus/icons-vue";
import {
  SearchContainer,
  CommonTable,
  EmptyContent,
  CommonDialog,
  OverflowTooltip,
} from "@/base-components";
import { serviceClassColumns } from "./config";
import {
  getServiceContentApi,
  getServiceItemApi,
  addServiceClassApi,
  editServiceClassApi,
  deleteServiceClassApi,
  addServiceItemApi,
  editServiceItemApi,
  deleteServiceItemApi,
} from "@/api";
import { Message, SystemPrompt, toastError } from "@/utils";
import { cloneDeep, max } from "lodash";

/* ====================================== 服务类别 ====================================== */
const serviceClassListLoading = ref(false);
const serviceClassList = ref([]);
// 当前选中的服务类型
const currentServiceClass = ref(null);

onMounted(() => {
  refreshServiceClassList();
});

function refreshServiceClassList() {
  serviceClassListLoading.value = true;
  getServiceContentApi()
    .then((res) => {
      serviceClassList.value = res.data.data || [];
      serviceClassListLoading.value = false;
    })
    .catch((err) => {
      toastError(err, "获取服务类别失败");
      serviceClassListLoading.value = false;
    });
}

/* ====================================== 服务类型操作 ====================================== */
const requestParams = reactive({ filters: "" });

// 服务类型表单
const serviceClassDialog = reactive({
  visible: false,
  type: "add",
});

const serviceClassForm = reactive({
  serviceClass: "",
  id: "",
});

const serviceClassRules = {
  serviceClass: [
    { required: true, message: "请输入服务类型", trigger: "blur" },
    { max: 20, message: "服务类型长度不能超过20个字符", trigger: "blur" },
  ],
  id: [
    { required: true, message: "请输入服务类型编号", trigger: "blur" },
    { max: 20, message: "服务类型编号长度不能超过20个字符", trigger: "blur" },
  ],
};

const serviceClassFormRef = ref();

// 新增/编辑服务类型
const handleChangeServiceClass = (type, row) => {
  serviceClassForm.serviceClass = row ? row.serviceClass : "";
  serviceClassForm.id = row ? row.id : "";
  serviceClassDialog.type = type;
  serviceClassDialog.visible = true;
  serviceClassFormRef.value?.clearValidate();
};

// 删除服务类型
const handleDeleteServiceClass = (row) => {
  SystemPrompt("是否删除该服务类型？", "warning").then(() => {
    deleteServiceClassApi(row.id)
      .then(() => {
        Message.success("删除成功");
        refreshServiceClassList();
      })
      .catch((err) => {
        toastError(err, "删除失败");
      });
  });
};

// 提交服务类型
const submitClassLoading = ref(false);
const submitServiceClass = () => {
  serviceClassFormRef.value.validate((valid) => {
    if (!valid) return;
    let requestApi =
      serviceClassDialog.type === "add"
        ? addServiceClassApi
        : editServiceClassApi;
    submitClassLoading.value = true;
    requestApi(serviceClassForm)
      .then((res) => {
        Message.success(
          `${serviceClassDialog.type === "add" ? "新增" : "编辑"}成功`
        );
        refreshServiceClassList();
        serviceClassDialog.visible = false;
        submitClassLoading.value = false;
      })
      .catch((err) => {
        submitClassLoading.value = false;
        toastError(
          err,
          `${serviceClassDialog.type === "add" ? "新增" : "编辑"}失败`
        );
      });
  });
};

/* ====================================== 服务项目 ====================================== */

// 搜索表单
const searchForm = reactive({
  serviceItem: "",
});

// 表格数据
const tableRef = ref();

function dataCallback(res) {
  const data = res.data?.data?.rows || [];
  return {
    tableRows: data,
    total: res.data?.data?.pageElements?.totalElements || 0,
  };
}

// 选择服务类型
const handleSelectServiceClass = (serviceClass) => {
  currentServiceClass.value = serviceClass;
  handleReset();
};

// 搜索
const handleSearch = () => {
  requestParams.filters = `serviceClass=${currentServiceClass.value.id},serviceItem=${searchForm.serviceItem}`;
};

// 重置
const handleReset = () => {
  searchForm.serviceItem = "";
  requestParams.filters = `serviceClass=${currentServiceClass.value.id}`;
};

/* ====================================== 服务项目操作 ====================================== */

// 服务项目表单
const serviceItemDialog = reactive({
  visible: false,
  type: "add",
});

const initFormServiceItem = {
  _id: "",
  serviceClass: "",
  serviceItem: "",
  code: "",
  unit: "",
  unitPrice: null,
  maxPrice: null,
  taxRate: null,
};
const serviceItemForm = ref(cloneDeep(initFormServiceItem));

const serviceItemRules = {
  serviceClass: [
    { required: true, message: "请选择服务类型", trigger: "change" },
  ],
  code: [{ required: true, message: "请输入服务项目编号", trigger: "blur" }],
  serviceItem: [{ required: true, message: "请输入服务项目", trigger: "blur" }],
  unit: [{ required: true, message: "请输入单位", trigger: "blur" }],
  unitPrice: [{ required: true, message: "请输入售价", trigger: "blur" }],
  maxPrice: [
    { required: true, message: "请输入成本最高限价", trigger: "blur" },
  ],
  taxRate: [{ required: true, message: "请输入税率", trigger: "blur" }],
};

const serviceItemFormRef = ref();
const submitLoading = ref(false);

// 新增/编辑服务类型
const handleChangeServiceItem = (type, row) => {
  if (row) {
    for (let key in serviceItemForm.value)
      if (key === "code") {
        // 去除第一个_和之前的字符串
        serviceItemForm.value[key] = row.code.split("_")[1];
      } else serviceItemForm.value[key] = row[key];
  } else {
    serviceItemForm.value = cloneDeep(initFormServiceItem);
    serviceItemForm.value.serviceClass = currentServiceClass.value.id;
  }
  serviceItemDialog.type = type;
  serviceItemDialog.visible = true;
};

// 删除服务项目
const handleDelete = (row) => {
  SystemPrompt("是否删除该服务项目？", "warning").then(() => {
    deleteServiceItemApi(row._id)
      .then(() => {
        Message.success("删除成功");
        tableRef.value.refreshTableData();
      })
      .catch((err) => {
        toastError(err, "删除失败");
      });
  });
};

// 提交服务项目
const submitServiceItem = () => {
  serviceItemFormRef.value.validate((valid) => {
    if (!valid) return;

    submitLoading.value = true;

    // 准备提交的数据
    const submitData = {
      serviceClass: serviceItemForm.value.serviceClass,
      serviceItem: serviceItemForm.value.serviceItem,
      unit: serviceItemForm.value.unit,
      unitPrice: serviceItemForm.value.unitPrice,
      maxPrice: serviceItemForm.value.maxPrice,
      taxRate: serviceItemForm.value.taxRate,
      code:
        serviceItemForm.value.serviceClass + "_" + serviceItemForm.value.code,
    };
    if (serviceItemDialog.type === "edit")
      submitData._id = serviceItemForm.value._id;

    // 根据是新增还是编辑选择不同的API
    const requestApi =
      serviceItemDialog.type === "edit"
        ? editServiceItemApi
        : addServiceItemApi;

    requestApi(submitData)
      .then((res) => {
        Message.success(
          serviceItemDialog.type === "edit" ? "编辑成功" : "新增成功"
        );

        // 刷新表格数据
        tableRef.value.refreshTableData();
        serviceItemDialog.visible = false;
        submitLoading.value = false;
      })
      .catch((err) => {
        submitLoading.value = false;
        toastError(
          err,
          serviceItemDialog.type === "edit" ? "编辑失败" : "新增失败"
        );
      });
  });
};
</script>

<style lang="less" scoped>
.service-class-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .service-class-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;
  }

  .service-class-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .service-class-content {
      flex: 1;
      min-height: 0px;
      display: flex;
      gap: 20px;

      .service-class-sidebar {
        width: 300px;
        background-color: #f5f5f5;
        border-radius: 10px;
        display: flex;
        flex-direction: column;

        .sidebar-header {
          padding: 15px;
          border-bottom: 1px solid #e0e0e0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: bold;
        }

        .sidebar-content {
          flex: 1;
          overflow-y: auto;
          .sidebar-item-container {
            display: flex;
          }

          .sidebar-item {
            padding: 12px 15px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;

            &:hover {
              background-color: #e8e8e8;
            }

            &.active {
              background-color: #e6f7ff;
              color: #1890ff;
              font-weight: bold;

              &:before {
                content: "";
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 3px;
                background-color: #1890ff;
              }
            }
          }
        }
      }

      .service-class-table-container {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        .service-class-search {
          flex-shrink: 0;
          background: #f5f5f5;
          border-radius: 10px;
          padding: 0px 20px;
          box-sizing: border-box;
        }
        .service-class-table {
          flex: 1;
          min-height: 0;
          margin-top: 10px;
        }
      }
    }
  }
}

.service-item-form {
  :deep(.el-input-number) {
    .el-input__inner {
      text-align: left;
    }
  }
}
</style>
