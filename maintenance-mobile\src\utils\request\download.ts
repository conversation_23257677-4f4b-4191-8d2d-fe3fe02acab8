import { BASE_URL, BASE_URL_PREFIX } from "@/configs"
import { useUserStore } from "@/store/user"

export const downloadStaticFile = async (url: string) => {
  let requestUrl = BASE_URL + BASE_URL_PREFIX + url
  // #ifdef H5
  requestUrl = BASE_URL_PREFIX + url
  // #endif
  console.log(requestUrl)
  return new Promise<string>((resolve, reject) => {
    uni.downloadFile({
      url: requestUrl,
      header: {
        'Authorization': `Bearer ${useUserStore().token}`
      },
      success: res => {
		  console.log(res)
        resolve(res.tempFilePath)
      },
      fail: error => {
        reject(error)
      }
    })
  })
}
