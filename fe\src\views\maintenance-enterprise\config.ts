import { TableColumnItem } from "@/types";

export const admissionColumns: Array<TableColumnItem> = [
  { prop: "id", label: "单位编号", minWidth: 130 },
  { prop: "companyName", label: "单位名称", minWidth: 200 },
  { prop: "contactPerson", label: "联系人", minWidth: 120 },
  { prop: "contactPhone", label: "联系电话", minWidth: 150 },
  { prop: "operationAreaLabel", label: "作业区域", minWidth: 150 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "memberCount", label: "成员人数", minWidth: 100 },
  { prop: "operations", label: "操作", minWidth: 240, fixed: "right" },
];

// 新增人员管理列配置
export const staffColumns: Array<TableColumnItem> = [
  { prop: "userId", label: "工号", minWidth: 130 },
  { prop: "username", label: "人员姓名", minWidth: 120 },
  { prop: "unit", label: "所属单位", minWidth: 150 },
  { prop: "phone", label: "联系电话", minWidth: 150 },
  { prop: "roles", label: "角色", minWidth: 180 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "createdAt", label: "添加时间", minWidth: 180 },
  { prop: "operations", label: "操作", minWidth: 120, fixed: "right" },
];
