<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar title="确认完成维修" :backUrl="`/pages/repair-order/detail/index?workOrderId=${workOrderId}`" />

    <!-- 页面内容 -->
    <view class="page-content-with-nav finish-rate">
      <!-- 表单内容 -->
      <view class="rate-form">
        <!-- 设备状态选择 -->
        <view class="form-item">
          <view class="form-label"><span class="required">*</span>维修后设备是否正常工作</view>
          <view class="radio-group">
            <radio-group @change="changeRepairResult" style="display: flex; flex-direction: row">
              <label class="radio-item" style="margin-right: 30rpx">
                <radio value="是" :checked="repairResult === '是'" />
                <text>是</text>
              </label>
              <label class="radio-item">
                <radio value="否" :checked="repairResult === '否'" />
                <text>否</text>
              </label>
            </radio-group>
          </view>
        </view>

        <!-- 评分 -->
        <view class="form-item">
          <view class="form-label"><span class="required">*</span>评分</view>
          <view class="star-rating">
            <view v-for="i in 5" :key="i" class="star" :class="{ active: i <= rateScore }" @click="changeScore(i)">
              ★</view
            >
            <text class="score-text" v-if="rateScore">{{ rateScore }}分</text>
          </view>
        </view>

        <!-- 评价内容 -->
        <view class="form-item">
          <view class="form-label">内容</view>
          <textarea
            v-model="rateComment"
            placeholder="请输入"
            class="content-textarea"
            @input="updateOrderDetailStore" />
        </view>

        <!-- 签名区域 -->
        <view class="form-item">
          <view class="form-label"><span class="required">*</span>签名</view>
          <view v-if="!signatureBase64" class="signature-box" @click="goToSignature">
            <text>点击进行签名</text>
          </view>
          <image v-else :src="signatureBase64" mode="aspectFit" class="signature-box" @click="goToSignature" />
        </view>

        <!-- 提交按钮 -->
        <view class="submit-btn" @click="submitRate">提交</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from "vue"
import { onLoad, onUnload } from "@dcloudio/uni-app"
import { NavBar } from "@/components"
import { repairFinishConfirmApi } from "@/api"
import { showToast, showLoading, hideLoading, navigateBack } from "@/utils"
import { useSystemStore, useOrderDetailStore } from "@/store"

const systemStore = useSystemStore()
const orderDetailStore = useOrderDetailStore()

// 工单信息
const workOrderId = ref("")
const reportUnitId = ref("")

// 评价信息
const repairResult = ref("")
const rateScore = ref(5) // 默认5分
const rateComment = ref("")
const signatureBase64 = ref("")

onLoad(options => {
  workOrderId.value = options.workOrderId
  reportUnitId.value = options.reportUnitId

  // 如果前面没有页面栈，则恢复store的缓存数据
  if (!systemStore.haveOtherPages()) recoverOrderDetailStoreInfo()
  // 有的话则更新store
  else updateOrderDetailStore()
})

// 恢复store的缓存数据
function recoverOrderDetailStoreInfo() {
  const data = orderDetailStore.finishRepair
  repairResult.value = data.repairResult || ""
  rateScore.value = data.rateScore || ""
  rateComment.value = data.rateComment || ""
  signatureBase64.value = data.signatureBase64 || ""
}

// 更新store
function updateOrderDetailStore() {
  const data = {
    rateScore: rateScore.value,
    rateComment: rateComment.value,
    repairResult: repairResult.value,
    signatureBase64: signatureBase64.value
  }
  orderDetailStore.setFinishRepair(data)
}

function changeRepairResult(e) {
  repairResult.value = e.detail.value
  updateOrderDetailStore()
}

function changeScore(score) {
  rateScore.value = score
  updateOrderDetailStore()
}

// 处理签名确认
const handleSignatureConfirm = data => {
  signatureBase64.value = data

  // 设置签名的时候其他数据不变，只更改签名数据
  const detailInfo = {
    ...orderDetailStore.finishRepair,
    signatureBase64: data
  }
  orderDetailStore.setFinishRepair(detailInfo)
  showToast("签名已确认")
  orderDetailStore.clearSignatureInfo()
}

watch(
  () => orderDetailStore.signatureConfirm,
  val => {
    if (val) {
      handleSignatureConfirm(orderDetailStore.signatureBase64)
    }
  },
  {
    immediate: true
  }
)

// 跳转到签名页
const goToSignature = () => {
  orderDetailStore.setSignatureFrom()

  uni.navigateTo({
    url: "/pages/signature-draw/signature-draw?pageType=workOrderConfirm"
  })
}

// 提交评价
const submitRate = async () => {
  if (!repairResult.value) {
    return showToast("请选择维修设备是否正常工作")
  }

  if (!rateScore.value) {
    return showToast("请对服务进行评分")
  }

  if (!signatureBase64.value) {
    return showToast("请先完成签名")
  }

  showLoading("提交中...", true)
  repairFinishConfirmApi({
    workOrderId: workOrderId.value,
    reportUnitId: reportUnitId.value,
    rateScore: rateScore.value,
    rateComment: rateComment.value,
    repairResult: repairResult.value,
    signatureBase64: signatureBase64.value
  })
    .then(res => {
      showToast("评价成功")
      // 使用uni.$emit触发工单详情刷新事件
      uni.$emit("workOrderDetailRefresh", { workOrderId: workOrderId.value })
      uni.$emit("repairOrderListRefresh")

      // 返回上一页
      navigateBack(`/pages/repair-order/detail/index?workOrderId=${workOrderId.value}`)
    })
    .catch(err => {
      showToast(err.message || "评价失败")
    })
    .finally(() => {
      hideLoading()
    })
}
</script>

<style lang="scss" scoped>
.finish-rate {
  background-color: #f5f5f5;
  height: calc(100% - var(--status-bar-height) - 88rpx);
  box-sizing: border-box;

  .rate-form {
    padding: 20rpx;

    .form-item {
      background-color: #fff;
      padding: 30rpx 20rpx;
      margin-bottom: 2rpx;

      .form-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
        .required {
          color: #f56c6c;
          margin-right: 10rpx;
        }
      }

      .radio-group {
        display: flex;

        .radio-item {
          margin-right: 40rpx;
          display: flex;
          align-items: center;

          text {
            margin-left: 8rpx;
          }
        }
      }

      .star-rating {
        display: flex;
        align-items: center;

        .star {
          font-size: 60rpx;
          color: #ddd;
          margin-right: 10rpx;

          &.active {
            color: #ffc107;
          }
        }

        .score-text {
          font-size: 28rpx;
          color: #666;
          margin-left: 10rpx;
        }
      }

      .content-textarea {
        width: 100%;
        height: 200rpx;
        background-color: #f8f8f8;
        padding: 20rpx;
        box-sizing: border-box;
        border-radius: 8rpx;
        font-size: 28rpx;
      }

      .signature-box {
        width: 100%;
        height: 200rpx;
        background-color: #f8f8f8;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #999;
        font-size: 30rpx;
        border-radius: 8rpx;
      }
    }

    .submit-btn {
      margin-top: 60rpx;
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      background-color: #4080ff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 45rpx;
    }
  }
}

.page-content-with-nav {
  height: calc(100% - var(--status-bar-height) - 88rpx);
  box-sizing: border-box;
}
</style>
