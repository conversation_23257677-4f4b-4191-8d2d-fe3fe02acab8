import { TableColumnItem } from "@/types";

export const workOrderColumns: Array<TableColumnItem> = [
  { prop: "workOrderId", label: "工单号", minWidth: 140 },
  { prop: "reportEnterprise", label: "报修单位", minWidth: 120 },
  { prop: "reporter", label: "报修人", minWidth: 120 },
  { prop: "faultDesc", label: "故障描述", minWidth: 180 },
  { prop: "deviceInfo", label: "机器信息", minWidth: 180 },
  { prop: "status", label: "工单状态", minWidth: 180 },
  { prop: "reportTime", label: "报修时间", minWidth: 180 },
  { prop: "maintenanceEnterprise", label: "维保单位", minWidth: 120 },
  { prop: "maintenanceUser", label: "维保人", minWidth: 120 },
  { prop: "evaluation", label: "评价", minWidth: 100 },
  { prop: "operations", label: "操作", minWidth: 120, fixed: "right" },
];
