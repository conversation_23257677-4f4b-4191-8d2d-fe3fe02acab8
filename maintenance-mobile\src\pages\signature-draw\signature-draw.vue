<template>
  <!-- <view class="container" :style="{ height: pageHeight }"> -->
  <view class="page-content-with-nav container">
    <NavBar
      :title="state.pageType === 'workOrderConfirm' ? '确认签名' : '签名'"
      :back-url="workOrderDetailStore.signatureFrom" />

    <view class="draw-container">
      <view class="toolbar" :style="{ width: `${TOOLBAR_WIDTH}rpx` }">
        <view class="button button--clean" @click="clean">清除</view>
        <view class="button button--submit" @click="confirmSign">完成</view>
      </view>

      <view class="canvas-wrapper">
        <canvas
          canvas-id="canvas"
          class="signature-canvas"
          :style="canvasStyle"
          :disable-scroll="true"
          @touchstart="drawStart"
          @touchmove="draw"
          @touchend="drawEnd"></canvas>
        <view v-show="state.placeholderShow" class="signature-placeholder">
          {{ state.pageType === "workOrderConfirm" ? "请签名确认维修方案" : "请在此绘制您的签名" }}
        </view>
      </view>
    </view>

    <canvas
      class="rotated-canvas"
      canvas-id="rotated-canvas"
      :style="{ width: rotatedCanvasWidth + 'px', height: rotatedCanvasHeight + 'px', background: '#fff' }"></canvas>
  </view>
</template>

<script setup lang="ts">
import { hideLoading, showLoading, showToast, sleep, navigateBack } from "@/utils"
import { onLoad, onReady, onResize } from "@dcloudio/uni-app"
import { getCurrentInstance, reactive, ref, computed, onMounted } from "vue"
import { useDisableGesture } from "@/hooks/useDisableGesture"
import { NavBar } from "@/components"
import { useWorkOrderDetailStore } from "@/store"
const workOrderDetailStore = useWorkOrderDetailStore()

useDisableGesture(0)

type Coordinate = {
  x: number
  y: number
}

const state = reactive({
  placeholderShow: true,
  canvasWidth: 375,
  canvasHeight: 750,
  pageType: "" // 页面来源
})

onLoad(options => {
  options?.pageType && (state.pageType = options.pageType)
})

/* ============== canvas 样式设置相关 ============== */

// 按钮工具栏的宽度，单位rpx
const TOOLBAR_WIDTH = 100
// 画布在页面上的展示样式
const canvasStyle = computed(() => {
  return {
    width: state.canvasWidth - uni.upx2px(TOOLBAR_WIDTH) + "px !important",
    height: state.canvasHeight + "px"
  }
})

let windowInfo: UniNamespace.GetWindowInfoResult
const pageHeight = ref("100vh")

onMounted(async () => {
  setCanvasSize()
  try {
    showLoading("加载中...")
    initCanvas()
    hideLoading()
  } catch (error: any) {
    hideLoading()
    navigateBack(workOrderDetailStore.signatureFrom)
  }
})

onResize(() => {
  let _windowInfo = uni.getWindowInfo()
  if (_windowInfo.windowHeight < _windowInfo.windowWidth) {
    uni.showModal({
      title: "提示",
      content: "请将设备锁定竖屏后使用签名板以获取最佳体验！",
      showCancel: false,
      success: () => {
        _windowInfo = uni.getWindowInfo()
        if (_windowInfo.windowHeight < _windowInfo.windowWidth) {
          navigateBack(workOrderDetailStore.signatureFrom)
        }
      }
    })
  }
})

const setCanvasSize = () => {
  // bug fix - 真机高度不正确，100vh会超出页面
  windowInfo = uni.getWindowInfo()
  pageHeight.value = windowInfo.windowHeight + "px"
  // 减去导航栏高度和额外的10px边距
  state.canvasHeight = windowInfo.windowHeight - 50 - 20
  state.canvasWidth = windowInfo.windowWidth
  rotatedCanvasWidth.value = state.canvasWidth
  rotatedCanvasHeight.value = windowInfo.windowHeight
}

/* ============== canvas ============== */

let canvasCtx: UniApp.CanvasContext
// 横向的canvas，作为传递后台数据的canvas
let rotatedCanvasCtx: UniApp.CanvasContext

const rotatedCanvasWidth = ref(0)
const rotatedCanvasHeight = ref(0)
let points: { x: number; y: number }[] = [] // 画布上绘制的点

const initCanvas = () => {
  canvasCtx = uni.createCanvasContext("canvas", getCurrentInstance())
  rotatedCanvasCtx = uni.createCanvasContext("rotated-canvas", getCurrentInstance())
  canvasCtx.setLineWidth(4)
  canvasCtx.setLineCap("round")
  canvasCtx.setLineJoin("miter")
  canvasCtx.setStrokeStyle("#000000")
}

const drawStart = (e: any) => {
  if (e.preventDefault) e.preventDefault()
  if (!e.changedTouches || e.changedTouches.length === 0) return

  state.placeholderShow = false
  const coord: Coordinate = {
    x: e.changedTouches[0].x,
    y: e.changedTouches[0].y
  }
  canvasCtx.beginPath()
  canvasCtx.moveTo(coord.x, coord.y)
  points.push({ ...coord })
}

const draw = (e: any) => {
  if (e.preventDefault) e.preventDefault()
  if (!e.changedTouches || e.changedTouches.length === 0) return

  const coord: Coordinate = {
    x: e.changedTouches[0].x,
    y: e.changedTouches[0].y
  }
  canvasCtx.lineTo(coord.x, coord.y)
  canvasCtx.stroke()
  canvasCtx.draw(true)
  canvasCtx.moveTo(coord.x, coord.y)
  points.push({ ...coord })
}

const drawEnd = e => {
  if (e.preventDefault) e.preventDefault()
  canvasCtx.closePath()
}

const clean = () => {
  state.placeholderShow = true
  canvasCtx.clearRect(0, 0, windowInfo.windowWidth, windowInfo.windowHeight)
  canvasCtx.draw(true)
  // 清空隐藏的canvas
  rotatedCanvasCtx?.clearRect(
    -rotatedCanvasHeight.value,
    -rotatedCanvasWidth.value,
    state.canvasWidth,
    state.canvasHeight
  )
  rotatedCanvasCtx?.draw(true)
  points = []
}

/* ============== upload ============== */

// 完成签名
const confirmSign = () => {
  if (points.length < 10) {
    return showToast("签名内容过少，请完成签名")
  }

  uni.showModal({
    title: "提示",
    content: "确认提交该签名吗？",
    success: async res => {
      if (!res.confirm) return
      generateRotatedSignature()
    }
  })
}

// 生成横屏的签名
const generateRotatedSignature = async () => {
  if (points.length === 0) {
    return showToast("请先完成签名")
  }

  showLoading("请稍候")
  try {
    // 获取适配区域
    const minX = points.reduce((min, point) => Math.min(min, point.x), points[0].x) - 10
    const maxX = points.reduce((max, point) => Math.max(max, point.x), points[0].x) + 10
    const minY = points.reduce((min, point) => Math.min(min, point.y), points[0].y) - 10
    const maxY = points.reduce((max, point) => Math.max(max, point.y), points[0].y) + 10
    const width = maxX - minX
    const height = maxY - minY
    rotatedCanvasWidth.value = height
    rotatedCanvasHeight.value = width

    const src = await getCanvasImageUrl({
      canvasId: "canvas",
      x: minX,
      y: minY,
      width: width,
      height: height
    })
    await sleep(1000)

    // 获取旋转后的图片
    rotatedCanvasCtx.translate(height / 2, width / 2) // 将绘图原点移动到中心，然后旋转90度
    rotatedCanvasCtx.rotate(-Math.PI / 2) // 逆时针90度
    rotatedCanvasCtx.drawImage(src, -width / 2, -height / 2)
    await canvasDraw(rotatedCanvasCtx, true)
    await sleep(1000)
    let rotatedImageUrl = await getCanvasImageUrl({
      canvasId: "rotated-canvas",
      height: width,
      width: height
    })

    // #ifdef MP-WEIXIN
    rotatedImageUrl = "data:image/png;base64," + uni.getFileSystemManager().readFileSync(rotatedImageUrl, "base64")
    // #endif

    handleWorkOrderConfirm(rotatedImageUrl)
  } catch (error) {
    console.error("生成签名出错:", error)
    showToast("生成签名失败，请重试")
  } finally {
    hideLoading()
  }
}

// 处理工单确认签名
const handleWorkOrderConfirm = (imageBase64: string) => {
  // 触发确认事件
  uni.$emit("signatureConfirm", { signatureBase64: imageBase64 })
  workOrderDetailStore.setSpecifiedInfo({
    signatureBase64: imageBase64,
    signatureConfirm: true
  })
  // 返回上一页
  navigateBack(workOrderDetailStore.signatureFrom)
}

/* ============= 其他 ============ */

// 获取canvas图片
const getCanvasImageUrl = (options: {
  canvasId: string
  x?: number
  y?: number
  width: number
  height: number
  destWidth?: number
  destHeight?: number
  quality?: number
}) => {
  return new Promise<string>((resolve, reject) => {
    const { canvasId, x = 0, y = 0, width, height, destWidth, destHeight, quality = 1 } = options
    uni.canvasToTempFilePath({
      canvasId: canvasId,
      x: x,
      y: y,
      width: width,
      height: height,
      destWidth: destWidth || width,
      destHeight: destHeight || height,
      quality: quality,
      success: async res => {
        // #ifdef APP
        const data = await filePathToBase64(res.tempFilePath)
        resolve(data as string)
        // #endif
        // #ifndef APP
        resolve(res.tempFilePath)
        // #endif
      },
      fail: err => {
        reject(err)
      }
    })
  })
}

function filePathToBase64(filePath: string) {
  return new Promise((resolve, reject) => {
    plus.io.resolveLocalFileSystemURL(
      filePath,
      entry => {
        entry.file(
          file => {
            const fileReader = new plus.io.FileReader()
            fileReader.onload = data => {
              resolve(data.target.result)
            }
            fileReader.onerror = error => {
              reject(error)
            }
            fileReader.readAsDataURL(file)
          },
          error => {
            reject(error)
          }
        )
      },
      error => {
        reject(error)
      }
    )
  })
}

// canvas绘制promise
const canvasDraw = (ctx: UniApp.CanvasContext, reserve: boolean = true) => {
  return new Promise<void>(resolve => {
    ctx.draw(reserve, res => {
      resolve()
    })
  })
}
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  touch-action: none;
  background-color: #f5f5f5;
  height: 100%;
}

.draw-container {
  position: relative;
  flex: 1;
  display: flex;
  margin: 10px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.canvas-wrapper {
  position: relative;
  flex: 1;
  background: #fff;
}

.signature-placeholder {
  position: absolute;
  top: 50%;
  left: 60%;
  transform: translate(-50%, -50%) rotate(90deg);
  font-size: 40rpx;
  white-space: nowrap;
  color: #999;
  opacity: 0.5;
  letter-spacing: 2rpx;
}

.toolbar {
  padding: 20rpx;
  border-right: 1px solid #e5e5e5;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.button {
  width: 160rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 32rpx;
  text-align: center;
  border-radius: 35rpx;
  transform: rotate(90deg);
  margin: 40rpx 0;

  &--clean {
    color: #0c7ffc;
    background: #e6f2ff;
    border: 1px solid #0c7ffc;
  }

  &--submit {
    color: #fff;
    background: #0c7ffc;
    box-shadow: 0 2px 8px rgba(12, 127, 252, 0.3);
  }
}

.rotated-canvas {
  position: absolute;
  top: 0;
  left: 100vw;
  z-index: -1;
  visibility: hidden;
}
</style>
