const express = require("express");
const router = express.Router();
const fileController = require("../controllers/file");
const { protect } = require("../middleware/auth");
const upload = require("../middleware/upload");

router.get("/get-file-data/:fileId", protect, fileController.getFileData);

router.delete("/delete-file", protect, fileController.deleteFile);

router.post(
  "/upload-file",
  protect,
  upload.single("file"),
  fileController.uploadFile
);

module.exports = router;
