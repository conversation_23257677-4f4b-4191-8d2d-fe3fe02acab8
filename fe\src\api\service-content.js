import axios from "./axios-instance";

// 获取服务类别
export function getServiceContentApi() {
  return axios.get("/api/service-content/service-class");
}

// 添加服务类别
export function addServiceClassApi(data) {
  return axios.post("/api/service-content/add-service-class", data, {
    headers: {
      checkSensitive: true,
    },
  });
}

// 编辑服务类别
export function editServiceClassApi(data) {
  return axios.put("/api/service-content/edit-service-class", data, {
    headers: {
      checkSensitive: true,
    },
  });
}

// 删除服务类别
export function deleteServiceClassApi(id) {
  return axios.delete(`/api/service-content/delete-service-class/${id}`);
}

// 获取所有服务项目
export function getServiceItemApi(params) {
  return axios.get("/api/service-content/service-items", {
    params,
  });
}

// 获取维保方指定合同的服务项目
export function getMaintenanceServiceItemApi(params) {
  return axios.get("/api/service-content/maintenance-service-items", {
    params,
  });
}

// 获取报修方当前生效的服务项目列表
export function getRepairServiceItemApi(params) {
  return axios.get("/api/service-content/repair-service-items", {
    params,
  });
}

// 添加服务项目
export function addServiceItemApi(data) {
  return axios.post("/api/service-content/add-service-item", data, {
    headers: {
      checkSensitive: true,
    },
  });
}

// 编辑服务项目
export function editServiceItemApi(data) {
  return axios.put("/api/service-content/edit-service-item", data, {
    headers: {
      checkSensitive: true,
    },
  });
}

// 删除服务项目
export function deleteServiceItemApi(code) {
  return axios.delete(`/api/service-content/delete-service-item/${code}`);
}

// 批量设置某个服务类别的所有服务项目的结算价
export function setServiceItemPriceApi(data) {
  return axios.post("/api/service-content/set-service-items-price", data);
}
