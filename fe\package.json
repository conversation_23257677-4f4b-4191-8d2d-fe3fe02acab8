{"name": "jishixiu-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build-image": "node scripts/build-image.cjs"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "chalk": "^4.1.2", "echarts": "^5.6.0", "element-plus": "^2.4.3", "lodash": "^4.17.21", "moment": "^2.30.1", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "shelljs": "^0.9.2", "vue": "^3.3.4", "vue-router": "^4.5.0", "vue3-dnd": "^2.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "less": "^4.2.2", "vite": "^4.4.5"}}