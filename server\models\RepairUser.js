const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { aesEncrypt, aesDecrypt } = require("../utils/encryption.js");

// 创建 RepairUser 鉴别器
const RepairUserSchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
      unique: true,
    },
    username: {
      type: String,
      required: true,
    },
    userType: {
      type: String,
      required: true,
    },
    gender: {
      type: String,
      default: "",
    },
    phone: {
      type: String,
      required: true,
      set: aesEncrypt,
      get: aesDecrypt,
    },
    roles: [
      {
        type: String,
      },
    ],
    status: {
      type: String,
      enum: ["ENABLED", "WAITING_AUDIT", "AUDIT_FAILED", "DISABLED"],
      default: "ENABLED",
    },
    lastLoginTime: {
      type: Number,
      default: () => new Date().getTime(),
    },
    password: {
      type: String,
      required: true,
    },
    unit: {
      type: String,
      required: true,
    },
  },
  { timestamps: true }
);

// 加密密码
RepairUserSchema.pre("save", async function (next) {
  if (!this.isModified("password")) {
    next();
  }
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
});

// 生成JWT Token
RepairUserSchema.methods.getSignedJwtToken = function () {
  return jwt.sign(
    { id: this._id, userType: this.userType },
    process.env.JWT_SECRET,
    {
      expiresIn: process.env.JWT_EXPIRE,
    }
  );
};

// 匹配用户输入的密码
RepairUserSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// 开启toJSON和toObject的getters选项,以确保在文档转换为JSON或普通对象时,
// 能正确调用Schema中定义的getter方法(如aesDecrypt)来解密敏感数据
RepairUserSchema.set("toJSON", { getters: true });
RepairUserSchema.set("toObject", { getters: true });

const RepairUser = mongoose.model("RepairUser", RepairUserSchema);

module.exports = { RepairUser, RepairUserSchema };
