import axios from "./axios-instance";

// 创建客户单位
export const createCustomerUnit = (data) => {
  return axios.post("/api/repair-enterprise/create-repair-enterprise", data, {
    headers: {
      checkSensitive: true,
    },
  });
};

// 编辑客户单位
export const editCustomerUnit = (data) => {
  return axios.put("/api/repair-enterprise/edit-repair-enterprise", data, {
    headers: {
      checkSensitive: true,
    },
  });
};

// 获取客户单位详情
export const getCustomerUnitDetail = (id) => {
  return axios.get(`/api/repair-enterprise/get-repair-enterprise-detail`, {
    params: {
      id,
    },
  });
};

// 修改客户单位状态
export const updateCustomerUnitStatus = (data) => {
  return axios.put(
    "/api/repair-enterprise/update-repair-enterprise-status",
    data
  );
};

// 获取客户单位列表
export const getCustomerUnitList = (params) => {
  return axios.get("/api/repair-enterprise/get-repair-enterprise-list", {
    params: params,
  });
};

// 获取报修人员列表
export const getCustomerUnitStaffList = (params) => {
  return axios.get("/api/repair-enterprise/get-repair-user-list", {
    params: params,
  });
};

// 添加报修人员
export const addCustomerUnitStaff = (data) => {
  return axios.post("/api/repair-enterprise/add-repair-user", data, {
    headers: {
      checkSensitive: true,
    },
  });
};

// 编辑报修人员
export const editCustomerUnitStaff = (data) => {
  return axios.put("/api/repair-enterprise/edit-repair-user", data, {
    headers: {
      checkSensitive: true,
    },
  });
};

// 修改报修人员状态
export const updateCustomerUnitStaffStatus = (data) => {
  return axios.put("/api/repair-enterprise/update-repair-user-status", data);
};

// 获取结算配置
export const getCustomerUnitConfig = (unitId) => {
  return axios.get("/api/repair-enterprise/get-repair-enterprise-config", {
    params: { unitId },
  });
};

// 新增结算配置
export const createCustomerUnitConfig = (data) => {
  return axios.post(
    "/api/repair-enterprise/create-repair-enterprise-config",
    data,
    {
      headers: {
        checkSensitive: true,
      },
    }
  );
};

// 设置包年配置项目
export const setCustomerUnitYearConfig = (data) => {
  return axios.post(
    "/api/repair-enterprise/set-repair-enterprise-year-config",
    data
  );
};

// 编辑结算配置
export const editCustomerUnitConfig = (data) => {
  return axios.put(
    "/api/repair-enterprise/edit-repair-enterprise-config",
    data
  );
};

// 删除结算配置
export const deleteCustomerUnitConfig = (data) => {
  return axios.delete("/api/repair-enterprise/delete-repair-enterprise-config");
};

// 生成邀请码
export const generateClientInviteCodeApi = (data) => {
  return axios.post("/api/repair-enterprise/generate-client-invite-code", data);
};
