<template>
  <view class="blob-video-container">
    <image
      style="width: 50rpx; height: 50rpx"
      src="@/static/common/video.svg"
      mode="aspectFit"
      @click="showPreview = true" />
    <view v-if="loading" class="mask">
      <view v-if="errorShow" class="mask-hint">{{ errorShow }}</view>
      <view v-else class="mask-hint">加载中</view>
    </view>

    <!-- 视频预览 -->
    <view
      v-if="showPreview"
      class="video-container"
      :style="{ top: systemStore.navBarHeight + 'px', height: `calc(100% - ${systemStore.navBarHeight}px)` }">
      <video :src="blobUrl" type="" controls style="width: 80%; height: 80%"></video>
      <image class="close-icon" src="@/static/common/close.svg" mode="aspectFit" @click="handleClose" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue"
import { getFileData } from "@/api"
import { downloadStaticFile } from "@/utils/request/download"
import { useSystemStore } from "@/store/system"

const systemStore = useSystemStore()

const props = withDefaults(
  defineProps<{
    fileId: string
    iconSize?: number
  }>(),
  { iconSize: 20 }
)

const blobUrl = ref("")
const loading = ref(false)
const errorShow = ref("")
const showPreview = ref(false)

watch(
  () => props.fileId,
  () => {
    if (!props.fileId) return (blobUrl.value = "")
    loading.value = true
    // #ifdef H5
    getFileData(props.fileId)
      .then(async res => {
        blobUrl.value = URL.createObjectURL(new Blob([res], { type: "video/mp4" }))
        loading.value = false
      })
      .catch(err => {
        errorShow.value = "加载失败"
        blobUrl.value = ""
      })
    //#endif

    // #ifndef H5
    downloadStaticFile(`/file/get-file-data/${props.fileId}`)
      .then(res => {
        blobUrl.value = res
        loading.value = false
      })
      .catch(err => {
        errorShow.value = "加载失败"
        blobUrl.value = ""
      })
    // #endif
  },
  {
    immediate: true
  }
)

function handleClose() {
  showPreview.value = false
}
</script>

<style lang="scss" scoped>
.blob-video-container {
  width: 100%;
  height: calc(100% - var(--status-bar-height));
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10rpx;
  box-sizing: border-box;
  border: 1rpx solid #ccc;
  position: relative;
}
.mask {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  .mask-hint {
    color: #fff;
  }
}
.video-container {
  width: 100%;
  position: fixed;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  .close-icon {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    cursor: pointer;
    width: 60rpx;
    height: 60rpx;
    z-index: 2;
  }
}
</style>
