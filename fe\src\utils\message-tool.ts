import { ElMessageBox, ElMessage } from "element-plus";
import type { AxiosError } from "axios";
import type { messageType } from "element-plus";

export function SystemPrompt(msg: string, type: messageType = "warning") {
  return ElMessageBox.confirm(msg, "系统提示", {
    type,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    closeOnClickModal: false,
    closeOnPressEscape: false,
  });
}

export function SystemAlert(msg: string, type: messageType = "error") {
  return ElMessageBox.alert(msg, "系统提示", {
    type,
    confirmButtonText: "知道了",
  });
}

// 从失败的网络请求中提取错误信息
export const extractErrorMsg = (
  err: AxiosError<any, any>,
  fallback = "操作失败"
) => {
  return (
    err.response?.data?.userMessage ||
    err.response?.data?.message ||
    err.message ||
    fallback
  );
};

// 从失败的网络请求中提取错误信息并展示
export const toastError = (
  err: AxiosError<any, any> | any,
  fallback = "操作失败"
) => {
  Message.error(extractErrorMsg(err, fallback));
};

export const Message = {
  success: function (msg: string) {
    ElMessage({
      message: msg,
      type: "success",
    });
  },
  warning: function (msg: string) {
    ElMessage({
      message: msg,
      type: "warning",
    });
  },
  error: function (msg: string) {
    ElMessage({
      message: msg,
      type: "error",
    });
  },
};
