<template>
  <div class="unit-root">
    <div class="unit-header">
      <div class="unit-header-title">
        <span>客户单位管理</span>
      </div>
      <div class="unit-header-action">
        <el-button
          type="primary"
          :icon="Plus"
          @click="$router.push('/customer/unit-detail')"
        >
          新建客户单位
        </el-button>
      </div>
    </div>
    <div class="unit-container">
      <div class="unit-search">
        <SearchContainer
          @queryBtnClick="handleSearch"
          @resetBtnClick="handleReset"
        >
          <el-form-item label="单位名称">
            <el-input
              v-model.trim="searchForm.companyName"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择"
              style="width: 100%"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="正常" :value="CommonStatusEnum.ENABLED" />
              <el-option label="已禁用" :value="CommonStatusEnum.DISABLED" />
            </el-select>
          </el-form-item>
        </SearchContainer>
      </div>
      <div class="unit-content">
        <CommonTable
          :columns="unitColumns"
          :requestApi="getCustomerUnitList"
          :requestParams="requestParams"
          :dataCallback="dataCallback"
        >
          <template #status="{ row }">
            <el-tag
              :type="
                row.status === CommonStatusEnum.ENABLED ? 'success' : 'danger'
              "
            >
              {{ row.status === CommonStatusEnum.ENABLED ? "正常" : "已禁用" }}
            </el-tag>
          </template>
          <template #operations="{ row }">
            <el-button link type="primary" @click="handleView(row)">
              查看
            </el-button>
            <el-button link type="primary" @click="handleStaffList(row)">
              人员台账
            </el-button>
            <el-button link type="primary" @click="handleSettlementConfig(row)">
              结算配置
            </el-button>
            <el-button
              v-if="row.status === CommonStatusEnum.ENABLED"
              link
              type="primary"
              @click="handleAddStaff(row)"
            >
              邀请员工注册
            </el-button>
          </template>
        </CommonTable>
      </div>
    </div>

    <InviteDialog
      ref="inviteDialogRef"
      :unit-id="unitId"
      :unit-name="unitName"
      :user-type="UserTypeEnum.REPAIR_UNIT"
      :roles="['报修人员']"
    />
  </div>
</template>

<script setup lang="ts">
import { SearchContainer, CommonTable } from "@/base-components";
import { reactive, ref } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { unitColumns } from "./config";
import { ElMessage } from "element-plus";
import { getCustomerUnitList } from "@/api";
import { CommonStatusEnum, UserTypeEnum } from "@/configs";
import { useRouter } from "vue-router";
import InviteDialog from "./component/InviteDialog.vue";

const router = useRouter();

const searchForm = reactive({
  companyName: "",
  status: "",
});
const requestParams = reactive({
  filters: "",
});

function handleSearch() {
  const filters = [] as string[];
  for (let key in searchForm) {
    if (searchForm[key]) {
      filters.push(`${key}=${searchForm[key]}`);
    }
  }
  requestParams.filters = filters.join(",");
}

function handleReset() {
  searchForm.companyName = "";
  searchForm.status = "";
  requestParams.filters = "";
}

function dataCallback(res: any) {
  const data = res.data?.data?.rows || [];
  data.forEach((item: any) => {});
  return {
    tableRows: data,
    total: res.data?.data?.pageElements?.totalElements || 0,
  };
}

// 查看详情
const handleView = (row: any) => {
  router.push(`/customer/unit-detail?unitId=${row.id}`);
};

// 查看人员台账
const handleStaffList = (row: any) => {
  router.push({
    path: "/customer/staff",
    query: { unitName: row.companyName },
  });
};

// 邀请员工注册
const inviteDialogRef = ref();
const unitId = ref("");
const unitName = ref("");
const handleAddStaff = (row: any) => {
  unitId.value = row.id;
  unitName.value = row.companyName;
  setTimeout(() => {
    inviteDialogRef.value?.openDialog();
  }, 100);
};

// 跳转到结算配置页面
const handleSettlementConfig = (row: any) => {
  router.push({
    path: "/customer/settlement-config",
    query: { unitId: row.id },
  });
};
</script>

<style lang="less" scoped>
.unit-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .unit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;
  }

  .unit-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .unit-search {
      flex-shrink: 0;
      background: #f5f5f5;
      border-radius: 10px;
      padding: 0px 20px;
      box-sizing: border-box;
    }

    .unit-content {
      margin-top: 10px;
      flex: 1;
      min-height: 0px;
    }
  }
}
</style>
