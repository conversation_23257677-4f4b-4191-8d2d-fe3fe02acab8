/* 页面布局通用样式 */

/* 页面基础样式 */
.page-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 有导航栏的页面内容区域 */
.page-content-with-nav {
  flex: 1;
  min-height: 0;
  width: 100%;
  overflow-y: auto;
  position: relative;
  box-sizing: border-box;
}

/* 无导航栏的页面内容区域 */
.page-content {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  position: relative;
  box-sizing: border-box;
}

/* 底部有TabBar的页面内容区域 */
.page-content-with-tab {
  height: calc(100% - var(--window-bottom)); /* 使用 uni-app 提供的变量 */
  width: 100%;
  overflow-y: auto;
  position: relative;
  box-sizing: border-box;
}

/* 既有导航栏又有TabBar的页面内容区域 */
.page-content-with-nav-tab {
  flex: 1;
  width: 100%;
  overflow-y: auto;
  position: relative;
  box-sizing: border-box;
}

/* 底部固定区域 */
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */
  z-index: 9;
}

/* 底部占位，防止内容被固定底栏遮挡 */
.bottom-placeholder {
  width: 100%;
  height: 120rpx;
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */
}

/* 禁止滚动 */
.no-scroll {
  overflow: hidden;
}

/* 允许滚动 */
.can-scroll {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* flex布局工具类 */
.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-1 {
  flex: 1;
}
