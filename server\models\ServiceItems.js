const mongoose = require("mongoose");
const { v4: uuid } = require("uuid");
// 服务内容Schema
const ServiceItemSchema = new mongoose.Schema(
  {
    // 服务项目编号
    code: {
      type: String,
      required: true,
      unique: true,
    },
    // 服务类型编号
    serviceClass: {
      type: String,
      required: true,
    },
    //服务项目
    serviceItem: {
      type: String,
      required: true,
    },
    // 单位
    unit: {
      type: String,
      required: true,
      default: "",
    },
    // 售价
    unitPrice: {
      type: Number,
      required: true,
    },
    // 成本最高限价
    maxPrice: {
      type: Number,
      required: true,
    },
    // 税率
    taxRate: {
      type: Number,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);
const ServiceItem = mongoose.model("ServiceItem", ServiceItemSchema);

module.exports = {
  ServiceItem,
  ServiceItemSchema,
};
