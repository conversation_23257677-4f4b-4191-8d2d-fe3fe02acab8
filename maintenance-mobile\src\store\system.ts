import { defineStore } from "pinia"
import { UserTypeEnum } from "@/configs"

interface SystemState {
  navBarHeight: string
}

export const useSystemStore = defineStore("SYSTEM_INFO", {
  state: (): SystemState => ({
    navBarHeight: uni.getStorageSync("navBarHeight") || "44"
  }),
  actions: {
    setNavBarHeight(navBarHeight: any) {
      // 更新 storage
      uni.setStorageSync("navBarHeight", navBarHeight)

      // 更新 state
      this.navBarHeight = navBarHeight
    },
    haveOtherPages() {
      return getCurrentPages().length > 1
    }
  }
})
