const mongoose = require("mongoose");
const { v4: uuid } = require("uuid");
// 服务内容Schema
const ServiceClassSchema = new mongoose.Schema(
  {
    // 编号
    id: {
      type: String,
      required: true,
      unique: true,
    },
    // 服务类型
    serviceClass: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);
const ServiceClass = mongoose.model("ServiceClass", ServiceClassSchema);

module.exports = {
  ServiceClass,
  ServiceClassSchema,
};
