import { SettlementTypeEnum, WorkOrderStatusEnum } from "../enums/work-order";

export const settlementTypeOptions = [
  {
    label: "一单一结",
    value: SettlementTypeEnum.IMMEDIATELY,
  },
  {
    label: "包年",
    value: SettlementTypeEnum.YEARLY,
  },
  {
    label: "包年配件另算",
    value: SettlementTypeEnum.YEARLY_WITHOUT_PARTS,
  },
];

export const workOrderStatusOptions = [
  {
    label: "待派单",
    value: WorkOrderStatusEnum.WAITING_DISPATCH,
  },
  {
    label: "待维保公司派单",
    value: WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH,
  },
  {
    label: "待维保人员上门",
    value: WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME,
  },
  {
    label: "待维保方出具维修方案",
    value: WorkOrderStatusEnum.WAITING_REPAIR_PLAN,
  },
  {
    label: "待平台审核维修方案",
    value: WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT,
  },
  {
    label: "待维保方修改维保方案",
    value: WorkOrderStatusEnum.WAITING_REPAIR_PLAN_MODIFY,
  },
  {
    label: "待报修方确认维修方案",
    value: WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM,
  },
  {
    label: "处理中",
    value: WorkOrderStatusEnum.PROCESSING,
  },
  {
    label: "待报修方确认维修完成",
    value: WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM,
  },
  {
    label: "待平台确认完成",
    value: WorkOrderStatusEnum.WAITING_PLATFORM_FINISH_CONFIRM,
  },
  {
    label: "已完成",
    value: WorkOrderStatusEnum.FINISHED,
  },
  {
    label: "已取消",
    value: WorkOrderStatusEnum.CANCELLED,
  },
  {
    label: "无需维修",
    value: WorkOrderStatusEnum.NO_NEED_REPAIR,
  },
];
