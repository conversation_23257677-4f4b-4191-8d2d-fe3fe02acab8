import { UserTypeEnum, UserStatusEnum } from "../enums/user";

export const userTypeOptions = [
  {
    label: "平台方",
    value: UserTypeEnum.PLATFORM,
  },
  {
    label: "维保单位",
    value: UserTypeEnum.MAINTENANCE_UNIT,
  },
  {
    label: "报修单位",
    value: UserTypeEnum.REPAIR_UNIT,
  },
];

export const userStatusOptions = [
  {
    label: "正常",
    value: UserStatusEnum.ENABLED,
  },
  {
    label: "待审核",
    value: UserStatusEnum.WAITING_AUDIT,
  },
  {
    label: "审核不通过",
    value: UserStatusEnum.AUDIT_FAILED,
  },
  {
    label: "已禁用",
    value: UserStatusEnum.DISABLED,
  },
];
