import { SettlementTypeEnum } from "@/configs"
import { defineStore } from "pinia"

export interface ServiceItemType {
  serviceItemId: string
  code: string
  serviceClass: string
  serviceItem: string
  unitPrice: number
  quantity: number
  unit: string
  subtotal: number
  description?: string
  isSelected?: boolean
  reportSettlementId?: string
  maintenanceUnitSettlementType?: SettlementTypeEnum
  [key: string]: any
}

export const useWorkOrderDetailStore = defineStore("WORK_ORDER_DETAIL", {
  state: () => ({
    workOrderId: uni.getStorageSync("workOrderId") || "", // 工单号
    maintenanceUnitId: uni.getStorageSync("maintenanceUnitId") || "", // 维保单位id
    maintenanceUserId: uni.getStorageSync("maintenanceUserId") || "", // 维保人id
    reportUnitId: uni.getStorageSync("reportUnitId") || "", // 报修单位id
    reportUnitName: uni.getStorageSync("reportUnitName") || "", // 保修单位名称
    detailLocation: uni.getStorageSync("detailLocation") || "", // 详细地址
    reporterId: uni.getStorageSync("reporterId") || "", // 报修人id
    reporterName: uni.getStorageSync("reporterName") || "", // 报修人名称
    reporterPhone: uni.getStorageSync("reporterPhone") || "", // 报修人号码
    serviceClass: uni.getStorageSync("serviceClass") || "", // 报修类型
    reportWay: uni.getStorageSync("reportWay") || "SELF_REPORT", // 报修方式
    faultDesc: uni.getStorageSync("faultDesc") || "", // 故障描述
    deviceInfo: uni.getStorageSync("deviceInfo") || "", // 机器信息
    attachments: uni.getStorageSync("attachments") ? JSON.parse(uni.getStorageSync("attachments")) : [], // 附件
    serviceItems: uni.getStorageSync("serviceItems") ? JSON.parse(uni.getStorageSync("serviceItems")) : [], // 服务类别
    parts: uni.getStorageSync("parts") ? JSON.parse(uni.getStorageSync("parts")) : [], // 配件
    processingOpinion: uni.getStorageSync("processingOpinion") || "", // 处理意见
    totalPrice: uni.getStorageSync("totalPrice") || "", // 总价
    distance: uni.getStorageSync("distance") || 0, // 距离
    serviceFee: uni.getStorageSync("serviceFee") || 0, // 服务费
    transportFee: uni.getStorageSync("transportFee") || 0, // 交通费
    partsTotal: uni.getStorageSync("partsTotal") || 0, // 配件总价
    repairProcess: uni.getStorageSync("repairProcess") ? JSON.parse(uni.getStorageSync("repairProcess")) : [], //处理信息
    finishAttachments: uni.getStorageSync("finishAttachments") || [], // 完成维修附件
    finishDesc: uni.getStorageSync("finishDesc") || "", // 完成维修备注
    signatureFrom: uni.getStorageSync("signatureFrom") || "", // 签名来源
    signatureConfirm: uni.getStorageSync("signatureConfirm") || false, // 签名确认
    signatureBase64: uni.getStorageSync("signatureBase64") || "" // 签名base
  }),
  actions: {
    setWorkOrderDetail(detail: any) {
      this.workOrderId = detail.workOrderId || ""
      this.maintenanceUnitId = detail.maintenanceUnitId || ""
      this.maintenanceUserId = detail.maintenanceUserId || ""
      this.reportUnitId = detail.reportUnitId || ""
      this.reportUnitName = detail.reportUnitName || ""
      this.detailLocation = detail.detailLocation || ""
      this.reporterId = detail.reporterId || ""
      this.reporterName = detail.reporterName || ""
      this.reporterPhone = detail.reporterPhone || ""
      this.serviceClass = detail.serviceClass || ""
      this.reportWay = detail.reportWay || "SELF_REPORT"
      this.faultDesc = detail.faultDesc || ""
      this.deviceInfo = detail.deviceInfo || ""
      this.attachments = detail.attachments || []
      this.serviceItems = detail.serviceItems || []
      this.parts = detail.parts || []
      this.processingOpinion = detail.processingOpinion || ""
      this.repairProcess = detail.repairProcess || []
      this.totalPrice = detail.totalPrice || ""
      this.distance = detail.distance || 0
      this.serviceFee = detail.serviceFee || 0
      this.transportFee = detail.transportFee || 0
      this.partsTotal = detail.partsTotal || 0

      uni.setStorageSync("workOrderId", this.workOrderId)
      uni.setStorageSync("maintenanceUnitId", this.maintenanceUnitId)
      uni.setStorageSync("maintenanceUserId", this.maintenanceUserId)
      uni.setStorageSync("reportUnitId", this.reportUnitId)
      uni.setStorageSync("reportUnitName", this.reportUnitName)
      uni.setStorageSync("detailLocation", this.detailLocation)
      uni.setStorageSync("reporterId", this.reporterId)
      uni.setStorageSync("reporterName", this.reporterName)
      uni.setStorageSync("reporterPhone", this.reporterPhone)
      uni.setStorageSync("serviceClass", this.serviceClass)
      uni.setStorageSync("reportWay", this.reportWay)
      uni.setStorageSync("faultDesc", this.faultDesc)
      uni.setStorageSync("deviceInfo", this.deviceInfo)
      uni.setStorageSync("attachments", JSON.stringify(this.attachments))
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))
      uni.setStorageSync("parts", JSON.stringify(this.parts))
      uni.setStorageSync("processingOpinion", this.processingOpinion)
      uni.setStorageSync("repairProcess", JSON.stringify(this.repairProcess))
      uni.setStorageSync("totalPrice", this.totalPrice)
      uni.setStorageSync("distance", this.distance)
      uni.setStorageSync("serviceFee", this.serviceFee)
      uni.setStorageSync("transportFee", this.transportFee)
      uni.setStorageSync("partsTotal", this.partsTotal)
    },
    // 设置指定的某些值
    setSpecifiedInfo(data: any) {
      for (let key in data) {
        this[key] = data[key]
        uni.setStorageSync(key, data[key])
      }
    },
    // 添加服务项目
    addServiceItem(item: ServiceItemType) {
      // 检查是否已存在相同ID的非临时项目
      const existingIndex = this.serviceItems.findIndex(
        (i: any) => i.serviceItemId === item.serviceItemId && !i.isSelected
      )

      if (existingIndex >= 0) {
        // 如果已存在，更新数量和小计
        this.serviceItems[existingIndex].quantity += item.quantity
        this.serviceItems[existingIndex].subtotal =
          this.serviceItems[existingIndex].unitPrice * this.serviceItems[existingIndex].quantity
      } else {
        // 如果不存在，添加新项目（确保不是临时选中项）
        this.serviceItems.push({
          ...item,
          isSelected: false // 确保不是临时选中项
        })
      }
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))

      // 更新总价
      this.calculateTotalPrice()
    },

    // 批量添加服务项目
    addServiceItems(items: Array<ServiceItemType>) {
      // 清除临时选中的服务项目
      this.serviceItems = this.serviceItems.filter((item: any) => !item.isSelected)

      // 添加新的服务项目（确保不是临时选中项）
      items.forEach(item => {
        const newItem = {
          ...item,
          isSelected: false // 确保不是临时选中项
        }
        const existingIndex = this.serviceItems.findIndex((i: any) => i.serviceItemId === newItem.serviceItemId)
        if (existingIndex !== -1) {
          this.serviceItems[existingIndex] = newItem
        } else {
          this.serviceItems.push(newItem)
        }
      })
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))

      // 更新总价
      this.calculateTotalPrice()
    },

    // 移除服务项目
    removeServiceItem(id: string) {
      this.serviceItems = this.serviceItems.filter((item: any) => item.serviceItemId !== id)
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))

      // 更新总价
      this.calculateTotalPrice()
    },

    // 设置临时选中的服务项目
    setSelectedServiceItems(items: Array<ServiceItemType>) {
      // 清除之前的临时选中项和取消选中的项
      const oldServiceItems = this.serviceItems.filter((item: any) => {
        return !item.isSelected && items.find(i => i.serviceItemId === item.serviceItemId)
      })

      // 添加新的临时选中项
      let selectedItems = items.map(item => ({
        ...item,
        quantity: 0, // 临时项数量设为0
        subtotal: 0, // 临时项小计设为0
        isSelected: true // 标记为临时选中项
      }))

      // 新添加的临时选项中过滤已经添加过的选项
      selectedItems = selectedItems.filter(item => {
        const existingIndex = oldServiceItems.findIndex(
          (i: any) => i.serviceItemId === item.serviceItemId && !i.isSelected
        )
        return existingIndex === -1
      })

      this.serviceItems = [...oldServiceItems, ...selectedItems]
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))
    },

    // 获取临时选中的服务项目
    getSelectedServiceItems() {
      return this.serviceItems.filter((item: any) => item.isSelected)
    },

    // 清除临时选中的服务项目
    clearSelectedServiceItems() {
      this.serviceItems = this.serviceItems.filter((item: any) => !item.isSelected)
      uni.setStorageSync("serviceItems", JSON.stringify(this.serviceItems))
    },

    // 计算总价
    calculateTotalPrice() {
      // 计算服务项目总价（只计算非临时选中项）
      const serviceItemsTotal = this.serviceItems.reduce((total: any, item: any) => {
        // 只计算非临时选中项的小计
        return total + (item.isSelected ? 0 : item.subtotal)
      }, 0)

      // 计算配件总价
      const partsTotal = this.parts.reduce((total: any, item: any) => {
        return total + (item.sellingPrice || 0)
      }, 0)

      // 设置总价
      this.totalPrice = serviceItemsTotal + partsTotal
      uni.setStorageSync("totalPrice", this.totalPrice)
    },
    // 清空工单详情
    clearWorkOrderDetail() {
      this.workOrderId = ""
      this.maintenanceUnitId = ""
      this.maintenanceUserId = ""
      this.reportUnitId = ""
      this.reportUnitName = ""
      this.detailLocation = ""
      this.reporterId = ""
      this.reporterName = ""
      this.reporterPhone = ""
      this.serviceClass = ""
      this.reportWay = "SELF_REPORT"
      this.faultDesc = ""
      this.deviceInfo = ""
      this.attachments = []
      this.serviceItems = []
      this.parts = []
      this.processingOpinion = ""
      this.repairProcess = []
      this.totalPrice = ""
      this.distance = 0
      this.serviceFee = 0
      this.transportFee = 0
      this.partsTotal = 0

      uni.removeStorageSync("workOrderId")
      uni.removeStorageSync("maintenanceUnitId")
      uni.removeStorageSync("maintenanceUserId")
      uni.removeStorageSync("reportUnitId")
      uni.removeStorageSync("reportUnitName")
      uni.removeStorageSync("detailLocation")
      uni.removeStorageSync("reporterId")
      uni.removeStorageSync("reporterName")
      uni.removeStorageSync("reporterPhone")
      uni.removeStorageSync("serviceClass")
      uni.removeStorageSync("reportWay")
      uni.removeStorageSync("faultDesc")
      uni.removeStorageSync("deviceInfo")
      uni.removeStorageSync("attachments")
      uni.removeStorageSync("serviceItems")
      uni.removeStorageSync("parts")
      uni.removeStorageSync("processingOpinion")
      uni.removeStorageSync("repairProcess")
      uni.removeStorageSync("totalPrice")
      uni.removeStorageSync("distance")
      uni.removeStorageSync("serviceFee")
      uni.removeStorageSync("transportFee")
      uni.removeStorageSync("partsTotal")
    }
  }
})
