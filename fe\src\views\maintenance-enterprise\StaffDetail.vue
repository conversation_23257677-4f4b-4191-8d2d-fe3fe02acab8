<template>
  <div v-loading="dataLoading || actionLoading" class="staff-detail">
    <div class="staff-detail-header">
      <el-button :icon="ArrowLeftBold" link @click="handleReturn">
        返回
      </el-button>

      <div class="right-actions">
        <!-- 根据不同状态显示不同按钮 -->
        <template v-if="userStore.userType === UserTypeEnum.MAINTENANCE_UNIT">
          <template v-if="status.value === 'create'">
            <el-button type="primary" @click="handleSave">保存</el-button>
          </template>
          <template v-else>
            <el-button type="primary" @click="handleEdit">保存</el-button>
            <el-button
              v-if="status.value === UserStatusEnum.ENABLED"
              type="danger"
              @click="() => handleApply(UserStatusEnum.DISABLED)"
            >
              禁用
            </el-button>
            <el-button
              v-if="status.value === UserStatusEnum.DISABLED"
              type="primary"
              @click="() => handleApply(UserStatusEnum.ENABLED)"
            >
              启用
            </el-button>
          </template>
        </template>
        <template
          v-else-if="
            status.value === UserStatusEnum.WAITING_AUDIT &&
            userStore.userType === UserTypeEnum.PLATFORM
          "
        >
          <el-button type="primary" @click="() => handleAudit('APPROVED')">
            审核通过
          </el-button>
          <el-button type="danger" @click="openRejectDialog">
            审核不通过
          </el-button>
        </template>
      </div>
    </div>

    <div class="staff-detail-content">
      <el-form
        ref="formRef"
        class="staff-detail-form"
        :model="formData"
        label-suffix="："
        label-width="120px"
        :rules="formRules"
      >
        <!-- 基本信息 -->
        <div class="section-title">
          <span>基本信息</span>
          <el-tag
            v-if="status.value !== 'create'"
            :type="status.type"
            style="flex-shrink: 0"
          >
            {{ status.label }}
          </el-tag>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="username">
              <el-input
                v-model.trim="formData.username"
                :disabled="!canEdit"
                placeholder="请输入姓名"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select
                v-model="formData.gender"
                :disabled="!canEdit"
                placeholder="请选择性别"
              >
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model.trim="formData.phone"
                :disabled="!canEdit"
                placeholder="请输入联系电话"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工种" prop="workTypes">
              <el-select
                v-model="formData.workTypes"
                :disabled="!canEdit"
                placeholder="请选择工种"
                multiple
                :loading="dictionaryLoading"
              >
                <el-option
                  v-for="item in workTypesOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="作业区域" prop="areas">
          <el-select
            v-model="formData.areas"
            placeholder="请选择作业区域"
            multiple
            :disabled="!canEdit"
            :loading="dictionaryLoading"
          >
            <el-option
              v-for="item in operationAreaOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="角色" :disabled="!canEdit" prop="roles">
          <el-select
            v-model="formData.roles"
            placeholder="请选择角色"
            multiple
            :disabled="!canEdit"
            :loading="dictionaryLoading"
          >
            <el-option
              v-for="item in maintenanceRolesOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 资质信息 -->
        <div class="section-title">资质信息</div>
        <el-form-item label="资质文件" prop="qualifications">
          <div class="upload-container">
            <div v-if="canEdit" class="upload-header">
              <span style="color: #aaa">
                （支持类型
                PDF、DOC、DOCX、JPG、JPEG、PNG，请注意上传的数据中是否包含敏感信息）
              </span>
              <span>
                资质文件包含（营业执照、安全生产许可证、根据业务需要由相关部门颁发的资质证书，如特种作业操作证等）
              </span>
            </div>
            <div class="qualification-file-list">
              <div
                v-for="(item, index) in showFileList"
                :key="index"
                v-loading="
                  deleteFileLoading.includes(item.fileId) ||
                  deleteFileLoading.includes(item.uid)
                "
                class="qualification-file-item"
              >
                <div class="file-info">
                  <el-icon
                    v-if="item.status === 'success'"
                    style="color: #67c23a"
                  >
                    <SuccessFilled />
                  </el-icon>
                  <el-icon
                    v-if="item.status === 'error'"
                    style="color: #f56c6c"
                  >
                    <CircleCloseFilled />
                  </el-icon>
                  <el-icon
                    v-if="item.status === 'uploading'"
                    class="is-loading"
                    style="color: #e6a23c"
                  >
                    <Loading />
                  </el-icon>
                  <div class="file-name">{{ item.fileName }}</div>
                  <div class="file-actions">
                    <!-- <el-button link type="primary"> 预览 </el-button> -->
                    <el-button
                      v-if="item.status === 'success'"
                      link
                      type="primary"
                      @click="handleDownloadFile(item)"
                    >
                      下载
                    </el-button>
                    <el-button
                      v-if="canEdit"
                      link
                      type="danger"
                      @click="() => handleDeleteFile(item)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <div v-if="item.errorMessage" class="file-hint">
                  {{ item.errorMessage }}
                </div>
              </div>
              <template v-if="!canEdit && showFileList.length === 0">
                <EmptyContent style="width: 100%" desc="暂无资质文件" />
              </template>
            </div>
            <!-- 只能上传PDF、DOC、DOCX、JPG、JPEG、PNG -->
            <el-upload
              v-if="canEdit"
              action="#"
              multiple
              accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,image/jpeg,image/png,image/jpg"
              :max-count="10"
              :show-file-list="false"
              :auto-upload="false"
              :on-change="uploadFile"
            >
              <div class="upload-button">
                <el-icon><Upload /></el-icon>
                <span>上传文件</span>
              </div>
            </el-upload>
          </div>
        </el-form-item>

        <!-- 审核信息 -->
        <template v-if="auditHistory.length > 0">
          <div class="section-title">审核信息</div>
          <div class="process-timeline">
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in auditHistory"
                :key="index"
                :timestamp="formatDatetime(item.auditTime)"
                :color="item.status === 'APPROVED' ? '#67C23A' : '#F56C6C'"
              >
                <div class="timeline-content">
                  <div class="timeline-info">
                    <span>{{ item.auditUserName }}</span>
                    <span>{{
                      item.status === "APPROVED" ? "审核通过" : "审核不通过"
                    }}</span>
                  </div>
                  <div v-if="item.auditReason" class="detail-info">
                    <span>原因：{{ item.auditReason }}</span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </template>
      </el-form>
    </div>

    <CommonDialog
      v-model:visible="rejectVisible"
      title="审核不通过"
      :confirm-callback="
        () =>
          rejectFormRef?.validate((valid) => {
            if (!valid) return;
            handleAudit('REJECTED');
          })
      "
    >
      <el-form ref="rejectFormRef" :model="{ rejectReason }">
        <el-form-item
          label="审核不通过原因"
          prop="rejectReason"
          :rules="[requiredRules]"
        >
          <el-input v-model.trim="rejectReason" type="textarea" />
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  ArrowLeftBold,
  SuccessFilled,
  CircleCloseFilled,
  Loading,
  Upload,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { UserStatusEnum, UserTypeEnum } from "@/configs";
import {
  getMaintenanceEnterpriseStaffDetail,
  getDictionaryList,
  uploadQualificationFile,
  createMaintenanceEnterpriseStaff,
  editMaintenanceEnterpriseStaff,
  auditMaintenanceEnterpriseStaff,
  getFileData,
  editMaintenanceEnterpriseStaffStatus,
  deleteFile,
} from "@/api";
import {
  convertUserStatus,
  toastError,
  formatDatetime,
  downloadFile,
  nameValidator,
  phoneNumberValidator,
} from "@/utils";
import { Message, SystemPrompt } from "../../utils";
import { useUserStore } from "@/store";
import { cloneDeep } from "lodash";
import { CommonDialog, EmptyContent } from "@/base-components";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const rejectFormRef = ref();

// 状态控制
const status = ref({
  label: "新建",
  value: "create",
  type: "info",
});
const userId = ref("");

const dataLoading = ref(false);
// 表单数据
const formData = ref({
  username: "",
  gender: "",
  phone: "",
  areas: [],
  roles: [],
  workTypes: [],
  qualifications: [],
});

const auditHistory = ref([]);

// 初始化数据
onMounted(async () => {
  userId.value = route.query.userId;
  if (userId.value) {
    // 调用获取用户详情接口
    initData();
  }
});

// 初始化数据
function initData() {
  dataLoading.value = true;
  getMaintenanceEnterpriseStaffDetail(userId.value)
    .then((res) => {
      const data = res.data?.data;
      for (let key in formData.value) {
        if (key === "qualifications") showFileList.value = cloneDeep(data[key]);
        formData.value[key] = data[key];
      }
      showFileList.value.forEach((item) => {
        item.status = "success";
      });
      status.value = convertUserStatus(data.status);

      auditHistory.value = data.auditHistory || [];
    })
    .catch((err) => {
      toastError(err, "获取用户详情失败");
    })
    .finally(() => {
      dataLoading.value = false;
    });
}

// 处理函数
const handleReturn = () => {
  router.back();
};

const handleApply = async (status) => {
  SystemPrompt(
    status === "ENABLED"
      ? "启用后，将可以向该维保人员进行派单，确定要启用吗？"
      : "禁用后，将无法向该维保人员进行派单，确定要禁用吗？"
  ).then(() => {
    actionLoading.value = true;
    editMaintenanceEnterpriseStaffStatus({
      userId: userId.value,
      status,
    })
      .then((res) => {
        Message.success("操作成功");
        initData();
      })
      .catch((err) => {
        toastError(err, "操作失败");
      })
      .finally(() => {
        actionLoading.value = false;
      });
  });
};

// 表单校验规则
const formRules = {
  username: [
    { required: true, message: "请输入姓名", trigger: "blur" },
    { validator: nameValidator, trigger: "blur" },
  ],
  gender: [{ required: true, message: "请选择性别", trigger: "change" }],
  phone: [
    { required: true, message: "请输入联系电话", trigger: "blur" },
    { validator: phoneNumberValidator, trigger: "blur" },
  ],
  workTypes: [{ required: true, message: "请选择工种", trigger: "change" }],
  areas: [{ required: true, message: "请选择作业区域", trigger: "change" }],
  roles: [{ required: true, message: "请选择角色", trigger: "change" }],
  qualifications: [
    { required: true, message: "请上传资质文件", trigger: "change" },
  ],
};

/* ============================================ 表单操作 ============================================ */
const requiredRules = {
  required: true,
  message: "请输入原因",
  trigger: "blur",
};
const actionLoading = ref(false);
// 新建
const handleSave = async () => {
  actionLoading.value = true;
  createMaintenanceEnterpriseStaff({ ...formData.value, unit: userStore.unit })
    .then((res) => {
      Message.success("新建成功");
      router.back();
    })
    .catch((err) => {
      toastError(err, "新建失败");
    })
    .finally(() => {
      actionLoading.value = false;
    });
};

// 是否可编辑
const canEdit = computed(() => {
  return (
    status.value.value === "create" ||
    userStore.userType === UserTypeEnum.MAINTENANCE_UNIT
  );
});
// 修改
const handleEdit = async () => {
  actionLoading.value = true;

  editMaintenanceEnterpriseStaff({
    ...formData.value,
    userId: userId.value,
    unit: userStore.unit,
  })
    .then((res) => {
      Message.success("修改成功");
      initData();
    })
    .catch((err) => {
      toastError(err, "修改失败");
    })
    .finally(() => {
      actionLoading.value = false;
    });
};

// 审核
const rejectVisible = ref(false);
const rejectReason = ref("");
function openRejectDialog() {
  rejectVisible.value = true;
  rejectReason.value = "";
}

const handleAudit = async (status) => {
  actionLoading.value = true;
  auditMaintenanceEnterpriseStaff({
    userId: userId.value,
    status,
    rejectReason: rejectReason.value,
  })
    .then((res) => {
      Message.success("审核成功");
      rejectVisible.value = false;
      initData();
    })
    .catch((err) => {
      toastError(err, "审核失败");
    })
    .finally(() => {
      actionLoading.value = false;
    });
};

/* ============================================ 字典数据 ============================================ */
const dictionaryLoading = ref(false);
const operationAreaOptions = ref([]);
const workTypesOptions = ref([]);
const maintenanceRolesOptions = ref([]);
// 获取字典列表
onMounted(async () => {
  try {
    dictionaryLoading.value = true;
    const res = await getDictionaryList(
      "operationArea,workTypes,maintenanceRoles"
    );
    operationAreaOptions.value =
      res.data.find((item) => item.groupValue === "operationArea")?.children ||
      [];
    workTypesOptions.value =
      res.data.find((item) => item.groupValue === "workTypes")?.children || [];
    maintenanceRolesOptions.value =
      res.data.find((item) => item.groupValue === "maintenanceRoles")
        ?.children || [];
    dictionaryLoading.value = false;
  } catch (error) {
    toastError(error, "获取字典列表失败");
    dictionaryLoading.value = false;
  }
});

/* ============================================ 资质文件 ============================================ */
const showFileList = ref([]);
// 是否是规定的上传类型
const isAllowedFileType = (fileType) => {
  return (
    fileType === "application/pdf" ||
    fileType === "application/msword" ||
    fileType ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
    fileType === "image/jpeg" ||
    fileType === "image/png" ||
    fileType === "image/jpg"
  );
};

// 上传文件
const uploadFile = async (file, fileList) => {
  if (!isAllowedFileType(file.raw.type)) {
    showFileList.value.push({
      uid: file.uid,
      fileId: "",
      fileName: file.name,
      status: "error",
      errorMessage: "只支持上传PDF、DOC、DOCX、JPG、JPEG、PNG文件",
    });
    return;
  }

  showFileList.value.push({
    uid: file.uid,
    fileId: "",
    fileName: file.name,
    status: "uploading",
  });

  try {
    // 使用文件的raw属性，这是实际的File对象
    const res = await uploadQualificationFile(file.raw);
    if (res.data && res.data.fileId) {
      // 添加到文件列表
      formData.value.qualifications.push({
        fileName: file.name,
        fileId: res.data.fileId,
        fileType: file.raw.type,
      });
      const index = showFileList.value.findIndex(
        (item) => item.uid === file.uid
      );
      if (index !== -1) {
        showFileList.value[index].status = "success";
        showFileList.value[index].fileId = res.data.fileId;
      }
    }
  } catch (error) {
    const index = showFileList.value.findIndex((item) => item.uid === file.uid);
    if (index !== -1) showFileList.value[index].status = "error";
    console.error("文件上传失败:", error);
  }
};

// 删除文件
const deleteFileLoading = ref([]);
const handleDeleteFile = async (file) => {
  try {
    //新上传的删除
    if (file.fileId) deleteFileLoading.value.push(file.fileId);
    else deleteFileLoading.value.push(file.uid);
    if (file.uid && file.fileId) {
      await deleteFile([file.fileId]);
    }
    showFileList.value = showFileList.value.filter(
      (item) =>
        item.fileId !== file.fileId &&
        ((item.uid && item.uid !== file.uid) || !item.uid)
    );
    formData.value.qualifications = formData.value.qualifications.filter(
      (item) =>
        item.fileId !== file.fileId &&
        ((item.uid && item.uid !== file.uid) || !item.uid)
    );
  } catch (error) {
    toastError(error, "删除文件失败");
  } finally {
    deleteFileLoading.value = deleteFileLoading.value.filter(
      (item) => item !== file.fileId && item !== file.uid
    );
  }
};

// 下载文件
const handleDownloadFile = (file) => {
  getFileData(file.fileId).then((res) => {
    console.log("下载文件:", res);
    downloadFile({
      fileData: res.data,
      fileName: file.fileName,
      fileType: file.fileType,
    });
  });
};
</script>

<style lang="less" scoped>
.staff-detail {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .staff-detail-header {
    padding: 10px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    flex-shrink: 0;
    min-height: 53px;

    .right-actions {
      .el-button {
        margin-left: 12px;
      }
    }
  }

  .staff-detail-content {
    padding: 0 20px 20px;
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    overflow-y: auto;

    .staff-detail-form {
      .upload-container {
        width: 100%;
        .upload-header {
          display: flex;
          flex-direction: column;
          line-height: 1;
          margin: 8px 0 12px;
          gap: 10px;
        }
        .qualification-file-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-bottom: 10px;
          .qualification-file-item {
            width: calc(50% - 5px);
            height: 50px;
            padding: 0px 10px;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .file-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .file-name {
                flex: 1;
                min-width: 0px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin: 0px 6px;
              }
            }
            .file-hint {
              margin-top: 5px;
              line-height: 1;
              color: var(--el-color-danger);
            }
          }
        }
        .upload-button {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 32px;
          border-radius: 4px;
          border: 1px solid var(--el-color-primary);
          background-color: var(--el-color-primary);
          color: #fff;
          padding: 0 20px;
          &:hover {
            background-color: var(--el-color-primary-light-3);
            border-color: var(--el-color-primary-light-3);
          }
          span {
            margin-left: 12px;
          }
        }
      }
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0;
      padding-left: 10px;
      border-left: 4px solid #409eff;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .upload-tip {
      color: #606266;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .upload-desc {
      color: #909399;
      font-size: 12px;
    }
  }
}

.process-timeline {
  padding: 20px;

  .timeline-content {
    .timeline-info {
      margin-bottom: 8px;

      span {
        margin-right: 8px;
      }
    }
  }

  .detail-info {
    margin-top: 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}
</style>
