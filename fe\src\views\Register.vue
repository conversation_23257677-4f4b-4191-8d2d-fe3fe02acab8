<template>
  <div class="register-container">
    <div class="right-section">
      <div class="register-box">
        <div class="login-title">注册账号</div>

        <div class="invite-register">
          <span v-if="registerStore.userType === UserTypeEnum.REPAIR_UNIT">
            {{
              `${registerStore.unitName}邀请您注册即时修报修工具，注册后，可发起报修`
            }}
          </span>
          <span
            v-else-if="registerStore.userType === UserTypeEnum.MAINTENANCE_UNIT"
          >
            {{
              `${registerStore.unitName}邀请您注册即时修维修工具，注册后，可进行维修`
            }}
          </span>
          <span v-else>
            {{ `即时修平台方邀请你注册即时修管理工具，注册后，可进行管理` }}
          </span>
        </div>

        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="register-form"
          label-position="top"
          size="large"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model.trim="registerForm.username"
              placeholder="请输入用户名（3-20个字符）"
            >
              <template #prefix>
                <el-icon>
                  <User />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model.trim="registerForm.phone"
              placeholder="请输入手机号"
            >
              <template #prefix>
                <el-icon>
                  <Phone />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              v-model.trim="registerForm.password"
              type="password"
              placeholder="请输入至少8位数字、大小写字母和字符的组合"
            >
              <template #prefix>
                <el-icon>
                  <Lock />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model.trim="registerForm.confirmPassword"
              type="password"
              placeholder="请再次输入密码"
            >
              <template #prefix>
                <el-icon>
                  <RefreshRight />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
          <div class="form-footer">
            <el-button
              type="primary"
              class="register-button"
              :loading="loading"
              @click="handleRegister"
            >
              注册
            </el-button>
            <div class="login-tip">
              已有账号？
              <router-link to="/" class="login-link">立即登录</router-link>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { register } from "../api/auth";
import { User, Phone, Lock, RefreshRight } from "@element-plus/icons-vue";
import { UserTypeEnum, userTypeOptions } from "@/configs";
import { nameValidator, Message, mixPasswordValidator } from "@/utils";
import { useRegisterStore } from "@/store";
import { SystemAlert } from "../utils";
const router = useRouter();
const registerFormRef = ref(null);
const loading = ref(false);

const registerStore = useRegisterStore();

onMounted(() => {
  const query = router.currentRoute.value.query;

  if (query.unitId || query.unitName || query.roles || query.userType) {
    registerStore.$patch({
      unitId: query.unitId || "",
      unitName: query.unitName || "",
      userType: query.userType || "",
      roles: query.roles?.split(",") || [],
    });
    router.replace("/register");
  }

  nextTick(() => {
    if (
      !registerStore.userType ||
      (registerStore.userType !== "PLATFORM" &&
        (!registerStore.unitId || !registerStore.roles))
    ) {
      Message.error("注册信息不完整");
      router.push("/");
    }
  });
});

// 表单数据
const registerForm = reactive({
  username: "",
  phone: "",
  password: "",
  confirmPassword: "",
});

// 密码验证函数
const validatePass = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请输入密码"));
  } else {
    if (registerForm.confirmPassword !== "") {
      registerFormRef.value.validateField("confirmPassword");
    }
    callback();
  }
};

const validatePass2 = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请再次输入密码"));
  } else if (value !== registerForm.password) {
    callback(new Error("两次输入密码不一致!"));
  } else {
    callback();
  }
};

// 表单验证规则
const registerRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { validator: nameValidator, trigger: "blur" },
  ],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { validator: mixPasswordValidator, trigger: "blur" },
    { validator: validatePass, trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, message: "请再次输入密码", trigger: "blur" },
    { validator: validatePass2, trigger: "blur" },
  ],
};

// 注册操作
const handleRegister = async () => {
  try {
    // 表单验证
    await registerFormRef.value.validate();

    // loading.value = true;

    // 调用注册API
    const response = await register({
      username: registerForm.username,
      phone: registerForm.phone,
      password: registerForm.password,
      userType: registerStore.userType,
      unitId: registerStore.unitId,
      roles: registerStore.roles,
    });

    ElMessage.success("注册成功");
    if (
      !registerStore.roles.includes("管理员") &&
      registerStore.userType !== UserTypeEnum.PLATFORM
    ) {
      if (registerStore.userType === UserTypeEnum.MAINTENANCE_UNIT) {
        SystemAlert(
          `注册成功，等待管理员审核后，可前往维修平台登录。`,
          "success"
        );
      } else {
        SystemAlert(`注册成功，请前往报修平台登录。`, "success");
      }
    }

    //管理员直接前往登录
    router.push("/");
  } catch (error) {
    if (error.response && error.response.data) {
      ElMessage.error(error.response.data.message || "注册失败");
    } else if (!error.isAxiosError) {
      // 表单验证错误，不显示消息
    } else {
      ElMessage.error("服务器连接失败");
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.register-container {
  width: 100%;
  height: 100%;
  background-image: url("../assets/png/login_bg.png");
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.login-hint {
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.right-section {
  width: 510px;
  background: white;
  border-radius: 8px;
  padding: 45px 35px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.register-box {
  width: 100%;
}

.login-title {
  font-weight: bold;
  font-size: 32px;
  color: var(--el-color-primary);
  line-height: 38px;
  text-align: center;
  margin-bottom: 30px;
  width: 100%;
}

.invite-register {
  margin-bottom: 30px;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 30px;
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 16px;
  padding: 12px 0;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.tab-item.active {
  color: #409eff;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #409eff;
}

.register-form {
  width: 100%;
}

.form-footer {
  margin-top: 20px;
}

.register-button {
  width: 100%;
  height: 40px;
  font-size: 16px;
  margin-bottom: 15px;
}

.login-tip {
  text-align: center;
  font-size: 14px;
  color: #606266;
}

.login-link {
  color: #409eff;
  text-decoration: none;
}

.login-link:hover {
  color: #66b1ff;
}

:deep(.el-input__wrapper) {
  padding: 1px 11px;
}

:deep(.el-input__prefix) {
  margin-right: 8px;
}
</style>
