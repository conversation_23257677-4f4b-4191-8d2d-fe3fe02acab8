export enum WorkOrderStatusEnum {
  // 待派单
  WAITING_DISPATCH = "WAITING_DISPATCH",
  // 待维保公司派单
  WAITING_COMPANY_DISPATCH = "WAITING_COMPANY_DISPATCH",
  //待维保人员上门
  WAITING_REPAIR_PERSON_COME = "WAITING_REPAIR_PERSON_COME",
  // 待维保方出具维修方案
  WAITING_REPAIR_PLAN = "WAITING_REPAIR_PLAN",
  // 待平台审核维修方案
  WAITING_PLATFORM_AUDIT = "WAITING_PLATFORM_AUDIT",
  // 待维保方修改维保方案
  WAITING_REPAIR_PLAN_MODIFY = "WAITING_REPAIR_PLAN_MODIFY",
  // 待报修方确认维修方案
  WAITING_REPAIR_CONFIRM = "WAITING_REPAIR_CONFIRM",
  // 处理中
  PROCESSING = "PROCESSING",
  // 待报修方确认维修完成
  WAITING_REPAIR_FINISH_CONFIRM = "WAITING_REPAIR_FINISH_CONFIRM",
  // 待平台确认完成
  WAITING_PLATFORM_FINISH_CONFIRM = "WAITING_PLATFORM_FINISH_CONFIRM",
  // 已完成
  FINISHED = "FINISHED",
  // 已取消
  CANCELLED = "CANCELLED",
  // 无需维修
  NO_NEED_REPAIR = "NO_NEED_REPAIR",
}

// 结算方式
export enum SettlementTypeEnum {
  IMMEDIATELY = "IMMEDIATELY", // 一单一结
  YEARLY = "YEARLY", // 包年
  YEARLY_WITHOUT_PARTS = "YEARLY_WITHOUT_PARTS", // 包年配件另算
}

// 报修途径
export enum ReportWayEnum {
  PHONE = "PHONE", // 电话报修
  SELF_REPORT = "SELF_REPORT", // 自主填报报修
}
