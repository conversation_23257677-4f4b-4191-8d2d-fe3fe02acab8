<!-- 新增/编辑类别弹窗 -->
<template>
  <CommonDialog
    v-model:visible="visible"
    :title="type === 'add' ? '新增类别' : '编辑类别'"
    :confirm-callback="handleConfirm"
  >
    <el-form label-width="80px" :model="form" :rules="rules">
      <el-form-item label="类别名称" prop="name">
        <el-input v-model.trim="form.name" />
      </el-form-item>
      <el-form-item label="类别编码" prop="code">
        <el-input v-model.trim="form.code" />
      </el-form-item>
      <el-form-item label="类别描述" prop="remark">
        <el-input
          v-model.trim="form.remark"
          type="textarea"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </CommonDialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { CommonDialog } from "@/base-components";
import { createSuppliesClass } from "@/api";

const emits = defineEmits(["refresh"]);

const type = ref<"add" | "edit">("add");
const visible = ref(false);

const form = reactive({
  name: "",
  code: "",
  remark: "",
  id: "",
  parentId: "",
});

const rules = {
  name: [{ required: true, message: "请输入类别名称", trigger: "blur" }],
  code: [{ required: true, message: "请输入类别编码", trigger: "blur" }],
};

function handleConfirm() {
  createSuppliesClass(form).then((res) => {
    visible.value = false;
    emits("refresh");
  });
}

defineExpose({
  open: (dialogType: "add" | "edit", row: any = null) => {
    visible.value = true;
    type.value = dialogType;
    if (dialogType === "edit") {
      form.name = row.name || "";
      form.code = row.code || "";
      form.remark = row.remark || "";
      form.id = row.id || "";
      form.parentId = row.parentId || "";
    } else if (dialogType === "add") {
      form.name = "";
      form.code = "";
      form.remark = "";
      form.parentId = row.id || "";
    }
  },
});
</script>
