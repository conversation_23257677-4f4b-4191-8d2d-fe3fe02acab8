<template>
  <img
    v-if="!loading"
    :style="{
      width: props.width ? props.width + 'px' : '100%',
      height: props.height ? props.height + 'px' : '100%',
      objectFit: 'contain',
    }"
    :src="blobUrl || $props.default"
    @click="
      () => {
        if (blobUrl) showPreview = true;
      }
    "
  />
  <el-icon
    v-else
    color="#999"
    :style="{ fontSize: props.iconSize + 'px', width: '100%', height: '100%' }"
  >
    <Picture />
  </el-icon>
  <!-- 图片预览 -->
  <el-image-viewer
    v-if="showPreview"
    :url-list="[blobUrl]"
    show-progress
    @close="showPreview = false"
  />
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { getFileData } from "@/api";
import { Picture } from "@element-plus/icons-vue";

const props = withDefaults(
  defineProps<{
    fileId: string;
    default?: string;
    iconSize?: number;
    width?: number;
    height?: number;
  }>(),
  { default: "", iconSize: 20 }
);

const blobUrl = ref("");
const loading = ref(false);
const imageDirection = ref(false);
const showPreview = ref(false);
watch(
  () => props.fileId,
  () => {
    if (!props.fileId) return (blobUrl.value = "");
    loading.value = true;

    getFileData(props.fileId)
      .then((res) => {
        blobUrl.value = URL.createObjectURL(res.data);
        loading.value = false;

        //判断图片方向
        const image = new Image();
        image.src = blobUrl.value;
      })
      .catch((err) => {
        blobUrl.value = "";
      });
  },
  {
    immediate: true,
  }
);
</script>
