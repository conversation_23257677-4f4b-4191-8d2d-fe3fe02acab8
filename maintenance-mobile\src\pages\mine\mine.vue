<template>
  <view class="mine-container">
    <view class="status-bar"></view>
    <!-- 用户信息区域 -->
    <view class="user-info-section">
      <view class="avatar-box">
        <image class="avatar" src="/static/avatar.png" mode="aspectFit" />
      </view>
      <view class="user-info">
        <view class="username">{{ userStore.username }}</view>
        <view class="user-phone">{{ userStore.phone }}</view>
        <view class="user-company">{{ userStore.companyName }}</view>
      </view>
    </view>

    <!-- 菜单列表 -->
    <view class="menu-list">
      <view class="menu-group">
        <view class="menu-item" @click="goToOrderRecord">
          <view class="menu-item-left">
            <uni-icons type="bars" size="22" color="#3c9cff"></uni-icons>
            <text class="menu-text">接单记录</text>
          </view>
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view>
        <!-- <view class="menu-item" @click="goToDeviceInspection">
          <view class="menu-item-left">
            <uni-icons type="checkbox" size="22" color="#3c9cff"></uni-icons>
            <text class="menu-text">设备巡检</text>
          </view>
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view> -->
        <!-- <view class="menu-item" @click="goToBasicInfo">
          <view class="menu-item-left">
            <uni-icons type="person" size="22" color="#3c9cff"></uni-icons>
            <text class="menu-text">基础信息</text>
          </view>
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view> -->
        <view class="menu-item" @click="goToResetPassword">
          <view class="menu-item-left">
            <uni-icons type="locked" size="22" color="#3c9cff"></uni-icons>
            <text class="menu-text">修改密码</text>
          </view>
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view>
      </view>

      <!-- <view class="menu-group">
        <view class="menu-item" @click="goToAboutUs">
          <view class="menu-item-left">
            <uni-icons type="info" size="22" color="#3c9cff"></uni-icons>
            <text class="menu-text">关于我们</text>
          </view>
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view>
        <view class="menu-item" @click="contactCustomerService">
          <view class="menu-item-left">
            <uni-icons type="headphones" size="22" color="#3c9cff"></uni-icons>
            <text class="menu-text">联系客服</text>
          </view>
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view>
      </view> -->

      <view class="menu-group">
        <view class="menu-item" @click="handleLogout">
          <view class="menu-item-left">
            <uni-icons type="closeempty" size="22" color="#ff5a5f"></uni-icons>
            <text class="menu-text" style="color: #ff5a5f">退出登录</text>
          </view>
          <uni-icons type="right" size="16" color="#999"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 退出登录确认弹窗 -->
    <uni-popup ref="logoutPopup" type="dialog">
      <uni-popup-dialog
        title="提示"
        content="确定要退出登录吗？"
        :before-close="true"
        @confirm="confirmLogout"
        @close="logoutPopup?.close()"></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { useUserStore } from "@/store/user"
import { logout } from "@/api"
import { showLoading, hideLoading, showToast } from "@/utils/common"
const userStore = useUserStore()
const logoutPopup = ref<any>(null)

// 跳转到接单记录
const goToOrderRecord = () => {
  uni.switchTab({
    url: "/pages/order-record/index"
  })
}

// 跳转到设备巡检页面
const goToDeviceInspection = () => {
  uni.showToast({
    title: "设备巡检功能开发中",
    icon: "none"
  })
}

// 跳转到基础信息页面
const goToBasicInfo = () => {
  uni.showToast({
    title: "基础信息功能开发中",
    icon: "none"
  })
}

// 跳转到关于我们页面
const goToAboutUs = () => {
  uni.showToast({
    title: "关于我们功能开发中",
    icon: "none"
  })
}

// 联系客服
const contactCustomerService = () => {
  uni.showToast({
    title: "关于我们功能开发中",
    icon: "none"
  })
}

// 退出登录
const handleLogout = () => {
  logoutPopup.value?.open()
}

// 确认退出登录
const confirmLogout = () => {
  showLoading("退出登录中...")
  logout()
    .then(() => {
      // 清除登录状态
      userStore.clearUserInfo()
      // 跳转到登录页
      uni.redirectTo({
        url: "/pages/login/login"
      })
    })
    .catch((error: any) => {
      showToast(error.message)
    })
    .finally(() => {
      hideLoading()
    })
}

// 跳转到修改密码页面
const goToResetPassword = () => {
  uni.navigateTo({
    url: "/pages/mine/reset-password"
  })
}
</script>

<style lang="scss">
.mine-container {
  background-color: #f5f5f5;
  min-height: 100vh;

  .status-bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: $uni-color-primary;
  }

  .user-info-section {
    display: flex;
    align-items: center;
    padding: 40rpx 30rpx;
    background-color: $uni-color-primary;
    color: #fff;

    .avatar-box {
      margin-right: 30rpx;

      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        border: 2px solid rgba(255, 255, 255, 0.5);
      }
    }

    .user-info {
      flex: 1;

      .username {
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 20rpx;
      }
      .user-company {
        font-size: 28rpx;
        opacity: 0.8;
      }

      .user-phone {
        font-size: 28rpx;
        opacity: 0.8;
        margin-bottom: 8rpx;
      }
    }
  }

  .menu-list {
    padding: 20rpx 0;

    .menu-group {
      background-color: #fff;
      border-radius: 12rpx;
      margin: 20rpx;
      overflow: hidden;
    }

    .menu-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .menu-item-left {
        display: flex;
        align-items: center;

        .menu-text {
          margin-left: 20rpx;
          font-size: 28rpx;
          color: #333;
        }
      }
    }
  }
}
</style>
