<template>
  <div v-loading="detailLoading || actionLoading" class="unit-detail">
    <div class="unit-detail-header">
      <el-button :icon="ArrowLeftBold" link @click="handleCancel">
        返回
      </el-button>
      <div class="unit-detail-header-right">
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button
          v-if="status === CommonStatusEnum.DISABLED"
          type="primary"
          @click="handleChangeStatus(CommonStatusEnum.ENABLED)"
        >
          启用
        </el-button>
        <el-button
          v-if="status === CommonStatusEnum.ENABLED"
          type="danger"
          @click="handleChangeStatus(CommonStatusEnum.DISABLED)"
        >
          禁用
        </el-button>
      </div>
    </div>

    <div class="unit-detail-content">
      <el-form
        :model="formData"
        label-suffix="："
        label-width="120px"
        :rules="formRules"
        ref="formRef"
      >
        <!-- 基本信息 -->
        <div class="section-title">
          <span>基本信息</span>
          <el-tag
            v-if="status"
            :type="status === CommonStatusEnum.ENABLED ? 'success' : 'danger'"
            style="margin-left: 10px"
          >
            {{ status === CommonStatusEnum.ENABLED ? "已启用" : "已禁用" }}
          </el-tag>
        </div>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model.trim="formData.companyName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="法人姓名" prop="legalPerson">
          <el-input v-model.trim="formData.legalPerson" placeholder="请输入" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人姓名" prop="contactPerson">
              <el-input
                v-model.trim="formData.contactPerson"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人电话" prop="contactPhone">
              <el-input
                v-model.trim="formData.contactPhone"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="公司地址" prop="companyAddress">
          <el-input
            v-model.trim="formData.companyAddress"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label="所属区域" prop="operationArea">
          <el-select
            v-model="formData.operationArea"
            placeholder="请选择"
            multiple
          >
            <el-option
              v-for="option in operationAreaOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ArrowLeftBold } from "@element-plus/icons-vue";
import {
  getDictionaryList,
  createCustomerUnit,
  getCustomerUnitDetail,
  editCustomerUnit,
  updateCustomerUnitStatus,
} from "@/api";
import { CommonStatusEnum } from "@/configs";
import {
  companyNameValidator,
  nameValidator,
  phoneNumberValidator,
  toastError,
  Message,
  SystemPrompt,
} from "@/utils";
import { cloneDeep } from "lodash";

const route = useRoute();
const router = useRouter();
const formRef = ref();

// 根据路由参数判断页面状态
const unitId = ref("");
const status = ref("");
onMounted(async () => {
  unitId.value = route.query.unitId as string;
  status.value = "";
  resetForm();
  if (unitId.value) {
    getUnitDetail();
  }
});

const detailLoading = ref(false);
function getUnitDetail() {
  detailLoading.value = true;
  getCustomerUnitDetail(unitId.value)
    .then((res) => {
      const data = res.data.data;
      for (let key in formData.value) {
        formData.value[key] = data[key];
      }
      status.value = data.status;
    })
    .catch((err) => {
      toastError(err, "获取详情失败");
    })
    .finally(() => {
      detailLoading.value = false;
    });
}
/* ============================================ 字典数据 ============================================ */
const dictionaryLoading = ref(false);
const operationAreaOptions = ref<any[]>([]);
// 获取字典列表
onMounted(async () => {
  try {
    dictionaryLoading.value = true;
    const res = await getDictionaryList("operationArea");
    operationAreaOptions.value =
      res.data.find((item) => item.groupValue === "operationArea")?.children ||
      [];
    dictionaryLoading.value = false;
  } catch (error) {
    toastError(error, "获取字典列表失败");
    dictionaryLoading.value = false;
  }
});

/* ============================================ 表单操作 ============================================ */
const actionLoading = ref(false);
// 表单数据
const initFormData = {
  companyName: "",
  legalPerson: "",
  contactPerson: "",
  contactPhone: "",
  companyAddress: "",
  operationArea: "",
  operationAreaLabel: "",
};
const formData = ref(cloneDeep(initFormData));

// 表单校验规则
const formRules = {
  companyName: [
    { required: true, message: "请输入公司名称", trigger: "change" },
    { validator: companyNameValidator, trigger: "change" },
  ],
  legalPerson: [
    { required: true, message: "请输入法人姓名", trigger: "change" },
    { validator: nameValidator, trigger: "change" },
  ],
  contactPerson: [
    { required: true, message: "请输入联系人姓名", trigger: "change" },
    { validator: nameValidator, trigger: "change" },
  ],
  contactPhone: [
    { required: true, message: "请输入联系人电话", trigger: "change" },
    { validator: phoneNumberValidator, trigger: "change" },
  ],
  companyAddress: [
    { required: true, message: "请输入公司地址", trigger: "change" },
    { max: 200, message: "公司地址不能超过200个字符", trigger: "change" },
  ],
  operationArea: [
    { required: true, message: "请选择所属区域", trigger: "change" },
  ],
};

function resetForm() {
  formData.value = cloneDeep(initFormData);
  formRef.value?.clearValidate();
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (unitId.value) {
        handleUpdate();
      } else {
        handleCreate();
      }
    }
  });
};

//  创建
const handleCreate = () => {
  actionLoading.value = true;
  const operationAreaLabel = operationAreaOptions.value
    .filter((item) => formData.value.operationArea.includes(item.value))
    .map((item) => item.label)
    .join(",");
  createCustomerUnit({
    ...formData.value,
    operationAreaLabel,
  })
    .then((res) => {
      Message.success("新建成功");
      router.back();
    })
    .catch((err) => {
      toastError(err, "新建失败");
    })
    .finally(() => {
      actionLoading.value = false;
    });
};

// 修改
const handleUpdate = () => {
  actionLoading.value = true;
  const operationAreaLabel = operationAreaOptions.value
    .filter((item) => formData.value.operationArea.includes(item.value))
    .map((item) => item.label)
    .join(",");
  editCustomerUnit({
    id: unitId.value,
    ...formData.value,
    operationAreaLabel,
  })
    .then((res) => {
      Message.success("修改成功");
    })
    .catch((err) => {
      toastError(err, "修改失败");
    })
    .finally(() => {
      actionLoading.value = false;
    });
};

// 修改状态
const handleChangeStatus = (status: CommonStatusEnum) => {
  SystemPrompt(
    `确定要${
      status === CommonStatusEnum.ENABLED ? "启用" : "禁用"
    }该客户单位吗？`
  ).then(() => {
    actionLoading.value = true;
    updateCustomerUnitStatus({
      id: unitId.value,
      status,
    })
      .then((res) => {
        Message.success("修改状态成功");
        getUnitDetail();
      })
      .catch((err) => {
        toastError(err, "修改状态失败");
      })
      .finally(() => {
        actionLoading.value = false;
      });
  });
};

// 取消
const handleCancel = () => {
  router.back();
};
</script>

<style lang="less" scoped>
.unit-detail {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);

  .unit-detail-header {
    padding: 10px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
  }

  .unit-detail-content {
    padding: 0px 20px 20px;
    box-sizing: border-box;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0;
      padding-left: 10px;
      border-left: 4px solid #409eff;
    }

    .settlement-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 18px;
      gap: 18px;
      .settlement-item {
        padding: 18px 18px 0;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .el-form-item {
          flex: 1;
          min-width: 300px;
        }
      }
      .el-button {
        margin-left: 0px;
      }
    }
  }
}
</style>
