<template>
  <div class="login-container">
    <div class="login-hint">
      <span>欢迎使用</span>
      <span> 即时修管理平台 </span>
      <span
        style="
          font-size: 18px;
          position: absolute;
          bottom: 5px;
          right: -10px;
          line-height: 1;
        "
      >
        —— 测试版本
      </span>
    </div>

    <div class="login-box">
      <div class="login-title">即时修管理平台</div>
      <div class="tabs">
        <div
          v-for="item in userTypeOptions"
          :key="item.value"
          :class="['tab-item', activeTab === item.value ? 'active' : '']"
          @click="activeTab = item.value"
        >
          {{ item.label }}
        </div>
      </div>
      <el-form
        ref="loginFormRef"
        size="large"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <el-form-item prop="phone">
          <el-input
            v-model.trim="loginForm.phone"
            type="text"
            placeholder="请输入手机号"
          >
            <template #prefix>
              <el-icon><Phone /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model.trim="loginForm.password"
            type="password"
            placeholder="请输入登录密码"
            @keyup.enter="handleLogin"
          >
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <div class="form-options">
          <el-checkbox v-model="rememberMe">记住账号</el-checkbox>
        </div>
        <el-form-item>
          <el-button
            type="primary"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { login } from "../api/auth";
import { Phone, Lock } from "@element-plus/icons-vue";
import { userTypeOptions, UserTypeEnum } from "@/configs";
import { useUserStore } from "@/store";
const router = useRouter();
const loginFormRef = ref(null);
const loading = ref(false);
const rememberMe = ref(false);
const userStore = useUserStore();
// 活动标签，默认平台方
const activeTab = ref(UserTypeEnum.PLATFORM);

// 表单数据
const loginForm = reactive({
  phone: "",
  password: "",
});

// 表单验证规则
const loginRules = {
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度至少为6个字符", trigger: "blur" },
  ],
};

// 登录操作
const handleLogin = async () => {
  try {
    // 表单验证
    await loginFormRef.value.validate();

    loading.value = true;

    // 调用登录API
    const response = await login({
      phone: loginForm.phone,
      password: loginForm.password,
      userType: activeTab.value,
    });

    const { token, user } = response.data;

    // 判断用户类型是否匹配
    if (user.userType !== activeTab.value) {
      ElMessage.error(`登录失败，您不是${activeTab.value}`);
      loading.value = false;
      return;
    }

    // 存储令牌和用户信息
    userStore.setUserInfo({
      token,
      rememberPhone: rememberMe.value,
      ...user,
    });

    ElMessage.success("登录成功");

    // 跳转到首页
    router.push("/home");
  } catch (error) {
    if (error.response && error.response.data) {
      ElMessage.error(error.response.data.message || "登录失败");
    } else if (!error.isAxiosError) {
      // 表单验证错误，不显示消息
    } else {
      ElMessage.error("服务器连接失败");
    }
  } finally {
    loading.value = false;
  }
};

// 页面加载时检查是否有记住的账号
onMounted(() => {
  const rememberedPhone = userStore.rememberPhone;
  if (rememberedPhone) {
    loginForm.phone = userStore.phone;
    rememberMe.value = true;
  }
});
</script>

<style style="less" scoped>
.login-container {
  width: 100%;
  height: 100%;
  /* background-image: url("../assets/png/login_bg.png"); */
  background: linear-gradient(to bottom right, #2050a8, #4a7bd6, #8098c7);
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.login-hint {
  color: white;
  margin-top: -207px;
  width: 530px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-weight: bold;
  font-size: 60px;
  color: #ffffff;
  line-height: 70px;
  position: relative;
}

.login-box {
  box-sizing: border-box;
  width: 530px;
  background: white;
  border-radius: 8px;
  padding: 45px 35px;
  margin-top: 57px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.login-title {
  font-weight: bold;
  font-size: 32px;
  color: var(--el-color-primary);
  line-height: 38px;
  text-align: center;
  margin-bottom: 30px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 30px;
}

.tab-item {
  flex: 1;
  text-align: center;
  font-size: 16px;
  padding: 12px 0;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.tab-item.active {
  color: #409eff;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #409eff;
}

.login-form {
  width: 100%;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  color: #606266;
}

.register-link {
  color: #409eff;
  text-decoration: none;
}

.register-link:hover {
  color: #66b1ff;
}

.login-button {
  width: 100%;
  height: 40px;
  font-size: 16px;
}

:deep(.el-input__wrapper) {
  padding: 1px 11px;
}

:deep(.el-input__prefix) {
  margin-right: 8px;
}
</style>
