import { TableColumnItem } from "@/types";

export const unitColumns: Array<TableColumnItem> = [
  { prop: "id", label: "单位编号", minWidth: 130 },
  { prop: "companyName", label: "单位名称", minWidth: 200 },
  { prop: "legalPerson", label: "法人姓名", minWidth: 120 },
  { prop: "contactPerson", label: "联系人", minWidth: 150 },
  { prop: "operationAreaLabel", label: "所属区域", minWidth: 120 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "memberCount", label: "成员数", minWidth: 100 },
  { prop: "operations", label: "操作", minWidth: 320, fixed: "right" },
];

// 新增报修人员列配置
export const staffColumns: Array<TableColumnItem> = [
  { prop: "userId", label: "编号", minWidth: 130 },
  { prop: "username", label: "人员姓名", minWidth: 120 },
  { prop: "unit", label: "所属单位", minWidth: 150 },
  { prop: "maskPhone", label: "联系电话", minWidth: 150 },
  { prop: "roles", label: "角色", minWidth: 180 },
  { prop: "status", label: "状态", minWidth: 100 },
  { prop: "createdAt", label: "注册时间", minWidth: 180 },
  { prop: "operations", label: "操作", minWidth: 120, fixed: "right" },
];
