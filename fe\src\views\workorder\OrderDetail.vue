<template>
  <div v-loading="actionLoading || loading" class="order-detail">
    <div class="order-detail-header">
      <el-button :icon="ArrowLeftBold" link @click="handleCancel">
        返回
      </el-button>
      <div class="right-actions">
        <template v-if="!status">
          <el-button type="primary" @click="handleCreate">保存</el-button>
        </template>
        <template v-else>
          <el-button v-if="canEdit" type="primary" @click="handleEdit">
            保存
          </el-button>
          <el-button
            v-if="
              workOrderDetail.status === WorkOrderStatusEnum.WAITING_DISPATCH &&
              userStore.userType !== UserTypeEnum.REPAIR_UNIT
            "
            type="primary"
            @click="handleDispatch(false, null)"
          >
            派单
          </el-button>
          <el-button
            v-if="canReassign"
            type="primary"
            @click="
              handleDispatch(true, {
                unit: workOrderDetail?.maintenanceUnitId || '',
                userId: workOrderDetail?.maintenanceUserId || '',
              })
            "
          >
            转派
          </el-button>

          <template
            v-if="
              workOrderDetail.status ===
                WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT &&
              userStore.userType === UserTypeEnum.PLATFORM
            "
          >
            <el-button
              v-if="
                workOrderDetail.status ===
                WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT
              "
              type="primary"
              @click="handlePlanAudit(true)"
            >
              审核通过
            </el-button>
            <el-button type="primary" @click="handlePlanAudit(false)">
              审核不通过
            </el-button>
          </template>

          <el-button
            v-if="
              workOrderDetail.status ===
                WorkOrderStatusEnum.WAITING_PLATFORM_FINISH_CONFIRM &&
              userStore.userType === UserTypeEnum.PLATFORM
            "
            :loading="completeLoading"
            type="primary"
            @click="handleComplete"
          >
            确认完成
          </el-button>
        </template>
      </div>
    </div>

    <div class="order-detail-content">
      <el-form
        ref="formRef"
        class="order-detail-form"
        :model="formData"
        label-suffix="："
        label-width="135px"
        :rules="formRules"
      >
        <!-- 基本信息 -->
        <div class="section-title">基本信息</div>
        <el-row v-if="workOrderId" :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单号">
              <span>{{ workOrderId }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工单状态">
              <el-tag :type="getWorkOrderStatus(status).type">
                {{ getWorkOrderStatus(status).label }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="报修单位" prop="reportUnitId">
          <el-select
            v-if="!status"
            v-model="formData.reportUnitId"
            placeholder="请选择"
            filterable
            :loading="reportUnitList.loading"
            @change="getReporterList"
          >
            <el-option
              v-for="item in reportUnitList.data"
              :key="item.id"
              :label="item.companyName"
              :value="item.id"
            />
          </el-select>
          <span v-else>{{
            workOrderDetail.reportEnterprise.companyName || "--"
          }}</span>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报修人" prop="reporterId">
              <el-select
                v-if="!status"
                v-model="formData.reporterId"
                placeholder="请选择"
                filterable
                :loading="reporterList.loading"
                @change="
                  formData.reporterPhone =
                    reporterList.data.find(
                      (item) => item.userId === formData.reporterId
                    )?.phone || ''
                "
              >
                <el-option
                  v-for="item in reporterList.data"
                  :key="item.userId"
                  :label="item.username"
                  :value="item.userId"
                />
              </el-select>
              <span v-else>{{
                workOrderDetail?.reporter?.username || "--"
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报修人联系方式" prop="reporterPhone">
              <el-input
                v-if="!status"
                v-model.trim="formData.reporterPhone"
                disabled
                placeholder="请选择报修人"
              />
              <span v-else>{{ workOrderDetail?.reporterPhone || "--" }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报修类型" prop="serviceClass">
              <el-select
                v-if="canEdit"
                v-model="formData.serviceClass"
                placeholder="请选择"
                :loading="serviceContentList.loading"
              >
                <el-option
                  v-for="item in serviceContentList.data"
                  :key="item.id"
                  :label="item.serviceClass"
                  :value="item.id"
                />
              </el-select>
              <span v-else>{{
                serviceContentList.data.find(
                  (item) => item.id === workOrderDetail?.serviceClass
                )?.serviceClass || "--"
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报修途径" prop="reportWay">
              <el-select
                v-if="!status"
                v-model="formData.reportWay"
                placeholder="请选择"
              >
                <el-option label="电话报修" :value="ReportWayEnum.PHONE" />
                <el-option
                  label="填报报修"
                  :value="ReportWayEnum.SELF_REPORT"
                />
              </el-select>
              <span v-else>{{
                formData.reportWay === ReportWayEnum.PHONE
                  ? "电话报修"
                  : "填报报修"
              }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="详细地点" prop="detailLocation">
          <el-input
            v-if="canEdit"
            v-model.trim="formData.detailLocation"
            placeholder="请输入"
          />
          <span v-else>{{ formData.detailLocation }}</span>
        </el-form-item>
        <el-form-item label="故障描述" prop="faultDesc">
          <el-input
            v-if="canEdit"
            v-model.trim="formData.faultDesc"
            type="textarea"
            placeholder="请输入"
          />
          <span v-else>{{ formData.faultDesc }}</span>
        </el-form-item>
        <el-form-item label="机器信息" prop="deviceInfo">
          <el-input
            v-if="canEdit"
            v-model.trim="formData.deviceInfo"
            placeholder="请输入"
          />
          <span v-else>{{ formData.deviceInfo }}</span>
        </el-form-item>

        <!-- 附件信息 -->
        <div class="section-title">附件信息</div>

        <el-form-item class="files-list-item" label="图片/视频">
          <div style="display: flex; flex-direction: column">
            <div v-if="canEdit" style="color: #aaa">请注意上传的数据中是否包含敏感信息</div>
            <div class="files-list">
              <div
                class="file-item"
                v-for="item in showFileList"
                :key="item.fileId"
              >
                <div class="thumb-container">
                  <BlobImage
                    v-if="isImage(item.fileType) && item.status === 'success'"
                    :file-id="item.fileId"
                  />

                  <BlobVideo
                    v-else-if="
                      isVideo(item.fileType) && item.status === 'success'
                    "
                    :file-id="item.fileId"
                  />
                  <el-icon
                    v-else-if="item.status === 'uploading'"
                    class="is-loading"
                    size="24"
                  >
                    <Loading />
                  </el-icon>

                  <el-icon
                    v-if="canEdit"
                    class="close-icon"
                    size="20"
                    @click="handleDeleteFile(item)"
                  >
                    <CircleCloseFilled />
                  </el-icon>
                </div>

                <OverflowTooltip :content="item.fileName"></OverflowTooltip>
              </div>
              <el-upload
                v-if="canEdit && showFileList.length < 9"
                action="#"
                multiple
                accept="image/*,video/*"
                :limit="9 - showFileList.length"
                :show-file-list="false"
                :auto-upload="false"
                :on-exceed="(e) => $message.warning('最多只能上传9个附件')"
                :on-change="uploadFile"
              >
                <div class="upload-button">
                  <el-icon size="24"><Plus /></el-icon>
                </div>
              </el-upload>
            </div>
          </div>
        </el-form-item>

        <!-- 派发信息 -->
        <template v-if="workOrderDetail?.maintenanceUnitId">
          <div class="section-title">派发信息</div>
          <el-form-item label="派发单位" prop="dispatchUnit">
            <span>{{
              workOrderDetail?.maintenanceEnterprise?.companyName || "--"
            }}</span>
          </el-form-item>
          <el-form-item label="派发维修人员" prop="dispatchPerson">
            <span>{{
              workOrderDetail?.maintenanceUser?.username || "--"
            }}</span>
          </el-form-item>
        </template>

        <!-- 处理方案 -->
        <template
          v-if="
            workOrderDetail?.processingOpinion ||
            workOrderDetail?.serviceItems.length > 0
          "
        >
          <div class="section-title">维修方案</div>

          <!-- 处理意见 -->
          <el-form-item label="结算方式" prop="reportUnitSettlementType">
            <span>{{ getSettlementType(reportUnitSettlementType) }}</span>
          </el-form-item>

          <!-- 处理意见 -->
          <el-form-item label="处理意见" prop="processingOpinion">
            <span>{{ workOrderDetail?.processingOpinion || "--" }}</span>
          </el-form-item>
        </template>

        <template
          v-if="
            workOrderDetail?.processingOpinion || workOrderDetail?.totalPrice
          "
        >
          <ServiceItemsTable
            v-model:workOrderDetail="workOrderDetail"
            :editable="
              workOrderDetail?.status ===
              WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT
            "
            :reportUnitSettlementType="reportUnitSettlementType"
          />
        </template>

        <template
          v-if="
            workOrderDetail?.processingOpinion || workOrderDetail?.totalPrice
          "
        >
          <div class="section-title">交通距离</div>

          <!-- 交通距离 -->
          <el-form-item label="交通距离">
            <template
              v-if="
                workOrderDetail?.status ===
                WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT
              "
            >
              <el-input-number
                v-model="formData.distance"
                :min="0"
                :precision="0"
                :controls="true"
                style="width: 200px"
              >
                <template #suffix>KM</template>
              </el-input-number>
            </template>
            <span v-else>{{ workOrderDetail?.distance || "0" }} KM</span>
          </el-form-item>
        </template>

        <template
          v-if="
            workOrderDetail?.processingOpinion || workOrderDetail?.totalPrice
          "
        >
          <div class="section-title">价格统计</div>

          <!-- 费用统计 -->
          <div class="fee-statistics">
            <div class="fee-item">
              <span class="fee-label">服务费</span>
              <span class="fee-value"
                >¥ {{ formatPrice(workOrderDetail?.serviceFee) }}</span
              >
            </div>
            <div class="fee-item">
              <span class="fee-label">交通服务费 (超出50KM，¥ 2.00/KM)</span>
              <span class="fee-value"
                >¥ {{ formatPrice(workOrderDetail?.transportFee) }}</span
              >
            </div>
            <div class="fee-item">
              <span class="fee-label">配件价格合计</span>
              <span class="fee-value">
                <template
                  v-if="
                    workOrderDetail?.status ===
                    WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT
                  "
                >
                  <el-input-number
                    :model-value="formData.partsTotal"
                    :min="0"
                    :controls="true"
                    style="width: 150px"
                    @input="
                      (value) => {
                        if (value >= 0)
                          formData.partsTotal = Number(value.toFixed(2));
                      }
                    "
                  />
                </template>
                <template v-else>
                  ¥ {{ formatPrice(workOrderDetail?.partsTotal) }}
                </template>
              </span>
            </div>
            <div class="fee-total">
              <span class="fee-label">合计</span>
              <span class="fee-value"
                >¥ {{ formatPrice(workOrderDetail?.totalPrice) }}</span
              >
            </div>
          </div>
        </template>

        <!-- 处理信息 -->
        <template v-if="processHistory && processHistory.length > 0">
          <div class="section-title">处理信息</div>
          <div class="process-timeline">
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in processHistory"
                :key="index"
                :timestamp="formatDatetime(item.processTime)"
                color="#409EFF"
              >
                <div class="timeline-content">
                  <div class="timeline-info">
                    <span>{{ item.processorName }}</span>
                    <span>{{ item.processType }}</span>
                    <span v-if="item.processResult">{{
                      item.processResult
                    }}</span>
                  </div>
                  <div v-if="showDetailInfo(item)" class="detail-info">
                    <div v-if="item.reason" class="reason">
                      {{ `原因：${item.reason}` }}
                    </div>
                    <div v-if="item.remark" class="remark">
                      {{ `备注：${item.remark}` }}
                    </div>
                    <div v-if="item.location" class="location">
                      {{ `地点：${item.location}` }}
                    </div>
                    <div v-if="item.repairResult" class="result">
                      {{ `维修结果：${item.repairResult}` }}
                    </div>
                    <div v-if="item.repairScore" class="repair-score">
                      {{ `评分：${item.repairScore}` }}
                    </div>
                    <div v-if="item.evaluation" class="evaluation">
                      {{ `评价：${item.evaluation}` }}
                    </div>
                    <div v-if="item.signatureBase64" class="signature">
                      <span>签名：</span>
                      <img :src="item.signatureBase64" alt="签名" />
                    </div>

                    <!-- 附件 -->
                    <div
                      v-if="item.attachments && item.attachments.length > 0"
                      class="attachments"
                    >
                      <span style="white-space: nowrap">附件：</span>
                      <div class="files-list">
                        <div
                          class="file-item"
                          v-for="attachment in item.attachments"
                          :key="attachment.fileId"
                        >
                          <div class="thumb-container">
                            <BlobImage
                              v-if="isImage(attachment.fileType)"
                              :file-id="attachment.fileId"
                            />

                            <BlobVideo
                              v-else-if="isVideo(attachment.fileType)"
                              :file-id="attachment.fileId"
                            />
                          </div>

                          <OverflowTooltip
                            :content="attachment.fileName"
                          ></OverflowTooltip>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </template>
      </el-form>
    </div>
    <DispatchDialog
      ref="dispatchDialogRef"
      :is-transfer="isTransfer"
      :work-order-id="workOrderId"
      @submit="getWorkOrderDetail"
    />

    <!-- 审核不通过弹窗 -->
    <CommonDialog
      title="审核不通过"
      v-model:visible="auditDialogVisible"
      :btn-loading="auditLoading"
      :confirm-callback="() => auditPlan(false)"
    >
      <el-form>
        <el-form-item label="审核不通过原因">
          <el-input v-model.trim="auditReason" type="textarea" />
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  ArrowLeftBold,
  Plus,
  CircleCloseFilled,
  Loading,
} from "@element-plus/icons-vue";
import {
  BlobImage,
  BlobVideo,
  OverflowTooltip,
  CommonDialog,
} from "@/base-components";
import {
  WorkOrderStatusEnum,
  ReportWayEnum,
  UserTypeEnum,
  SettlementTypeEnum,
} from "@/configs";
import {
  createWorkOrderApi,
  getCustomerUnitList,
  getCustomerUnitStaffList,
  getServiceContentApi,
  getWorkOrderDetailApi,
  editWorkOrderApi,
  uploadWorkOrderAttachmentApi,
  deleteFile,
  platformAuditApi,
  platformFinishConfirmApi,
  getRepairPrice,
} from "@/api";
import {
  companyNameValidator,
  Message,
  phoneNumberValidator,
  toastError,
  getWorkOrderStatus,
  formatDatetime,
  getSettlementType,
} from "@/utils";
import { cloneDeep } from "lodash";
import DispatchDialog from "./components/DispatchDialog.vue";
import ServiceItemsTable from "./components/ServiceItemsTable.vue";
import { useUserStore } from "@/store";

const route = useRoute();
const router = useRouter();

const userStore = useUserStore();
const formRef = ref();
const dispatchDialogRef = ref();
const isTransfer = ref(false);

const formData = ref({
  reportUnitId: "",
  detailLocation: "",
  reporterId: "",
  reporterPhone: "",
  serviceClass: "",
  reportWay: "",
  faultDesc: "",
  deviceInfo: "",
  attachments: [],
  serviceItems: [],
  distance: 0,
  serviceFee: 0,
  transportFee: 0,
  partsTotal: 0,
  totalPrice: 0,
});

const reportUnitSettlementType = computed(() => {
  return (
    workOrderDetail.value?.serviceItems?.[0]?.reportUnitSettlementType || ""
  );
});

/* ====================================== 详情获取 ====================================== */
const loading = ref(false);
// 工单状态
const status = ref("");
const workOrderId = ref("");
// 处理历史数据
const processHistory = ref([]);

const workOrderDetail = ref(null);

// 初始化数据
onMounted(async () => {
  workOrderId.value = route.query.workOrderId;
  if (workOrderId.value) {
    getWorkOrderDetail();
  }
});

// 监听交通距离、配件价格和服务项目的变化
watch(
  () => [
    formData.value.distance,
    formData.value.partsTotal,
    workOrderDetail.value?.serviceItems,
  ],
  async () => {
    if (
      workOrderDetail.value?.status ===
      WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT
    ) {
      await calculatePrice();
    }
  },
  { deep: true }
);

function getWorkOrderDetail() {
  loading.value = true;
  getWorkOrderDetailApi(workOrderId.value)
    .then((res) => {
      const data = res.data.data;
      for (let key in data) {
        if (key === "attachments") showFileList.value = cloneDeep(data[key]);
        formData.value[key] = data[key];
      }
      workOrderDetail.value = data;
      processHistory.value = data.repairProcess || [];
      showFileList.value.forEach((item) => {
        item.status = "success";
      });
      status.value = data.status;

      // 更新表单数据中的价格相关字段
      formData.value.distance = data.distance || 0;
      formData.value.serviceFee = data.serviceFee || 0;
      formData.value.transportFee = data.transportFee || 0;
      formData.value.partsTotal = data.partsTotal || 0;
      formData.value.totalPrice = data.totalPrice || 0;
      formData.value.serviceItems = data.serviceItems || [];
    })
    .catch((err) => {
      toastError(err, "获取工单详情失败");
    })
    .finally(() => {
      loading.value = false;
    });
}

/* ====================================== 权限判断 ====================================== */
// 是否可编辑
const canEdit = computed(() => {
  return (
    (!status.value ||
      status.value === WorkOrderStatusEnum.WAITING_DISPATCH ||
      status.value === WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH ||
      status.value === WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME ||
      status.value === WorkOrderStatusEnum.WAITING_REPAIR_PLAN) &&
    userStore.userType !== UserTypeEnum.MAINTENANCE_UNIT
  );
});

// 是否可转派
const canReassign = computed(() => {
  return (
    (status.value === WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH ||
      status.value === WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME) &&
    userStore.userType !== UserTypeEnum.REPAIR_UNIT
  );
});

function showDetailInfo(item) {
  return (
    item?.location ||
    item?.repairResult ||
    item?.repairScore ||
    item?.evaluation ||
    item?.remark ||
    item?.signatureBase64 ||
    item?.attachments?.length > 0
  );
}

/* ====================================== 动态选项获取 ====================================== */
onMounted(() => {
  getReportUnit();
  getServiceContent();
});

// 报修单位列表
const reportUnitList = reactive({
  data: [],
  loading: false,
});
function getReportUnit() {
  reportUnitList.loading = true;
  getCustomerUnitList({
    offset: 0,
    limit: 1000,
  })
    .then((res) => {
      reportUnitList.data = res.data.data.rows || [];
      reportUnitList.loading = false;
    })
    .catch((err) => {
      reportUnitList.loading = false;
      toastError(err, "获取报修单位失败");
    });
}

// 获取报修人列表
const reporterList = reactive({
  data: [],
  loading: false,
});
function getReporterList(reportUnitId) {
  reporterList.loading = true;
  getCustomerUnitStaffList({
    offset: 0,
    limit: 1000,
    unitId: reportUnitId,
    filters: "status=ENABLED",
    showMask: "false",
  })
    .then((res) => {
      reporterList.data = res.data.data.rows || [];
      reporterList.loading = false;
    })
    .catch((err) => {
      reporterList.loading = false;
      toastError(err, "获取报修人失败");
    });
}

// 获取服务类别
const serviceContentList = reactive({
  data: [],
  loading: false,
});
function getServiceContent() {
  serviceContentList.loading = true;
  getServiceContentApi()
    .then((res) => {
      serviceContentList.data = res.data.data || [];
      serviceContentList.loading = false;
    })
    .catch((err) => {
      serviceContentList.loading = false;
      toastError(err, "获取服务类别失败");
    });
}

/* ====================================== 文件上传 ====================================== */

const showFileList = ref([]);
// 是否是规定的上传类型(图片或视频)
function isImage(fileType) {
  return fileType.startsWith("image/");
}
function isVideo(fileType) {
  return fileType.startsWith("video/");
}

// 上传文件
const uploadFile = async (file, fileList) => {
  if (!isImage(file.raw.type) && !isVideo(file.raw.type)) {
    showFileList.value.push({
      uid: file.uid,
      fileId: "",
      fileName: file.name,
      fileType: file.raw.type,
      status: "error",
      errorMessage: "只支持上传图片或视频",
    });
    return;
  }
  showFileList.value.push({
    uid: file.uid,
    fileId: "",
    fileName: file.name,
    fileType: file.raw.type,
    status: "uploading",
  });

  try {
    // 使用文件的raw属性，这是实际的File对象
    const res = await uploadWorkOrderAttachmentApi(file.raw);

    if (res.data && res.data.fileId) {
      // 添加到文件列表
      formData.value.attachments.push({
        fileName: file.name,
        fileId: res.data.fileId,
        fileType: file.raw.type,
      });
      const index = showFileList.value.findIndex(
        (item) => item.uid === file.uid
      );
      if (index !== -1) {
        showFileList.value[index].status = "success";
        showFileList.value[index].fileId = res.data.fileId;
      }
    }
  } catch (error) {
    const index = showFileList.value.findIndex((item) => item.uid === file.uid);
    if (index !== -1) showFileList.value[index].status = "error";
    console.error("文件上传失败:", error);
  }
};

// 删除文件
const deleteFileLoading = ref([]);
const handleDeleteFile = async (file) => {
  try {
    //新上传的删除
    if (file.fileId) deleteFileLoading.value.push(file.fileId);
    else deleteFileLoading.value.push(file.uid);
    if (file.uid && file.fileId) {
      await deleteFile([file.fileId]);
    }
    showFileList.value = showFileList.value.filter(
      (item) =>
        item.fileId !== file.fileId &&
        ((item.uid && item.uid !== file.uid) || !item.uid)
    );
    formData.value.attachments = formData.value.attachments.filter(
      (item) =>
        item.fileId !== file.fileId &&
        ((item.uid && item.uid !== file.uid) || !item.uid)
    );
  } catch (error) {
    toastError(error, "删除文件失败");
  } finally {
    deleteFileLoading.value = deleteFileLoading.value.filter(
      (item) => item !== file.fileId && item !== file.uid
    );
  }
};
/* ====================================== 表单操作 ====================================== */
const actionLoading = ref(false);
// 表单数据

// 表单校验规则
const formRules = {
  reportUnitId: [
    { required: true, message: "请选择报修单位", trigger: "change" },
    { validator: companyNameValidator, trigger: "change" },
  ],
  reporter: [{ required: true, message: "请选择报修人", trigger: "change" }],
  detailLocation: [
    { required: true, message: "请输入详细地点", trigger: "blur" },
    { max: 200, message: "详细地点最多输入200个字符", trigger: "blur" },
  ],
  reporterId: [{ required: true, message: "请选择报修人", trigger: "blur" }],
  reporterPhone: [
    { required: true, message: "请输入联系方式", trigger: "blur" },
    { validator: phoneNumberValidator, trigger: "blur" },
  ],
  serviceClass: [
    { required: true, message: "请选择报修类型", trigger: "change" },
  ],
  reportWay: [{ required: true, message: "请选择报修途径", trigger: "change" }],
  faultDesc: [
    { required: true, message: "请输入故障描述", trigger: "blur" },
    { max: 200, message: "故障描述最多输入200个字符", trigger: "blur" },
  ],
  deviceInfo: [
    { max: 200, message: "机器信息最多输入200个字符", trigger: "blur" },
  ],
};
// 提交表单
const handleCreate = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (!valid) return;
    actionLoading.value = true;
    createWorkOrderApi({ ...formData.value, source: "PLATFORM" })
      .then((res) => {
        actionLoading.value = false;
        Message.success("创建成功");
        router.back();
      })
      .catch((err) => {
        actionLoading.value = false;
        toastError(err, "创建失败");
      });
  });
};

// 取消
const handleCancel = () => {
  router.back();
};

// 修改
const handleEdit = () => {
  actionLoading.value = true;
  editWorkOrderApi({
    workOrderId: workOrderId.value,
    ...formData.value,
  })
    .then((res) => {
      Message.success("修改成功");
      getWorkOrderDetail();
    })
    .catch((err) => {
      toastError(err, "修改失败");
    })
    .finally(() => {
      actionLoading.value = false;
    });
};

/* ====================================== 工单流转 ====================================== */

// 派单
const handleDispatch = (type, data = null) => {
  isTransfer.value = type;
  dispatchDialogRef.value?.openDialog(data);
};

// 平台审核
const auditDialogVisible = ref(false);
const auditReason = ref("");
const auditLoading = ref(false);
const handlePlanAudit = (isPass) => {
  if (!isPass) {
    auditDialogVisible.value = true;
    auditReason.value = "";
  } else {
    auditPlan(isPass);
  }
};

// 审核
function auditPlan(isPass) {
  if (!auditReason.value && !isPass) {
    Message.error("请输入审核原因");
    return;
  }
  auditLoading.value = true;
  platformAuditApi({
    workOrderId: workOrderId.value,
    isPass,
    auditReason: auditReason.value,
    serviceItems: workOrderDetail.value.serviceItems,
    partsTotal: formData.value.partsTotal,
    distance: formData.value.distance,
  })
    .then((res) => {
      Message.success("审核成功");
      auditDialogVisible.value = false;
      getWorkOrderDetail();
    })
    .catch((err) => {
      toastError(err, "审核失败");
    })
    .finally(() => {
      auditLoading.value = false;
    });
}

// 确认完成
const completeLoading = ref(false);
const handleComplete = () => {
  completeLoading.value = true;
  platformFinishConfirmApi({
    workOrderId: workOrderId.value,
  })
    .then((res) => {
      Message.success("确认完成成功");
      getWorkOrderDetail();
    })
    .catch((err) => {
      toastError(err, "确认完成失败");
    })
    .finally(() => {
      completeLoading.value = false;
    });
};

// 维修完成
const handleRepairComplete = () => {
  // TODO: 实现维修完成逻辑
};

// 新增处理方法
const handleCompanyDispatch = async () => {
  // TODO: 实现维保公司派单逻辑
};

const handleConfirmRepair = async () => {
  // TODO: 实现确认维修完成逻辑
};

const handlePlatformConfirm = async () => {
  // TODO: 实现平台确认完成逻辑
};

// 新增驳回方法
const handleReject = () => {
  // TODO: 实现驳回逻辑
};

// 格式化价格
const formatPrice = (price) => {
  if (price === undefined || price === null) return "0.00";
  return Number(price).toFixed(2);
};

// 计算价格
const calculatePrice = async () => {
  try {
    console.log(workOrderDetail.value.serviceItems);
    // 调用API获取最新价格
    const res = await getRepairPrice({
      serviceItems: workOrderDetail.value.serviceItems,
      settlementType:
        workOrderDetail.value.serviceItems[0]?.reportUnitSettlementType || "",
      distance: formData.value.distance || 0,
      workOrderId: workOrderId.value,
      partsTotal: formData.value.partsTotal || 0,
    });

    const data = res.data.data;

    // 更新服务费和交通费
    workOrderDetail.value.serviceFee = data.serviceFee || 0;
    workOrderDetail.value.transportFee = data.transportFee || 0;

    // 更新表单数据
    formData.value.serviceFee = workOrderDetail.value.serviceFee;
    formData.value.transportFee = workOrderDetail.value.transportFee;

    // 根据状态计算总价
    if (
      workOrderDetail.value.status ===
      WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT
    ) {
      // 待平台审核状态：总价 = 服务费 + 交通费 + 配件费
      workOrderDetail.value.totalPrice =
        (workOrderDetail.value.serviceFee || 0) +
        (workOrderDetail.value.transportFee || 0) +
        (formData.value.partsTotal || 0);

      // 更新表单数据
      formData.value.totalPrice = workOrderDetail.value.totalPrice;
    }
    // 其他状态不更新总价，保持原值
  } catch (error) {
    toastError(error, "计算价格失败");
  }
};
</script>

<style lang="less" scoped>
.order-detail {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .order-detail-header {
    padding: 10px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    flex-shrink: 0;
  }

  .order-detail-content {
    padding: 0 20px 20px;
    box-sizing: border-box;
    flex: 1;
    min-height: 0px;
    overflow-y: auto;
    .order-detail-form {
      .files-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .file-item {
          position: relative;
          width: 100px;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          gap: 5px;
          &:hover {
            border-color: var(--el-color-primary-light-3);
            color: var(--el-color-primary-light-3);
          }

          .thumb-container {
            width: 100px;
            height: 100px;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 5px;
            box-sizing: border-box;
            .close-icon {
              position: absolute;
              top: -8px;
              right: -8px;
              cursor: pointer;
              color: var(--el-color-danger);
              background-color: #fff;
              border-radius: 50%;
            }
          }
        }
      }
      .upload-button {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
        width: 100px;
        border-radius: 4px;
        border: 1px solid #ccc;
        color: #ccc;
        &:hover {
          border-color: var(--el-color-primary-light-3);
          color: var(--el-color-primary-light-3);
        }
      }
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0;
      padding-left: 10px;
      border-left: 4px solid #409eff;
    }
  }
}

.process-timeline {
  padding: 20px;

  .timeline-content {
    .timeline-info {
      margin-bottom: 8px;

      span {
        margin-right: 8px;
      }
    }

    .detail-info {
      margin-top: 10px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      .attachments {
        display: flex;
      }
    }

    .signature {
      margin-top: 10px;

      img {
        max-width: 100px;
        max-height: 50px;
        object-fit: contain;
        background: #fff;
        padding: 5px;
        border-radius: 4px;
      }
    }

    .process-images {
      margin-top: 10px;
      display: flex;
      gap: 10px;

      .el-image {
        width: 100px;
        height: 100px;
        border-radius: 4px;
      }
    }
  }
}

.settlement-type-info {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fdf6ec;
  color: #e6a23c;
  border-radius: 4px;
  font-size: 14px;
}

.fee-statistics {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .fee-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .fee-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px dashed #ebeef5;

    .fee-label {
      color: #606266;
    }

    .fee-value {
      color: #303133;
    }
  }

  .fee-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0 0;
    margin-top: 10px;

    .fee-label {
      font-size: 16px;
      font-weight: bold;
    }

    .fee-value {
      font-size: 18px;
      color: #f56c6c;
      font-weight: bold;
    }
  }
}
</style>
