const shell = require("shelljs");
const chalk = require("chalk");
const moment = require("moment");

const versionTag = process.argv.slice(2)[0] || moment().format("YYYYMMDD-HHmm");

const remoteHub = "registry.cn-chengdu.aliyuncs.com/signit-fe";
const imageName = `jishixiu-server:${versionTag}`;

// 构建并发布docker镜像
const buildImageStart = Date.now();
console.log(chalk.blue("===开始生成镜像==="));
shell.exec(`docker build -t ${imageName} .`);
console.log(chalk.blue("===镜像构建完成，开始发布==="));
shell.exec(`docker tag ${imageName} ${remoteHub}/${imageName}`);
shell.exec(`docker push ${remoteHub}/${imageName}`);
shell.exec(`docker rmi ${imageName}`);
console.log(chalk.blue("===镜像发布完成==="));
console.log(
  chalk.green("镜像发布成功，耗时", (Date.now() - buildImageStart) / 1000, "秒")
);
