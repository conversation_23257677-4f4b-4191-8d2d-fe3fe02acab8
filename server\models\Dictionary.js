const mongoose = require("mongoose");
const dictionarySchema = new mongoose.Schema(
  {
    groupLabel: {
      type: String,
      required: true,
    },
    groupValue: {
      type: String,
      unique: true,
    },
    children: [
      {
        _id: false,
        label: {
          type: String,
          required: true,
        },
        value: {
          type: String,
          required: true,
          unique: true,
        },
      },
    ],
  },
  {
    timestamps: true,
  }
);

const Dictionary = mongoose.model("Dictionary", dictionarySchema);

module.exports = Dictionary;
