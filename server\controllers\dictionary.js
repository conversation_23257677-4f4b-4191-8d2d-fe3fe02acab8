const Dictionary = require("../models/Dictionary");

// 获取字典列表
const getDictionaryList = async (req, res) => {
  try {
    const { groupValues } = req.query;
    const groupValuesArray = groupValues.split(",");
    const dictionary = await Dictionary.find(
      {
        groupValue: { $in: groupValuesArray },
      },
      {
        groupValue: 1,
        children: 1,
      }
    ).sort({ updatedAt: -1 });
    res.json({
      code: 200,
      message: "获取字典列表成功",
      data: dictionary,
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: "获取字典列表失败",
    });
  }
};

module.exports = {
  getDictionaryList,
};
