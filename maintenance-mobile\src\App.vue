<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

onLaunch(() => {
  console.log("App Launch")

  // 刷新时获取当前页面路由
  const route = window?.location?.hash?.substring(1).split("?")?.[0]
  if (route) {
    checkRoute(route)
  }

  // // 添加路由拦截
  uni.addInterceptor("navigateTo", {
    invoke(e) {
      return checkRoute(e.url)
    }
  })

  uni.addInterceptor("switchTab", {
    invoke(e) {
      return checkRoute(e.url)
    }
  })
})

function checkRoute(url: string) {
  const token = userStore.token
  const withoutTokenPage = ["/", "pages/login/login", "pages/login/forget-password"]
  const loginPage = ["/", "pages/login/login"]

  const isLoginPage = withoutTokenPage.find(path => (path === "/" ? path === url : url.includes(path)))

  if (!token && !isLoginPage) {
    uni.redirectTo({
      url: "/pages/login/login"
    })
    return false
  }

  if (token && loginPage.find(path => (path === "/" ? path === url : url.includes(path)))) {
    uni.switchTab({
      url: "/pages/home/<USER>"
    })
    return false
  }

  return true
}

onShow(() => {
  console.log("App Show")
})
onHide(() => {
  console.log("App Hide")
})
</script>

<style lang="scss">
/* 全局样式 */
page {
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    "Helvetica Neue",
    Helvetica,
    Segoe UI,
    Arial,
    Roboto,
    "PingFang SC",
    "miui",
    "Hiragino Sans GB",
    "Microsoft Yahei",
    sans-serif;
}

/* 去除默认边距 */
uni-page-body {
  height: 100%;
}

/* 溢出文本处理 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本溢出 */
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
</style>
