<template>
  <div class="category-root">
    <div class="category-header">
      <div class="category-header-title">
        <span>类别设置</span>
      </div>
      <div class="category-header-action">
        <el-button
          type="primary"
          :icon="Plus"
          @click="categoryDialogRef?.open('add')"
        >
          新增类别
        </el-button>
      </div>
    </div>
    <div class="category-container">
      <div v-loading="categoryLoading" class="category-content">
        <TreeTable :columns="categoryColumns" :data="categoryList">
          <template #operations="{ row }">
            <el-button
              link
              type="primary"
              @click="categoryDialogRef?.open('add', row)"
            >
              添加分类
            </el-button>
            <el-button
              link
              type="primary"
              @click="categoryDialogRef?.open('edit', row)"
            >
              编辑
            </el-button>
            <el-button link type="primary"> 上移 </el-button>
            <el-button link type="primary"> 下移 </el-button>
            <el-button link type="primary"> 删除 </el-button>
          </template>
        </TreeTable>
      </div>
    </div>
    <CategoryDialog ref="categoryDialogRef" @refresh="getCategoryList" />
  </div>
</template>

<script setup>
import { SearchContainer, TreeTable } from "@/base-components";
import { onMounted, reactive, ref } from "vue";
import { Plus, ArrowDown, ArrowRight } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import CategoryDialog from "./components/CategoryDialog.vue";
import { getSuppliesClassTreeApi } from "@/api";

// 搜索表单
const searchForm = reactive({
  name: "",
  code: "",
});

const requestParams = reactive({
  filters: "",
});

// 表格列配置
const categoryColumns = [
  {
    prop: "name",
    label: "名称",
    minWidth: "300px",
  },
  {
    prop: "code",
    label: "编码",
    minWidth: "150px",
  },
  {
    prop: "remark",
    label: "备注",
    minWidth: "300px",
  },
  {
    prop: "operations",
    label: "操作",
    minWidth: "260px",
    fixed: "right",
  },
];

const categoryLoading = ref(false);
const categoryList = ref([]);

const getCategoryList = async (params) => {
  categoryLoading.value = true;
  getSuppliesClassTreeApi()
    .then((res) => {
      categoryList.value = res.data.data;
      categoryLoading.value = false;
    })
    .catch((err) => {
      categoryLoading.value = false;
      toastError(err, "获取类别列表失败");
    });
};

onMounted(() => {
  getCategoryList();
});

/* ===================================== 操作 ===================================== */
const categoryDialogRef = ref();
</script>

<style lang="less" scoped>
.category-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;
  }

  .category-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .category-content {
      margin-top: 10px;
      flex: 1;
      min-height: 0px;
    }
  }

  .category-name {
    display: flex;
    align-items: center;
    gap: 8px;

    .expand-icon {
      font-size: 12px;
      color: #909399;
      cursor: pointer;
    }
  }
}
</style>
