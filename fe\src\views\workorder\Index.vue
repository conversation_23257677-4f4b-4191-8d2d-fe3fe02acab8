<template>
  <div class="work-order-root">
    <div class="work-order-header">
      <div class="work-order-header-title">
        <span>工单管理</span>
      </div>
      <div class="work-order-header-action">
        <el-button
          v-if="userStore.userType === UserTypeEnum.PLATFORM"
          type="primary"
          :icon="Plus"
          @click="$router.push('/workorder/detail')"
        >
          新增
        </el-button>
      </div>
    </div>
    <div class="work-order-container">
      <div class="work-order-search">
        <SearchContainer
          @queryBtnClick="handleSearch"
          @resetBtnClick="handleReset"
        >
          <el-form-item label="工单号">
            <el-input
              v-model.trim="searchForm.workOrderId"
              placeholder="请输入工单号"
            />
          </el-form-item>
          <DateRangeFormItem label="报修时间" v-model="searchForm.reportTime" />
          <el-form-item label="工单状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态">
              <el-option label="全部" value="" />
              <el-option
                v-for="item in workOrderStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="
              userStore.userType === UserTypeEnum.REPAIR_UNIT
                ? '报修人'
                : '报修单位/报修人'
            "
          >
            <el-input
              v-model.trim="searchForm.reporter"
              placeholder="请输入报修单位/报修人"
            />
          </el-form-item>
          <el-form-item
            :label="
              userStore.userType === UserTypeEnum.MAINTENANCE_UNIT
                ? '维修单位'
                : '维修单位/维修人'
            "
          >
            <el-input
              v-model.trim="searchForm.repair"
              placeholder="请输入维保单位/维保人"
            />
          </el-form-item>
        </SearchContainer>
      </div>
      <div class="work-order-content">
        <CommonTable
          ref="tableRef"
          :columns="workOrderColumns"
          :requestApi="getWorkOrderListApi"
          :dataCallback="dataCallback"
          :requestParams="requestParams"
          :canRefresh="canRefresh"
        >
          <template #reportTime="{ row }">
            {{ formatDatetime(row.reportTime) }}
          </template>
          <template #status="{ row }">
            <el-tag :type="getWorkOrderStatus(row.status).type">
              {{ getWorkOrderStatus(row.status).label }}
            </el-tag>
          </template>
          <template #operations="{ row }">
            <el-button
              link
              type="primary"
              @click="
                $router.push(`/workorder/detail?workOrderId=${row.workOrderId}`)
              "
            >
              查看
            </el-button>
            <el-button
              v-if="canDispatch(row.status)"
              link
              type="primary"
              @click="handleDispatch(row.workOrderId, false, null)"
            >
              派单
            </el-button>
            <el-button
              v-if="canTransfer(row.status)"
              link
              type="primary"
              @click="
                handleDispatch(row.workOrderId, true, {
                  unit: row.maintenanceUnitId || '',
                  userId: row.maintenanceUserId || '',
                })
              "
            >
              转派
            </el-button>
          </template>
        </CommonTable>
      </div>
    </div>

    <DispatchDialog
      ref="dispatchDialogRef"
      :is-transfer="isTransfer"
      :work-order-id="workOrderId"
      @submit="tableRef?.refreshTableData()"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import {
  SearchContainer,
  DateRangeFormItem,
  CommonTable,
} from "@/base-components";
import { Plus } from "@element-plus/icons-vue";
import { workOrderColumns } from "./config";
import {
  WorkOrderStatusEnum,
  UserTypeEnum,
  workOrderStatusOptions,
} from "@/configs";
import { useUserStore } from "@/store";
import { getWorkOrderListApi } from "@/api";
import { formatDatetime, getWorkOrderStatus } from "@/utils";
import DispatchDialog from "./components/DispatchDialog.vue";
const userStore = useUserStore();
const router = useRouter();

const tableRef = ref();
const dispatchDialogRef = ref();

/* ======================================= 列表操作权限 ======================================= */

const canDispatch = (status) => {
  return (
    (userStore.userType === UserTypeEnum.PLATFORM &&
      status === WorkOrderStatusEnum.WAITING_DISPATCH) ||
    (userStore.userType === UserTypeEnum.MAINTENANCE_UNIT &&
      status === WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH)
  );
};

const canTransfer = (status) => {
  return (
    status === WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME ||
    (userStore.userType === UserTypeEnum.PLATFORM &&
      status === WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH)
  );
};

/* ======================================= 列表数据 ======================================= */

const canRefresh = ref(false);
const requestParams = reactive({
  filters: "",
  reporter: "",
  repair: "",
});

onMounted(() => {
  handleReset();
  if (router.currentRoute.value.query.status) {
    searchForm.status = router.currentRoute.value.query.status as string;
    requestParams.filters = `status=${router.currentRoute.value.query.status}`;
  }
  canRefresh.value = true;
});

function dataCallback(res) {
  return {
    tableRows: res.data?.data?.rows || [],
    total: res.data?.data?.pageElements?.totalElements || 0,
  };
}

/* ======================================= 搜索 ======================================= */
const searchForm = reactive({
  workOrderId: "",
  status: "",
  reportTime: "",
  reporter: "",
  repair: "",
});
// 搜索
function handleSearch() {
  const filters = [] as Array<string>;
  if (userStore.userType === UserTypeEnum.MAINTENANCE_UNIT)
    filters.push(`maintenanceUnitId=${userStore.unit}`);
  else if (userStore.userType === UserTypeEnum.REPAIR_UNIT)
    filters.push(`reportUnitId=${userStore.unit}`);

  for (let key in searchForm) {
    if (key === "reporter" || key === "repair") continue;
    else if (key === "reportTime" && searchForm[key].length === 2)
      filters.push(
        `reportTime>${new Date(searchForm[key][0]).setHours(
          0,
          0,
          0,
          0
        )},reportTime<${new Date(searchForm[key][1]).setHours(23, 59, 59, 999)}`
      );
    else if (searchForm[key]) filters.push(`${key}=${searchForm[key]}`);
  }
  requestParams.filters = filters.join(",");
  requestParams.reporter = searchForm.reporter;
  requestParams.repair = searchForm.repair;
}

// 重置
function handleReset() {
  for (let key in searchForm) {
    searchForm[key] = "";
  }
  requestParams.reporter = "";
  requestParams.repair = "";
  if (userStore.userType === UserTypeEnum.MAINTENANCE_UNIT)
    requestParams.filters = `maintenanceUnitId=${userStore.unit}`;
  else if (userStore.userType === UserTypeEnum.REPAIR_UNIT)
    requestParams.filters = `reportUnitId=${userStore.unit}`;
  else requestParams.filters = "";
}

/* ======================================= 列表操作 ======================================= */
const workOrderId = ref("");
const isTransfer = ref(false);
function handleDispatch(orderId, type, data) {
  workOrderId.value = orderId;
  isTransfer.value = type;
  dispatchDialogRef.value.openDialog(data);
}
</script>

<style lang="less" scoped>
.work-order-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  .work-order-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;
  }
  .work-order-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .work-order-search {
      flex-shrink: 0;
      background: #f5f5f5;
      border-radius: 10px;
      padding: 0px 20px;
      box-sizing: border-box;
    }
    .work-order-content {
      margin-top: 10px;
      flex: 1;
      min-height: 0px;
    }
  }
}
</style>
