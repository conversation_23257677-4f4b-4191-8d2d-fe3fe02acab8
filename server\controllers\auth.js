const { registerUser } = require("../utils/user-utils");
const { PlatformUser } = require("../models/PlatformUser");
const { MaintenanceUser } = require("../models/MaintenanceUser");
const { RepairUser } = require("../models/RepairUser");
const { MaintenanceEnterprise } = require("../models/MaintenanceEnterprise");
const { RepairEnterprise } = require("../models/RepairEnterprise");

// @desc    注册用户
// @route   POST /api/auth/register
// @access  公开
exports.register = async (req, res) => {
  try {
    const { username, phone, password, userType, unitId, roles } = req.body;

    if (userType !== "PLATFORM") {
      const Unit =
        userType === "MAINTENANCE_UNIT"
          ? MaintenanceEnterprise
          : RepairEnterprise;
      const unit = await Unit.findOne({ id: unitId });
      if (!unit) {
        return res.status(400).json({
          success: false,
          message: "单位不存在",
        });
      }
      if (roles.length === 0) {
        return res.status(400).json({
          success: false,
          message: "请选择角色",
        });
      }
    }

    const user = await registerUser({
      username,
      phone,
      password,
      userType,
      unit: unitId,
      roles,
    });

    if (!user.success) {
      return res.status(400).json({
        success: false,
        message: user.message,
      });
    }

    res.status(201).json({
      success: true,
      user: user.data,
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: err.message,
    });
  }
};

// @desc    通过邀请码注册报修端用户
// @route   POST /api/auth/register
// @access  公开
exports.registerClientUserByCode = async (req, res) => {
  try {
    const { username, phone, password, code } = req.body;
    const enterprise = await RepairEnterprise.findOne({
      inviteCode: { $elemMatch: { code: code } },
    });
    if (!enterprise)
      return res.status(500).json({
        success: false,
        message: "该邀请码不存在",
      });

    const tempCode = enterprise.inviteCode.find((item) => item.code === code);
    const user = await registerUser({
      username,
      phone,
      password,
      userType: "REPAIR_UNIT",
      unit: enterprise.id,
      roles: tempCode.roles,
    });

    if (!user.success) {
      return res.status(400).json({
        success: false,
        message: user.message,
      });
    }

    res.status(201).json({
      success: true,
      user: user.data,
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: err.message,
    });
  }
};

// @desc    登录用户
// @route   POST /api/auth/login
// @access  公开
exports.login = async (req, res) => {
  try {
    const { phone, password, userType, source } = req.body;

    if (!phone || !password || !userType) {
      return res.status(400).json({
        success: false,
        message: "请提供手机号、密码和用户类型",
      });
    }

    // 根据用户类型选择对应的模型
    let UserModel, EnterpriseModel;
    switch (userType) {
      case "PLATFORM":
        UserModel = PlatformUser;
        break;
      case "MAINTENANCE_UNIT":
        UserModel = MaintenanceUser;
        EnterpriseModel = MaintenanceEnterprise;
        break;
      case "REPAIR_UNIT":
        UserModel = RepairUser;
        EnterpriseModel = RepairEnterprise;

        break;
      default:
        return res.status(400).json({
          success: false,
          message: "无效的用户类型",
        });
    }

    let user = await UserModel.findOne({ phone, userType }).select("+password");

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "账号不存在",
      });
    }

    // 验证密码
    const isMatch = await user.matchPassword(password);

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: "密码错误",
      });
    }

    // 账号未启用不允许登录
    if (user.status !== "ENABLED") {
      return res.status(401).json({
        success: false,
        message: "账号不存在或已禁用",
      });
    }

    //管理平台登录非平台方要校验角色权限(source不存在时默认是管理平台)
    if ((!source || source === "PLATFORM") && user.userType !== "PLATFORM") {
      if (!user.roles.includes("管理员")) {
        return res.status(401).json({
          success: false,
          message: "无权限访问",
        });
      }
    }
    //小程序登录
    if (
      (source === "REPAIR_APP" && user.userType !== "REPAIR_UNIT") ||
      (source === "MAINTENANCE_APP" && user.userType !== "MAINTENANCE_UNIT")
    ) {
      return res.status(401).json({
        success: false,
        message: "无权限访问",
      });
    }

    // 创建token
    const token = user.getSignedJwtToken();

    user = user.toObject();

    // 非平台方需要获取企业信息
    if (userType !== "PLATFORM") {
      const enterprise = await EnterpriseModel.findOne({ id: user.unit });
      user.companyName = enterprise?.companyName || "";
    }

    res.status(200).json({
      success: true,
      token,
      user,
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: err.message,
    });
  }
};

// @desc    获取当前登录用户
// @route   GET /api/auth/me
// @access  私有
exports.getMe = async (req, res) => {
  try {
    let UserModel;
    switch (req.user.userType) {
      case "PLATFORM":
        UserModel = PlatformUser;
        break;
      case "MAINTENANCE_UNIT":
        UserModel = MaintenanceUser;
        break;
      case "REPAIR_UNIT":
        UserModel = RepairUser;
        break;
    }

    const user = await UserModel.findOne({
      userId: req.user.userId,
      userType: req.user.userType,
    });

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: err.message,
    });
  }
};

// 退出登录
exports.logout = async (req, res) => {
  try {
    // 清除cookie中的token
    res.clearCookie("token");

    res.status(200).json({
      success: true,
      message: "退出成功",
      clearToken: true, // 告诉前端清除localStorage或sessionStorage中的token
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: err.message,
    });
  }
};
// 根据旧密码修改密码
exports.updatePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword, phone, userType } = req.body;
    let UserModel;
    switch (userType) {
      case "PLATFORM":
        UserModel = PlatformUser;
        break;
      case "MAINTENANCE_UNIT":
        UserModel = MaintenanceUser;
        break;
      case "REPAIR_UNIT":
        UserModel = RepairUser;
        break;
    }
    const user = await UserModel.findOne({
      phone,
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "账号不存在",
      });
    }

    // 验证旧密码
    const isMatch = await user.matchPassword(oldPassword);
    if (!isMatch) {
      return res.status(500).json({
        success: false,
        message: "旧密码错误",
      });
    }

    // 更新密码
    user.password = newPassword;
    await user.save();

    res.status(200).json({
      success: true,
      message: "密码更新成功",
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "服务器错误",
      error: err.message,
    });
  }
};
