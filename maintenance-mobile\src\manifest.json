{
    "name" : "即时修维保端",
    "appid" : "__UNI__2695205",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Camera" : {},
            "Contacts" : {},
            "VideoPlayer" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "上传附件",
                    "NSPhotoLibraryAddUsageDescription" : "附件显示",
                    "NSCameraUsageDescription" : "拍照，上传附件",
                    "NSContactsUsageDescription" : "拨打电话",
                    "NSLocalNetworkUsageDescription" : "联网"
                },
                "idfa" : false,
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {},
            "icons" : {
                "android" : {
                    "hdpi" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/android-icon/icon-hdpi.png",
                    "xhdpi" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/android-icon/icon-xhdpi.png",
                    "xxhdpi" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/android-icon/icon-xxhdpi.png",
                    "xxxhdpi" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/android-icon/icon-xxxhdpi.png"
                },
                "ios" : {
                    "iphone" : {
                        "app@2x" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/ios-icon/icon-2x.png",
                        "app@3x" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/ios-icon/icon-3x.png",
                        "spotlight@2x" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/ios-icon/icon-2x.png",
                        "spotlight@3x" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/ios-icon/icon-3x.png",
                        "settings@2x" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/ios-icon/icon-2x.png",
                        "settings@3x" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/ios-icon/icon-3x.png",
                        "notification@2x" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/ios-icon/icon-2x.png",
                        "notification@3x" : "C:/Users/<USER>/Desktop/code/即时修/jishixiu/ios-icon/icon-3x.png"
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3"
}
