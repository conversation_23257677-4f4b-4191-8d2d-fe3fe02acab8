import axios from "./axios-instance";
// 获取工单列表
export function getWorkOrderListApi(params) {
  return axios.get("/api/workorder/work-order-list", {
    params,
  });
}

// 获取工单详情
export function getWorkOrderDetailApi(id) {
  return axios.get(`/api/workorder/work-order-detail?workOrderId=${id}`);
}

// 创建工单
export function createWorkOrderApi(data) {
  return axios.post("/api/workorder/create-work-order", data, {
    headers: {
      checkSensitive: true,
    },
  });
}

// 编辑工单
export function editWorkOrderApi(data) {
  return axios.put(`/api/workorder/edit-work-order`, data, {
    headers: {
      checkSensitive: true,
    },
  });
}

// 派单/转派
export function dispatchWorkOrderApi(data) {
  return axios.post(`/api/workorder/dispatch`, data);
}

// 更新工单状态
export function updateWorkOrderStatusApi(id, status) {
  return axios.put(`/api/workorder/${id}/status`, { status });
}

// 上传附件
export function uploadWorkOrderAttachmentApi(file) {
  const formData = new FormData();
  formData.append("file", file);
  return axios.post("/api/workorder/upload-work-order-attachment", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 平台审核方案
export function platformAuditApi(data) {
  return axios.post("/api/workorder/platform-audit", data);
}

// 平台方确认完成
export function platformFinishConfirmApi(data) {
  return axios.post("/api/workorder/platform-finish-confirm", data);
}

// 获取维保方价格
export function getRepairPrice(data) {
  return axios.post("/api/workorder/get-repair-price", data);
}
