<template>
  <view class="home-container">
    <NavBar title="首页" :show-back="false" />
    <scroll-view
      class="page-content-with-nav"
      scroll-y
      @scrolltolower="loadMore"
      @refresherrefresh="onRefresh"
      refresher-enabled
      :refresher-triggered="isRefreshing">
      <!-- 工单状态统计 -->
      <view class="stats-grid">
        <view class="stats-item">
          <view class="stats-number">{{ workOrderCount.WAITING_REPAIR_PERSON_COME || 0 }}</view>
          <view class="stats-label">待上门</view>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <view class="stats-number">{{ workOrderCount.WAITING_REPAIR_PLAN || 0 }}</view>
          <view class="stats-label">待处理维修方案</view>
        </view>
        <view class="stats-divider horizontal"></view>
        <view class="stats-item">
          <view class="stats-number">{{ workOrderCount.PENDING || 0 }}</view>
          <view class="stats-label">处理中</view>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <view class="stats-number">{{ workOrderCount.COMPLETED || 0 }}</view>
          <view class="stats-label">已完成</view>
        </view>
      </view>

      <!-- 通知横幅 -->
      <view class="alert-banner" @click="goToDispatchRecord">
        <text class="alert-text">当前需要进行派单到维保工人的工单：</text>
        <text class="alert-number">{{ workOrderCount.WAITING_COMPANY_DISPATCH || 0 }}</text>
      </view>

      <!-- 功能按钮 -->
      <view class="function-buttons">
        <view class="function-btn primary" @click="goToRepairRecords">
          <text class="btn-text">报修记录</text>
        </view>
        <view class="function-btn secondary" @click="goToDeviceRepair">
          <text class="btn-text">设备报修</text>
        </view>
      </view>

      <!-- 待办工单列表 -->
      <view class="card-section">
        <view class="section-title">待办</view>
        <view class="empty-list" v-if="pendingOrders.length === 0">
          <image src="/static/empty.png" mode="aspectFit" class="empty-image"></image>
          <text class="empty-text">暂无待办工单</text>
        </view>
        <view class="order-list" v-else>
          <view
            class="order-item"
            v-for="(item, index) in pendingOrders"
            :key="index"
            @click="goToDetail(item.workOrderId)">
            <view class="order-header">
              <text class="order-id">{{ item.workOrderId }}</text>
              <text class="tag" :class="`tag-${item.status?.type}`">
                {{ item.status?.label }}
              </text>
            </view>
            <view class="order-content">
              <text class="order-type">报修类型：{{ item.serviceClassLabel }}</text>
              <text class="order-desc">故障描述：{{ item.faultDesc }}</text>
              <text class="order-desc">报修单位：{{ item.reportEnterprise }}</text>
              <text class="order-desc">详细地址：{{ item.detailLocation }}</text>
            </view>
            <view class="order-footer">
              <text class="order-time">{{ formatTime(item.reportTime) }}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="pendingOrders.length > 0">
        <text v-if="isLoading">加载中...</text>
        <text v-else-if="noMoreData">没有更多数据了</text>
        <text v-else>上拉加载更多</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { NavBar } from "@/components"
import { onLoad, onUnload } from "@dcloudio/uni-app"
import { getWorkOrderCountApi, getWorkOrderListApi } from "@/api"
import { useUserStore } from "@/store/user"
import { formatTime, getWorkOrderStatus } from "@/utils"

const userStore = useUserStore()
/* ===================================== 统计数据 ===================================== */
const workOrderCount = ref<Record<string, number>>({})

// 获取工单数量
const getWorkOrderCount = async () => {
  const res = await getWorkOrderCountApi({
    maintenanceUnitId: userStore.unit,
    maintenanceUserId: userStore.userId
  })
  const data = res.data.data
  let PENDING_COUNT = 0
  let COMPLETED_COUNT = 0
  data.forEach((item: any) => {
    if (
      [
        "WAITING_REPAIR_PERSON_COME",
        "WAITING_REPAIR_PLAN",
        "WAITING_PLATFORM_AUDIT",
        "WAITING_REPAIR_PLAN_MODIFY",
        "WAITING_REPAIR_CONFIRM",
        "PROCESSING",
        "WAITING_REPAIR_FINISH_CONFIRM",
        "WAITING_PLATFORM_FINISH_CONFIRM"
      ].includes(item._id)
    ) {
      PENDING_COUNT += item.count
    } else if (["FINISHED", "CANCELLED", "NO_NEED_REPAIR"].includes(item._id)) {
      COMPLETED_COUNT += item.count
    }
    workOrderCount.value[item._id] = item.count
  })
  workOrderCount.value.PENDING = PENDING_COUNT
  workOrderCount.value.COMPLETED = COMPLETED_COUNT
}

onLoad(() => {
  getWorkOrderCount()
  getPendingOrders()
  uni.$on("repairOrderListRefresh", resetRefresh)
})

onUnload(() => {
  uni.$off("repairOrderListRefresh", resetRefresh)
})

function resetRefresh() {
  getWorkOrderCount()
  getPendingOrders(true)
}

/* ===================================== 待办工单 ===================================== */

const pendingOrders = ref<any[]>([])
const pageOffset = ref(0)
const pageLimit = ref(10)
const isRefreshing = ref(false)
const isLoading = ref(false)
const noMoreData = ref(false)

function getPendingOrders(isRefresh = false) {
  isLoading.value = true

  // 如果是刷新，重置页码
  if (isRefresh) {
    pageOffset.value = 0
    noMoreData.value = false
  }

  getWorkOrderListApi({
    limit: pageLimit.value,
    offset: pageOffset.value,
    filters: `maintenanceUnitId=${userStore.unit},maintenanceUserId=${userStore.userId}`,
    commonStatus: "WAITING_REPAIR_PERSON_COME,WAITING_REPAIR_PLAN,WAITING_REPAIR_PLAN_MODIFY,PROCESSING"
  })
    .then(res => {
      const data = res?.data?.data?.rows || []

      data.forEach((item: any) => {
        item.status = getWorkOrderStatus(item.status)
      })

      // 如果是刷新，替换数据，否则追加数据
      if (isRefresh) {
        pendingOrders.value = data
      } else {
        pendingOrders.value = [...pendingOrders.value, ...data]
      }

      // 如果返回的数据少于请求的数量，表示没有更多数据了
      if (data.length < pageLimit.value) {
        noMoreData.value = true
      }

      isLoading.value = false
      if (isRefreshing.value) {
        isRefreshing.value = false
      }
    })
    .catch(() => {
      isLoading.value = false
      if (isRefreshing.value) {
        isRefreshing.value = false
      }
    })
}

// 下拉刷新
function onRefresh() {
  isRefreshing.value = true
  getWorkOrderCount()
  getPendingOrders(true)
}

// 上拉加载更多
function loadMore() {
  if (isLoading.value || noMoreData.value) return
  pageOffset.value += pageLimit.value
  getPendingOrders()
}

/* ===================================== 跳转页面 ===================================== */
// 跳转到接单记录
const goToOrderRecords = () => {
  uni.switchTab({
    url: "/pages/order-record/index"
  })
}

// 跳转到待派单
const goToDispatchRecord = () => {
  uni.navigateTo({
    url: "/pages/dispatch-record/index"
  })
}

// 跳转到报修记录
const goToRepairRecords = () => {
  uni.navigateTo({
    url: "/pages/repair-records/index"
  })
}

// 跳转到设备报修
const goToDeviceRepair = () => {
  uni.navigateTo({
    url: "/pages/device-repair/index"
  })
}

// 跳转到工单详情
const goToDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/order-detail/index?workOrderId=${id}`
  })
}
</script>

<style lang="scss">
.home-container {
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-content-with-nav {
    height: calc(100vh - 88rpx);
    padding-bottom: 88rpx;
  }

  /* 工单状态统计样式 */
  .stats-grid {
    display: flex;
    flex-wrap: wrap;
    background-color: #8a78ef;
    border-radius: 4rpx;
    margin: 0rpx 0rpx 20rpx 0rpx;
    position: relative;
    color: white;

    .stats-item {
      width: 50%;
      padding: 30rpx 20rpx;
      text-align: center;
      box-sizing: border-box;

      .stats-number {
        font-size: 46rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
      }

      .stats-label {
        font-size: 26rpx;
      }
    }

    .stats-divider {
      position: absolute;
      background-color: rgba(255, 255, 255, 0.3);

      &:not(.horizontal) {
        width: 1px;
        height: 50%;
        top: 25%;
        left: 50%;
      }

      &.horizontal {
        height: 1px;
        width: 100%;
        left: 0;
        top: 50%;
      }
    }
  }

  /* 通知横幅样式 */
  .alert-banner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 20rpx 30rpx;
    border-radius: 8rpx;
    margin: 20rpx 20rpx;
    border-left: 8rpx solid #ff9900;

    .alert-text {
      font-size: 28rpx;
      color: #333;
    }

    .alert-number {
      font-size: 28rpx;
      color: #ff9900;
      font-weight: bold;
    }
  }

  /* 功能按钮样式 */
  .function-buttons {
    display: flex;
    justify-content: space-between;
    margin: 20rpx;

    .function-btn {
      width: 48%;
      height: 100rpx;
      border-radius: 8rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      &.primary {
        background-color: #007aff;
      }

      &.secondary {
        background-color: #8a78ef;
      }

      .btn-text {
        color: #fff;
        font-size: 30rpx;
        margin-left: 10rpx;
      }
    }
  }

  .card-section {
    margin: 30rpx 20rpx 10rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    box-sizing: border-box;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .section-title {
      font-size: 30rpx;
      font-weight: bold;
      margin-bottom: 30rpx;
      color: #333;
    }
  }

  .empty-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;

    .empty-image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .order-list {
    .order-item {
      background-color: #f9f9f9;
      border-radius: 8rpx;
      padding: 20rpx;
      margin-bottom: 20rpx;

      .order-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16rpx;

        .order-id {
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
        }
        .tag {
          &-success {
            color: #1890ff;
          }
          &-warning {
            color: #e69215;
          }
          &-error {
            color: #f56c6c;
          }
          &-info {
            color: #909399;
          }
        }
      }

      .order-content {
        margin-bottom: 16rpx;

        .order-type,
        .order-source,
        .order-desc {
          font-size: 26rpx;
          color: #666;
          display: block;
          margin-bottom: 8rpx;
        }
      }

      .order-footer {
        display: flex;
        flex-direction: column;
        border-top: 1px solid #eee;
        padding-top: 16rpx;

        .order-detail,
        .order-time {
          font-size: 24rpx;
          color: #999;
          margin-bottom: 4rpx;
        }
      }
    }
  }

  /* 加载更多提示 */
  .loading-more {
    text-align: center;
    padding: 20rpx 0;

    text {
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>
