const express = require("express");
const router = express.Router();
const repairEnterpriseController = require("../controllers/repair-enterprise");
const upload = require("../middleware/upload");
const { protect } = require("../middleware/auth");

// 新建入驻单位
router.post(
  "/create-repair-enterprise",
  protect,
  repairEnterpriseController.createRepairEnterprise
);

// 编辑入驻单位
router.put(
  "/edit-repair-enterprise",
  protect,
  repairEnterpriseController.editRepairEnterprise
);

// 获取入驻单位详情
router.get(
  "/get-repair-enterprise-detail",
  protect,
  repairEnterpriseController.getRepairEnterpriseDetail
);

// 修改入驻单位状态
router.put(
  "/update-repair-enterprise-status",
  protect,
  repairEnterpriseController.updateRepairEnterpriseStatus
);

// 获取入驻单位列表
router.get(
  "/get-repair-enterprise-list",
  protect,
  repairEnterpriseController.getRepairEnterpriseList
);

// 添加报修人员
router.post(
  "/add-repair-user",
  protect,
  repairEnterpriseController.addRepairUser
);

// 获取报修人员列表
router.get(
  "/get-repair-user-list",
  protect,
  repairEnterpriseController.getRepairUserList
);

// 编辑报修人员
router.put(
  "/edit-repair-user",
  protect,
  repairEnterpriseController.editRepairUser
);

// 修改报修人员状态
router.put(
  "/update-repair-user-status",
  protect,
  repairEnterpriseController.updateRepairUserStatus
);

// 获取结算配置
router.get(
  "/get-repair-enterprise-config",
  protect,
  repairEnterpriseController.getRepairEnterpriseConfig
);

// 新增结算配置
router.post(
  "/create-repair-enterprise-config",
  protect,
  repairEnterpriseController.createRepairEnterpriseConfig
);

// 设置包年配置项目
router.post(
  "/set-repair-enterprise-year-config",
  protect,
  repairEnterpriseController.setRepairEnterpriseYearConfig
);

// 编辑结算配置
router.put(
  "/edit-repair-enterprise-config",
  protect,
  repairEnterpriseController.editRepairEnterpriseConfig
);

// 删除结算配置
router.delete(
  "/delete-repair-enterprise-config",
  protect,
  repairEnterpriseController.deleteRepairEnterpriseConfig
);

// 生成邀请码
router.post(
  "/generate-client-invite-code",
  protect,
  repairEnterpriseController.generateInviteCode
);

module.exports = router;
