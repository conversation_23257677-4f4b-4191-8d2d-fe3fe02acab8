import { createApp } from "vue";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import App from "./App.vue";
import router from "./router";
import store from "@/store";
import zhCn from "element-plus/es/locale/lang/zh-cn";
// 导入自定义样式
import "./assets/main.css";

const app = createApp(App);
app.use(ElementPlus, {
  locale: zhCn,
});
app.use(router);
app.use(store);
app.mount("#app");
