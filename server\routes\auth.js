const express = require("express");
const {
  register,
  login,
  getMe,
  logout,
  updatePassword,
  registerClientUserByCode,
} = require("../controllers/auth");
const { protect } = require("../middleware/auth");

const router = express.Router();

// 注册路由
router.post("/register", register);
// 通过邀请码注册报修人员
router.post("/register-client-user-by-code", registerClientUserByCode);

// 登录路由
router.post("/login", login);

// 获取当前用户路由 (需要身份验证)
router.get("/me", protect, getMe);

// 退出登录路由
router.post("/logout", logout);

// 根据旧密码修改密码
router.post("/update-password", updatePassword);

module.exports = router;
