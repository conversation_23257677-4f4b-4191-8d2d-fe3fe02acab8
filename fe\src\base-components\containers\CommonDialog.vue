<template>
  <el-dialog
    v-model="dialogVisible"
    class="common-dialog"
    :title="props.title"
    :append-to-body="props.appendToBody"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :destroy-on-close="false"
    :show-close="props.showClose"
    :width="props.width"
    :center="props.center"
    :align-center="props.alignCenter"
    @close="handleCancel"
  >
    <template #header>
      <span>{{ props.title }}</span>
    </template>
    <slot></slot>
    <template v-if="!props.noFooter" #footer>
      <slot name="footer">
        <span class="dialog-footer">
          <el-button
            :disabled="props.btnLoading || actionDisabled"
            @click="handleCancel()"
            >取消</el-button
          >
          <el-button
            :loading="props.btnLoading"
            :disabled="actionDisabled"
            type="primary"
            @click="handleConfirm()"
          >
            确定
          </el-button>
        </span>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { noop } from "lodash";

const props = defineProps({
  visible: { type: Boolean, default: false },
  title: { type: String, default: "" },
  appendToBody: { type: Boolean, default: false },
  cancelCallback: {
    type: Function,
    default: noop,
  },
  confirmCallback: {
    type: Function,
    default: noop,
  },
  noFooter: { type: Boolean, default: false },
  width: { type: String, default: "500px" },
  center: { type: Boolean, default: false },
  btnLoading: { type: Boolean, default: false },
  alignCenter: { type: Boolean, default: true },
  showClose: { type: Boolean, default: true },
  actionDisabled: { type: Boolean, default: false },
});

const emits = defineEmits(["update:visible"]);

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emits("update:visible", val),
});

function handleCancel() {
  if (props.cancelCallback) {
    props.cancelCallback();
  }
  emits("update:visible", false);
}

function handleConfirm() {
  if (props.confirmCallback) {
    props.confirmCallback();
  } else {
    emits("update:visible", false);
  }
}
</script>
