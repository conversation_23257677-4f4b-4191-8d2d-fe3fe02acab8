<template>
  <CommonDialog
    v-model:visible="visible"
    title="包年项目配置"
    width="1000px"
    no-footer
    :cancel-callback="
      () => {
        emit('refresh');
      }
    "
  >
    <div class="yearly-project-dialog">
      <div class="yearly-project-layout">
        <!-- 左侧服务类别列表 -->
        <div class="service-category-panel">
          <div class="panel-header">
            <h3>服务类别</h3>
          </div>
          <div class="panel-content">
            <div
              v-for="(category, index) in serviceClasses"
              :key="index"
              :class="[
                'category-item',
                { active: activeCategory === category.id },
              ]"
              @click="handleCategoryClick(category)"
            >
              {{ category.serviceClass }}
            </div>
          </div>
        </div>

        <!-- 右侧服务项目列表 -->
        <div class="service-item-panel">
          <div class="panel-header">
            <h3>服务项目</h3>
            <div class="search-box">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索服务项目"
                clearable
              >
                <template #append>
                  <el-button :icon="Search" @click="handleSearch()">
                  </el-button>
                </template>
              </el-input>
            </div>
          </div>

          <div class="panel-content">
            <div
              v-if="serviceItems.length === 0 && !isLoadingItems"
              class="empty-data"
            >
              <el-empty description="暂无数据" />
            </div>
            <div v-else class="table-container">
              <el-table
                :data="currentPageItems"
                style="width: 100%"
                v-loading="isLoadingItems"
                height="calc(100% - 50px)"
              >
                <el-table-column width="50">
                  <template #header>
                    <el-checkbox
                      v-model="selectAll"
                      @change="handleSelectAllChange"
                      :indeterminate="isIndeterminate"
                    ></el-checkbox>
                  </template>
                  <template #default="scope">
                    <el-checkbox
                      v-model="scope.row.selected"
                      @change="(val: boolean) => handleItemSelectChange(val, scope.row)"
                    ></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="code"
                  label="服务项目编号"
                  width="150"
                ></el-table-column>
                <el-table-column
                  prop="serviceItem"
                  label="服务项目"
                ></el-table-column>
                <el-table-column
                  prop="unit"
                  label="单位"
                  width="80"
                ></el-table-column>
              </el-table>

              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="total"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  background
                  small
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </CommonDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import { CommonDialog } from "@/base-components";
import { getServiceContentApi, getServiceItemApi } from "@/api";
import { setCustomerUnitYearConfig } from "@/api/customer";
import { toastError, Message } from "@/utils";
import { Search } from "@element-plus/icons-vue";

const props = defineProps({
  settlement: {
    type: Object,
    default: () => ({}),
  },
  unitId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["refresh"]);

const visible = ref(false);
const loading = ref(false);

// 定义服务项目类型
interface ServiceItem {
  _id: string;
  code: string;
  serviceItem: string;
  serviceClass: string;
  unit: string;
  selected?: boolean;
}

// 定义选中项类型
interface SelectedItem {
  serviceClassId: string;
  serviceItemIds: string[];
}

// 服务类别和服务项目
const serviceClasses = ref<any[]>([]);
const serviceItems = ref<ServiceItem[]>([]);
const selectedItems = ref<SelectedItem[]>([]);

// 搜索关键词
const searchKeyword = ref("");

// 当前选中的服务类别
const activeCategory = ref("");

// 分页相关
const defaultPageSize = 10;
const currentPage = ref(1);
const pageSize = ref(defaultPageSize);
const total = ref(0);

// 标记是否正在加载数据
const isLoadingItems = ref(false);

// 处理服务项目数据，添加选中状态
const processServiceItems = (items: ServiceItem[]) => {
  return items.map((item) => {
    // 检查是否已选中
    let isSelected = false;

    // 遍历selectedItems，查找是否包含当前项
    for (const selected of selectedItems.value) {
      if (
        selected.serviceClassId === item.serviceClass &&
        selected.serviceItemIds &&
        selected.serviceItemIds.includes(item._id)
      ) {
        isSelected = true;
        break;
      }
    }

    return {
      ...item,
      selected: isSelected,
    };
  });
};

// 当前页的数据
const currentPageItems = computed(() => {
  return serviceItems.value;
});

// 全选状态
const selectAll = ref(false);
const isIndeterminate = computed(() => {
  if (currentPageItems.value.length === 0) return false;
  const selectedCount = currentPageItems.value.filter(
    (item) => item.selected
  ).length;
  return selectedCount > 0 && selectedCount < currentPageItems.value.length;
});

// 处理服务类别点击
const handleCategoryClick = (category: { id: string }) => {
  activeCategory.value = category.id;
  // 重置为第一页
  currentPage.value = 1;
  // 重新加载数据
  getServiceItems();
};

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  // 重新加载数据
  getServiceItems();
};

// 处理每页显示数量变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  // 重置为第一页
  currentPage.value = 1;
  // 重新加载数据
  getServiceItems();
};

// 更新全选状态
const updateSelectAllStatus = () => {
  if (currentPageItems.value.length === 0) {
    selectAll.value = false;
    return;
  }
  const selectedCount = currentPageItems.value.filter(
    (item) => item.selected
  ).length;
  selectAll.value = selectedCount === currentPageItems.value.length;
};

// 全选/取消全选
const handleSelectAllChange = (val: boolean) => {
  loading.value = true;

  // 按服务类别分组
  const serviceClassGroups: Record<string, string[]> = {};
  // 只处理当前页的数据
  currentPageItems.value.forEach((item) => {
    if (!serviceClassGroups[item.serviceClass]) {
      serviceClassGroups[item.serviceClass] = [];
    }
    // 如果当前状态与目标状态不同，则添加到组中
    if (item.selected !== val) {
      serviceClassGroups[item.serviceClass].push(item._id);
      // 更新项目的选中状态
      item.selected = val;
    }
  });

  // 创建一个Promise数组，用于存储所有API调用
  const promises = Object.keys(serviceClassGroups).map((serviceClassId) => {
    const serviceItemIds = serviceClassGroups[serviceClassId];

    // 如果没有需要更新的项目，则跳过
    if (serviceItemIds.length === 0) return Promise.resolve();

    return setCustomerUnitYearConfig({
      unitId: props.unitId,
      settlementId: props.settlement.id,
      serviceClassId,
      serviceItemIds,
      isChecked: val,
    });
  });

  // 等待所有API调用完成
  Promise.all(promises)
    .then(() => {
      // 更新selectedItems
      const updateSelectedItems = () => {
        // 遍历所有服务类别
        Object.keys(serviceClassGroups).forEach((serviceClassId) => {
          // 查找是否已存在相同serviceClassId的项
          const serviceClassIndex = selectedItems.value.findIndex(
            (selected) => selected.serviceClassId === serviceClassId
          );

          if (val) {
            // 如果是选中操作
            const itemsToAdd = serviceClassGroups[serviceClassId];
            if (serviceClassIndex !== -1) {
              // 如果已存在该服务类别，添加serviceItemIds
              itemsToAdd.forEach((_id) => {
                if (
                  !selectedItems.value[
                    serviceClassIndex
                  ].serviceItemIds.includes(_id)
                ) {
                  selectedItems.value[serviceClassIndex].serviceItemIds.push(
                    _id
                  );
                }
              });
            } else if (itemsToAdd.length > 0) {
              // 如果不存在该服务类别，创建新项
              selectedItems.value.push({
                serviceClassId,
                serviceItemIds: [...itemsToAdd],
              });
            }
          } else {
            // 如果是取消选中操作
            if (serviceClassIndex !== -1) {
              // 从serviceItemIds中移除
              const itemsToRemove = serviceClassGroups[serviceClassId];
              selectedItems.value[serviceClassIndex].serviceItemIds =
                selectedItems.value[serviceClassIndex].serviceItemIds.filter(
                  (id) => !itemsToRemove.includes(id)
                );

              // 如果serviceItemIds为空，删除整个serviceClass项
              if (
                selectedItems.value[serviceClassIndex].serviceItemIds.length ===
                0
              ) {
                selectedItems.value.splice(serviceClassIndex, 1);
              }
            }
          }
        });
      };

      updateSelectedItems();
      Message.success(val ? "批量添加包年项目成功" : "批量移除包年项目成功");
    })
    .catch((error: any) => {
      // 恢复当前页所有项目的选中状态
      currentPageItems.value.forEach((item) => {
        item.selected = !val;
      });
      toastError(error, val ? "批量添加包年项目失败" : "批量移除包年项目失败");
    })
    .finally(() => {
      loading.value = false;
      updateSelectAllStatus();
    });
};

// 单个选择变化
const handleItemSelectChange = (
  val: boolean,
  item: ServiceItem & { selected: boolean }
) => {
  loading.value = true;

  // 调用API更新包年项目配置
  setCustomerUnitYearConfig({
    unitId: props.unitId,
    settlementId: props.settlement.id,
    serviceClassId: item.serviceClass,
    serviceItemIds: [item._id],
    isChecked: val,
  })
    .then(() => {
      // 更新selectedItems
      // 查找是否已存在相同serviceClassId的项
      const serviceClassIndex = selectedItems.value.findIndex(
        (selected) => selected.serviceClassId === item.serviceClass
      );

      if (val) {
        // 如果选中
        if (serviceClassIndex !== -1) {
          // 如果已存在该服务类别，添加serviceItemId
          if (
            !selectedItems.value[serviceClassIndex].serviceItemIds.includes(
              item._id
            )
          ) {
            selectedItems.value[serviceClassIndex].serviceItemIds.push(
              item._id
            );
          }
        } else {
          // 如果不存在该服务类别，创建新项
          selectedItems.value.push({
            serviceClassId: item.serviceClass,
            serviceItemIds: [item._id],
          });
        }
      } else {
        // 如果取消选中
        if (serviceClassIndex !== -1) {
          // 从serviceItemIds中移除
          const itemIndex = selectedItems.value[
            serviceClassIndex
          ].serviceItemIds.indexOf(item._id);
          if (itemIndex !== -1) {
            selectedItems.value[serviceClassIndex].serviceItemIds.splice(
              itemIndex,
              1
            );

            // 如果serviceItemIds为空，删除整个serviceClass项
            if (
              selectedItems.value[serviceClassIndex].serviceItemIds.length === 0
            ) {
              selectedItems.value.splice(serviceClassIndex, 1);
            }
          }
        }
      }

      updateSelectAllStatus();
      Message.success(val ? "添加包年项目成功" : "移除包年项目成功");
    })
    .catch((error: any) => {
      // 恢复选中状态
      item.selected = !val;
      toastError(error, val ? "添加包年项目失败" : "移除包年项目失败");
    })
    .finally(() => {
      loading.value = false;
    });
};

// 获取服务类别
const getServiceClasses = async () => {
  try {
    loading.value = true;
    const res = await getServiceContentApi();
    serviceClasses.value = res.data.data;

    // 如果有服务类别，默认选中第一个
    if (serviceClasses.value.length > 0) {
      activeCategory.value = serviceClasses.value[0].id;
      getServiceItems();
    }

    loading.value = false;
  } catch (error) {
    toastError(error, "获取服务类别失败");
    loading.value = false;
  }
};

function handleSearch() {
  currentPage.value = 1;
  getServiceItems();
}

// 获取服务项目
const getServiceItems = async () => {
  try {
    if (!activeCategory.value) return;
    isLoadingItems.value = true;

    // 构建请求参数
    const params: Record<string, any> = {
      limit: pageSize.value,
      offset: (currentPage.value - 1) * pageSize.value,
      filters: `serviceClass=${activeCategory.value}`,
    };

    // 如果有搜索关键词，添加到过滤条件
    if (searchKeyword.value) {
      params.filters += `,serviceItem=${searchKeyword.value}`;
    }

    const res = await getServiceItemApi(params);

    // 确保返回的数据结构正确
    if (res.data && res.data.data) {
      // 处理返回的数据，添加选中状态
      serviceItems.value = processServiceItems(res.data.data.rows || []);
      total.value = res.data.data.pageElements?.totalElements || 0;
    } else {
      // 如果数据结构不正确，设置为空数组和0
      serviceItems.value = [];
      total.value = 0;
    }

    isLoadingItems.value = false;
  } catch (error: any) {
    toastError(error, "获取服务项目失败");
    isLoadingItems.value = false;
  }
};

// 初始化数据
const initData = () => {
  // 如果有已选择的项目，初始化selectedItems
  if (props.settlement && props.settlement.serviceItems) {
    // 深拷贝，避免直接修改props
    selectedItems.value = JSON.parse(
      JSON.stringify(props.settlement.serviceItems)
    );
  } else {
    selectedItems.value = [];
  }

  // 重置搜索关键词
  searchKeyword.value = "";

  // 重置分页状态
  currentPage.value = 1;
  pageSize.value = defaultPageSize;
};

// 打开弹窗
const openDialog = () => {
  visible.value = true;
  nextTick(() => {
    initData();
    getServiceClasses();
  });
};

defineExpose({
  openDialog,
});
</script>

<style lang="less" scoped>
.yearly-project-dialog {
  .yearly-project-layout {
    display: flex;
    gap: 20px;
    height: 700px;

    // 左侧服务类别面板
    .service-category-panel {
      width: 200px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .panel-header {
        padding: 12px 15px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;

        h3 {
          margin: 0;
          font-size: 16px;
          color: #303133;
        }
      }

      .panel-content {
        flex: 1;
        overflow-y: auto;

        .category-item {
          padding: 10px 15px;
          cursor: pointer;
          border-bottom: 1px solid #ebeef5;
          transition: all 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            background-color: #409eff;
            color: #fff;
          }
        }
      }
    }

    // 右侧服务项目面板
    .service-item-panel {
      flex: 1;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      display: flex;
      flex-direction: column;

      .panel-header {
        padding: 12px 15px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          font-size: 16px;
          color: #303133;
        }

        .search-box {
          width: 250px;
        }
      }

      .panel-content {
        flex: 1;
        overflow-y: auto;

        .empty-data {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
        }

        .table-container {
          display: flex;
          flex-direction: column;
          height: 100%;

          .el-table {
            flex: 1;
            overflow: auto;
          }

          .pagination-container {
            padding: 10px;
            display: flex;
            justify-content: flex-end;
            background-color: #fff;
            min-height: 40px; /* 确保分页器有最小高度 */
            position: relative; /* 确保分页器正确定位 */
            z-index: 10; /* 提高层级，避免被其他元素覆盖 */
          }
        }
      }
    }
  }
}
</style>
