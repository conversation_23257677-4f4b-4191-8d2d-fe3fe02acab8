const {
  RepairEnterprise,
  RepairEnterpriseSchema,
} = require("../models/RepairEnterprise");
const { RepairUser, RepairUserSchema } = require("../models/RepairUser");
const { File } = require("../models/File");
const { v4: uuid } = require("uuid");
const fs = require("fs");
const { getFilterObj } = require("../utils/filters");
const { registerUser } = require("../utils/user-utils");
const { maskPhoneNumber } = require("../utils/mask-show");
const { generateUniqueRandomCode } = require("../utils/workorder");
/**
 * 新建报修单位
 * @param {string} companyName 公司名称
 * @param {string} legalPerson 法人
 * @param {string} legalPersonPhone 法人电话
 * @param {string} contactPerson 联系人
 * @param {string} contactPhone 联系人电话
 * @param {string} companyAddress 公司地址
 * @param {string} operationArea 经营区域
 * @param {Array} settlements 结算方式
 * @returns {Object} 创建的报修单位信息
 */
const createRepairEnterprise = async (req, res) => {
  let adminUser;
  let repairEnterprise;
  try {
    const data = req.body;
    const enterpriseId = uuid();

    // 创建管理员用户
    adminUser = await registerUser({
      username: data.contactPerson,
      phone: data.contactPhone,
      password: "123456@abc", //默认密码
      userType: "REPAIR_UNIT",
      unit: enterpriseId,
      roles: ["管理员"],
    });
    if (!adminUser.success) {
      return res.status(400).json({
        code: 400,
        message: adminUser.message,
      });
    }
    repairEnterprise = await RepairEnterprise.create({
      id: enterpriseId,
      ...data,
      contactPersonId: adminUser.data.userId,
      status: "ENABLED",
    });

    return res.status(200).json({
      code: 200,
      message: "新建报修单位成功",
      data: {
        adminUser: adminUser.data,
        repairEnterprise,
      },
    });
  } catch (error) {
    // 如果创建报修单位失败,需要删除已创建的管理员用户
    if (adminUser?.success) {
      await RepairUser.findOneAndDelete({
        userId: adminUser.data.userId,
      });
    }
    return res.status(500).json({
      code: 500,
      message: "新建报修单位失败",
      error: error.message || "未知错误",
    });
  }
};

// 编辑入驻单位
const editRepairEnterprise = async (req, res) => {
  try {
    const data = req.body;
    const repairEnterprise = await RepairEnterprise.findOneAndUpdate(
      { id: data.id },
      data
    );
    // 更新联系人
    await RepairUser.findOneAndUpdate(
      { userId: repairEnterprise.contactPersonId },
      { username: data.contactPerson, phone: data.contactPhone }
    );
    res.status(200).json({
      code: 200,
      message: "编辑报修单位成功",
      data: repairEnterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "编辑报修单位失败",
      error: error.message || "未知错误",
    });
  }
};

// 获取报修单位列表
const getRepairEnterpriseList = async (req, res) => {
  try {
    let { offset, limit, filters, showMask = "true" } = req.query;
    offset = Number(offset);
    limit = Number(limit);
    let filtersObj = {};
    if (filters) {
      filtersObj = getFilterObj(filters, RepairEnterpriseSchema);
    }
    // 返回数据和总数
    const [repairEnterpriseList, total] = await Promise.all([
      //模糊查询
      RepairEnterprise.find(filtersObj)
        .skip(offset)
        .limit(limit)
        .sort({ updatedAt: -1 }),
      RepairEnterprise.countDocuments({}),
    ]);
    const resultList = repairEnterpriseList.map((item) => item.toObject());
    for (const item of resultList) {
      const memberCount = await RepairUser.countDocuments({
        unit: item.id,
      });
      item.memberCount = memberCount;
      if (showMask === "true") {
        item.contactPhone = maskPhoneNumber(item.contactPhone);
      }
    }
    res.status(200).json({
      code: 200,
      message: "获取入驻单位列表成功",
      data: {
        rows: resultList,
        pageElements: {
          totalElements: total,
          pageSize: limit,
        },
      },
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取入驻单位列表失败",
      error: error.message || "未知错误",
    });
  }
};

// 获取报修单位详情
const getRepairEnterpriseDetail = async (req, res) => {
  try {
    const { id } = req.query;
    const repairEnterprise = await RepairEnterprise.findOne({
      id,
    });

    if (!repairEnterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该入驻单位",
      });
    }

    return res.status(200).json({
      code: 200,
      message: "获取入驻单位详情成功",
      data: repairEnterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取入驻单位详情失败",
      error: error.message || "未知错误",
    });
  }
};

// 修改入驻单位状态
const updateRepairEnterpriseStatus = async (req, res) => {
  try {
    const { id, status } = req.body;
    const repairEnterprise = await RepairEnterprise.findOneAndUpdate(
      { id },
      { status }
    );
    res.status(200).json({
      code: 200,
      message: "修改入驻单位状态成功",
      data: repairEnterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "修改入驻单位状态失败",
      error: error.message || "未知错误",
    });
  }
};

// 获取报修人员列表
const getRepairUserList = async (req, res) => {
  try {
    const {
      unitId,
      filters,
      offset,
      limit,
      unitName,
      roles,
      showMask = "true",
    } = req.query;
    let filtersObj = {};
    if (filters) {
      filtersObj = getFilterObj(filters, RepairUser.schema);
    }
    if (unitId) filtersObj.unit = unitId;
    // roles参数

    if (roles) filtersObj.roles = { $in: roles.split(",") };

    // 如果存在unitName参数，先查询符合条件的企业ID
    let enterprises;
    if (unitName) {
      enterprises = await RepairEnterprise.find({
        companyName: { $regex: unitName, $options: "i" }, // 使用正则表达式进行模糊查询，i表示不区分大小写
      }).select(
        "id companyName contactPerson contactPhone companyAddress status"
      );

      // 获取符合条件的企业ID列表
      let enterpriseIds = enterprises.map((enterprise) => enterprise.id);

      // 将企业ID条件添加到筛选条件中
      if (enterpriseIds.length > 0) {
        filtersObj.unit = { $in: enterpriseIds };
      } else {
        // 如果没有找到匹配的企业，返回空结果
        return res.json({
          code: 200,
          data: {
            total: 0,
            list: [],
          },
        });
      }
    }

    // 只返回用户名、手机号、角色、状态
    let staffList = await RepairUser.find(filtersObj).skip(offset).limit(limit);
    staffList = staffList.map((item) => item.toObject());
    if (!unitName)
      // 获取每个用户的单位信息
      enterprises = await RepairEnterprise.find({
        id: { $in: staffList.map((item) => item.unit) },
      });

    // 将单位信息添加到每个用户的单位信息中
    staffList.forEach((item) => {
      const unit = enterprises.find((unit) => unit.id === item.unit);
      item.createdAt = item.createdAt.getTime();
      if (unit) {
        item.unitInfo = {
          id: unit.id,
          companyName: unit.companyName,
          contactPerson: unit.contactPerson,
          contactPhone: unit.contactPhone,
          companyAddress: unit.companyAddress,
          status: unit.status,
        };
      }
      if (showMask === "true") {
        item.maskPhone = maskPhoneNumber(item.phone);
      }
    });

    // 获取总数
    const total = await RepairUser.countDocuments(filtersObj);

    return res.status(200).json({
      code: 200,
      message: "获取入驻单位人员列表成功",
      data: {
        rows: staffList,
        pageElements: {
          totalElements: total,
          pageSize: limit,
        },
      },
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取报修人员列表失败",
      error: error.message || "未知错误",
    });
  }
};

// 添加报修人员
const addRepairUser = async (req, res) => {
  try {
    const data = req.body;
    const user = await registerUser({
      ...data,
      password: "123456@abc",
      userType: "REPAIR_UNIT",
      status: "ENABLED",
    });
    if (!user.success) {
      return res.status(400).json({
        code: 400,
        message: user.message,
      });
    }
    res.status(200).json({
      code: 200,
      message: "添加报修人员成功",
      data: user.data,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "添加报修人员失败",
      error: error.message || "未知错误",
    });
  }
};

// 编辑报修人员
const editRepairUser = async (req, res) => {
  try {
    const data = req.body;
    const user = await RepairUser.findOneAndUpdate(
      { userId: data.userId },
      { ...data }
    );

    await RepairEnterprise.findOneAndUpdate(
      { contactPersonId: data.userId },
      { contactPerson: data.username, contactPhone: data.phone }
    );
    res.status(200).json({
      code: 200,
      message: "编辑报修人员成功",
      data: user,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "编辑报修人员失败",
      error: error.message || "未知错误",
    });
  }
};

// 修改报修人员状态
const updateRepairUserStatus = async (req, res) => {
  try {
    const { userId, status } = req.body;
    const user = await RepairUser.findOneAndUpdate({ userId }, { status });
    res.status(200).json({
      code: 200,
      message: "修改报修人员状态成功",
      data: user,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "修改报修人员状态失败",
      error: error.message || "未知错误",
    });
  }
};

// 获取结算配置
const getRepairEnterpriseConfig = async (req, res) => {
  try {
    const { unitId } = req.query;
    // 返回settlements属性和id
    const repairEnterprise = await RepairEnterprise.findOne({
      id: unitId,
    }).select("settlements id");
    res.status(200).json({
      code: 200,
      message: "获取结算配置成功",
      data: repairEnterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取结算配置失败",
      error: error.message || "未知错误",
    });
  }
};

// 新增结算配置
const createRepairEnterpriseConfig = async (req, res) => {
  try {
    const data = req.body;

    // 查找报修单位
    const repairEnterprise = await RepairEnterprise.findOne({
      id: data.unitId,
    });
    if (!repairEnterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该报修单位",
      });
    }

    // 如果没有settlements字段，初始化为空数组
    if (!repairEnterprise.settlements) {
      repairEnterprise.settlements = [];
    }

    // 检查contractId是否存在
    if (
      repairEnterprise.settlements.some(
        (settlement) => settlement.contractId === data.contractId
      )
    ) {
      return res.status(400).json({
        code: 400,
        message: "合同编号已存在",
      });
    }

    // 创建新的结算配置对象
    const newSettlement = {
      id: uuid(),
      contractId: data.contractId,
      contractName: data.contractName,
      settlementType: data.settlementType,
      contractPeriod: data.contractPeriod,
      status: "ENABLED",
      serviceItems: [],
    };

    // 将新的结算配置对象推入settlements数组
    repairEnterprise.settlements.push(newSettlement);

    // 保存更新后的报修单位
    await repairEnterprise.save();

    res.status(200).json({
      code: 200,
      message: "新增结算配置成功",
      data: newSettlement,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "新增结算配置失败",
      error: error.message || "未知错误",
    });
  }
};

// 设置包年配置项目
const setRepairEnterpriseYearConfig = async (req, res) => {
  try {
    const data = req.body;
    // 设置settlements内settlementId相同的serviceItems，data.isChecked为 true则推入，false则删除
    const { unitId, settlementId, serviceClassId, serviceItemIds, isChecked } =
      data;

    // 确保serviceItemIds是数组
    const itemIds = Array.isArray(serviceItemIds)
      ? serviceItemIds
      : [serviceItemIds];

    // 查找报修单位
    const repairEnterprise = await RepairEnterprise.findOne({ id: unitId });
    if (!repairEnterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该报修单位",
      });
    }

    // 查找对应的结算方式
    const settlementIndex = repairEnterprise.settlements.findIndex(
      (settlement) => settlement.id === settlementId
    );

    if (settlementIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: "未找到对应的结算方式",
      });
    }

    // 获取当前结算方式
    const settlement = repairEnterprise.settlements[settlementIndex];

    // 如果没有serviceItems字段，初始化为空数组
    if (!settlement.serviceItems) {
      settlement.serviceItems = [];
    }
    // 如果有serviceItems存在，判断是否有交叉item
    else {
      // 查找和当前结算方式时间交叉的合同
      const settlements = repairEnterprise.settlements.filter((item) => {
        const otherStartTime = item.contractPeriod[0];
        const otherEndTime = item.contractPeriod[1];

        const hasTimeOverlap =
          (settlement.contractPeriod[0] >= otherStartTime &&
            settlement.contractPeriod[0] <= otherEndTime) ||
          (settlement.contractPeriod[1] >= otherStartTime &&
            settlement.contractPeriod[1] <= otherEndTime) ||
          (otherStartTime >= settlement.contractPeriod[0] &&
            otherStartTime <= settlement.contractPeriod[1]) ||
          (otherEndTime >= settlement.contractPeriod[0] &&
            otherEndTime <= settlement.contractPeriod[1]);

        if (
          hasTimeOverlap &&
          item.settlementType === settlement.settlementType &&
          item.id !== settlementId
        )
          return item;
      });
      if (settlements && settlements.length > 0) {
        const existItem = settlements.filter((tempSettlement) => {
          const tempResult = tempSettlement.serviceItems?.filter((item) => {
            return (
              item.serviceClassId === serviceClassId &&
              item.serviceItemIds?.filter((itemId) => itemIds.includes(itemId))
                ?.length > 0
            );
          });
          return tempResult && tempResult.length > 0;
        });
        if (existItem && existItem.length > 0)
          return res.status(500).json({
            code: 500,
            message: "该服务项目存在合同时间交叉，不可重复设置",
          });
      }
    }

    // 根据isChecked决定添加或删除服务项目
    if (isChecked) {
      // 查找是否已存在相同serviceClassId的项
      const serviceClassIndex = settlement.serviceItems.findIndex(
        (item) => item.serviceClassId === serviceClassId
      );

      if (serviceClassIndex !== -1) {
        // 如果存在，合并serviceItemIds（去重）
        const existingItemIds =
          settlement.serviceItems[serviceClassIndex].serviceItemIds;
        const newItemIds = [...new Set([...existingItemIds, ...itemIds])];
        settlement.serviceItems[serviceClassIndex].serviceItemIds = newItemIds;
      } else {
        // 如果不存在，添加新项
        settlement.serviceItems.push({
          serviceClassId,
          serviceItemIds: itemIds,
        });
      }
    } else {
      // 如果isChecked为false，从serviceClassId下的serviceItemIds中移除指定的项
      const serviceClassIndex = settlement.serviceItems.findIndex(
        (item) => item.serviceClassId === serviceClassId
      );

      if (serviceClassIndex !== -1) {
        // 过滤掉要删除的serviceItemIds
        const filteredItemIds = settlement.serviceItems[
          serviceClassIndex
        ].serviceItemIds.filter((id) => !itemIds.includes(id));

        if (filteredItemIds.length > 0) {
          // 如果还有剩余项，更新serviceItemIds
          settlement.serviceItems[serviceClassIndex].serviceItemIds =
            filteredItemIds;
        } else {
          // 如果没有剩余项，删除整个serviceClass项
          settlement.serviceItems.splice(serviceClassIndex, 1);
        }
      }
    }

    // 更新结算方式
    repairEnterprise.settlements[settlementIndex] = settlement;

    // 保存更新后的报修单位
    await repairEnterprise.save();

    res.status(200).json({
      code: 200,
      message: "设置包年配置项目成功",
      data: settlement,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "设置包年配置项目失败",
      error: error.message || "未知错误",
    });
  }
};

// 编辑结算配置
const editRepairEnterpriseConfig = async (req, res) => {
  try {
    const data = req.body;

    // 查找报修单位
    const repairEnterprise = await RepairEnterprise.findOne({
      id: data.unitId,
    });
    if (!repairEnterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该报修单位",
      });
    }

    // 查找要编辑的结算配置项
    const settlementIndex = repairEnterprise.settlements.findIndex(
      (settlement) => settlement.id === data.id
    );

    if (settlementIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: "未找到对应的结算配置",
      });
    }

    // 更新结算配置项
    const updatedSettlement = {
      id: data.id,
      contractId: data.contractId,
      contractName: data.contractName,
      settlementType: data.settlementType,
      contractPeriod: data.contractPeriod,
      status: data.status || "ENABLED",
      serviceItems: repairEnterprise.settlements[settlementIndex].serviceItems,
    };

    // 更新结算方式
    repairEnterprise.settlements[settlementIndex] = updatedSettlement;

    // 保存更新后的报修单位
    await repairEnterprise.save();

    res.status(200).json({
      code: 200,
      message: "编辑结算配置成功",
      data: updatedSettlement,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "编辑结算配置失败",
      error: error.message || "未知错误",
    });
  }
};

// 删除结算配置
const deleteRepairEnterpriseConfig = async (req, res) => {
  try {
    const { unitId, id } = req.body;

    // 查找报修单位
    const repairEnterprise = await RepairEnterprise.findOne({ id: unitId });
    if (!repairEnterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该报修单位",
      });
    }

    // 查找要删除的结算配置项
    const settlementIndex = repairEnterprise.settlements.findIndex(
      (settlement) => settlement.id === id
    );

    if (settlementIndex === -1) {
      return res.status(404).json({
        code: 404,
        message: "未找到对应的结算配置",
      });
    }

    // 删除结算配置项
    repairEnterprise.settlements.splice(settlementIndex, 1);

    // 保存更新后的报修单位
    await repairEnterprise.save();

    res.status(200).json({
      code: 200,
      message: "删除结算配置成功",
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "删除结算配置失败",
      error: error.message || "未知错误",
    });
  }
};

// 生成邀请码
async function getCode() {
  // 生成英文和数字的六位随机码
  const code = generateUniqueRandomCode(6);
  // inviteCode数组的code是否存在
  const isExit = await RepairEnterprise.findOne({
    inviteCode: {
      $elemMatch: {
        code: code,
      },
    },
  });
  if (isExit) return getCode();
  else return code;
}
const generateInviteCode = async (req, res) => {
  try {
    const { unitId, roles } = req.body;
    if (!unitId || !roles) {
      return res.status(400).json({
        code: 400,
        message: "单位ID和角色不能为空",
      });
    }
    const enterprise = await RepairEnterprise.findOne({ id: unitId });
    const existCode = enterprise.inviteCode.find((item) => {
      // roles的值完全相等，排序可能不一样，所以需要排序后比较
      return item.roles.sort().join(",") === roles.sort().join(",");
    });
    // 如果存在，则返回邀请码
    if (existCode) {
      return res.status(200).json({
        code: 200,
        message: "该角色已存在，返回邀请码",
        data: existCode.code,
      });
    }

    // 如果不存在，则生成邀请码
    const code = await getCode();
    await RepairEnterprise.findOneAndUpdate(
      { id: unitId },
      { $push: { inviteCode: { code, roles } } }
    );
    return res.status(200).json({
      code: 200,
      message: "生成邀请码成功",
      data: code,
    });
  } catch (err) {
    return res.status(500).json({
      code: 500,
      message: "生成邀请码失败",
      error: err.message || "未知错误",
    });
  }
};

module.exports = {
  createRepairEnterprise,
  editRepairEnterprise,
  getRepairEnterpriseList,
  getRepairEnterpriseDetail,
  updateRepairEnterpriseStatus,
  addRepairUser,
  getRepairUserList,
  editRepairUser,
  updateRepairUserStatus,
  getRepairEnterpriseConfig,
  createRepairEnterpriseConfig,
  editRepairEnterpriseConfig,
  setRepairEnterpriseYearConfig,
  deleteRepairEnterpriseConfig,
  generateInviteCode,
};
