{"name": "user-auth-server", "version": "1.0.0", "description": "用户登录注册系统", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "build-image": "node scripts/build-image.cjs"}, "dependencies": {"bcryptjs": "^2.4.3", "chalk": "^4.1.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.1", "moment": "^2.30.1", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "shelljs": "^0.9.2", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.0.1"}}