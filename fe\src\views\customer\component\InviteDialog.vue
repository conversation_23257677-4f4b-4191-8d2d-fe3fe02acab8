<!-- 邀请员工注册弹窗 -->
<template>
  <CommonDialog v-model:visible="visible" title="邀请员工注册" width="500px">
    <div class="invite-container">
      <div class="tip-text">
        提示：将邀请码发给需要注册的员工后，员工在报修端注册页面填写邀请码即可完成注册
      </div>
      <div v-loading="inviteLoading" class="register-url">{{ inviteCode }}</div>
    </div>
    <template #footer>
      <el-button
        :disabled="inviteLoading"
        type="primary"
        @click="handleCopyLink"
        >复制邀请码</el-button
      >
    </template>
  </CommonDialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { CommonDialog } from "@/base-components";
import { Message, toastError } from "@/utils";
import { generateClientInviteCodeApi } from "@/api";

const visible = ref(false);

interface PropsType {
  unitId: string;
  unitName: string;
  userType: string;
  roles: string[];
}

const props = withDefaults(defineProps<PropsType>(), {});

// 打开弹窗
const inviteCode = ref("");
const inviteLoading = ref(false);
function openDialog() {
  visible.value = true;
  inviteLoading.value = true;
  generateClientInviteCodeApi({
    unitId: props.unitId,
    roles: props.roles,
  })
    .then((res) => {
      inviteCode.value = res.data.data;
      inviteLoading.value = false;
    })
    .catch((err) => {
      inviteLoading.value = false;
      toastError(err, "生成邀请码失败");
      visible.value = false;
    });
}

// 复制链接
function handleCopyLink() {
  const textarea = document.createElement("textarea");
  textarea.setAttribute("readonly", "readonly");
  textarea.value = `${props.unitName}邀请你注册即时修报修工具，注册后，可发起设备报修，注册时请填写邀请码：${inviteCode.value}`;
  document.body.appendChild(textarea);
  textarea.select();
  if (document.execCommand("copy")) {
    document.execCommand("copy");
    Message.success("复制成功！");
  }
  document.body.removeChild(textarea);
}

defineExpose({
  openDialog,
});
</script>

<style lang="less" scoped>
.invite-container {
  padding: 20px 0;

  .tip-text {
    font-size: 14px;
    color: #606266;
    margin-bottom: 20px;
  }

  .register-url {
    padding: 15px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    word-break: break-all;
    font-size: 14px;
    color: #409eff;
    cursor: pointer;
    text-align: center;

    &:hover {
      background-color: #ecf5ff;
    }
  }

  .copy-btn {
    margin-top: 10px;
    color: #409eff;
    cursor: pointer;
  }
}
</style>
