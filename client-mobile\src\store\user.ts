import { defineStore } from "pinia"
import { UserTypeEnum } from "@/configs"

interface UserState {
  userId: string
  username: string
  phone: string
  userType: UserTypeEnum
  unit: string
  token: string
  rememberPhone: boolean
  roles: string[]
  companyName: string
}

export const useUserStore = defineStore("USER_INFO", {
  state: (): UserState => ({
    userId: uni.getStorageSync("userId") || "",
    username: uni.getStorageSync("username") || "",
    phone: uni.getStorageSync("phone") || "",
    userType: uni.getStorageSync("userType") || UserTypeEnum.PLATFORM,
    unit: uni.getStorageSync("unit") || "",
    token: uni.getStorageSync("token") || "",
    rememberPhone: uni.getStorageSync("rememberPhone") || false,
    roles: uni.getStorageSync("roles") ? JSON.parse(uni.getStorageSync("roles")) : [],
    companyName: uni.getStorageSync("companyName") || ""
  }),
  actions: {
    // 设置用户信息
    setUserInfo(userInfo: any) {
      const userId = userInfo.userId || ""
      const username = userInfo.username || ""
      const phone = userInfo.phone || ""
      const userType = userInfo.userType || UserTypeEnum.PLATFORM
      const unit = userInfo.unit || ""
      const token = userInfo.token || ""
      const roles = userInfo.roles || []
      const companyName = userInfo.companyName || ""

      // 更新 storage
      uni.setStorageSync("userId", userId)
      uni.setStorageSync("username", username)
      uni.setStorageSync("phone", phone)
      uni.setStorageSync("userType", userType)
      uni.setStorageSync("unit", unit)
      uni.setStorageSync("token", token)
      uni.setStorageSync("roles", JSON.stringify(roles))
      uni.setStorageSync("companyName", companyName)

      // 更新 state
      this.userId = userId
      this.username = username
      this.phone = phone
      this.userType = userType
      this.unit = unit
      this.token = token
      this.roles = roles
      this.companyName = companyName
    },

    // 设置是否记住手机号
    setRememberPhone(remember: boolean) {
      uni.setStorageSync("rememberPhone", remember)
      this.rememberPhone = remember
    },

    // 清除用户信息（登出时使用）
    clearUserInfo() {
      console.log("clearUserInfo")
      // 清除 storage
      uni.removeStorageSync("userId")
      uni.removeStorageSync("username")
      uni.removeStorageSync("userType")
      uni.removeStorageSync("unit")
      uni.removeStorageSync("token")
      uni.removeStorageSync("roles")
      uni.removeStorageSync("companyName")

      // 清除 state
      this.userId = ""
      this.username = ""
      this.userType = UserTypeEnum.PLATFORM
      this.unit = ""
      this.token = ""
      this.roles = []
      this.companyName = ""

      // 如果不记住手机号，则清除手机号
      if (!this.rememberPhone) {
        uni.removeStorageSync("phone")
        this.phone = ""
      }
    }
  },
  getters: {
    isAuth: state => {
      return state.roles.includes("管理员") || state.roles.includes("报修审核员")
    }
  }
})
