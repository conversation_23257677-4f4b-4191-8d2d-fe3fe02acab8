const express = require("express");
const router = express.Router();
const maintenanceEnterpriseController = require("../controllers/maintenance-enterprise");
const upload = require("../middleware/upload");
const { protect } = require("../middleware/auth");

// 新建入驻单位
router.post(
  "/create-maintenance-enterprise",
  protect,
  maintenanceEnterpriseController.createMaintenanceEnterprise
);

// 编辑入驻单位
router.put(
  "/edit-maintenance-enterprise",
  protect,
  maintenanceEnterpriseController.editMaintenanceEnterprise
);

// 获取入驻单位列表
router.get(
  "/get-maintenance-enterprise-list",
  protect,
  maintenanceEnterpriseController.getMaintenanceEnterpriseList
);

// 上传资质文件
router.post(
  "/upload-qualification-file",
  protect,
  upload.single("file"),
  maintenanceEnterpriseController.uploadQualificationFile
);

// 获取入驻单位详情
router.get(
  "/get-maintenance-enterprise-detail/:id",
  protect,
  maintenanceEnterpriseController.getMaintenanceEnterpriseById
);

// 更新入驻单位状态
router.put(
  "/update-maintenance-enterprise-status",
  protect,
  maintenanceEnterpriseController.updateMaintenanceEnterpriseStatus
);

// 获取入驻单位人员列表
router.get(
  "/get-maintenance-enterprise-staff-list",
  protect,
  maintenanceEnterpriseController.getMaintenanceEnterpriseStaffList
);

// 获取入驻单位人员详情
router.get(
  "/get-maintenance-enterprise-staff-detail",
  protect,
  maintenanceEnterpriseController.getMaintenanceEnterpriseStaffDetail
);

// 新建入驻单位人员
router.post(
  "/create-maintenance-enterprise-staff",
  protect,
  maintenanceEnterpriseController.createMaintenanceEnterpriseStaff
);

// 编辑入驻单位人员
router.put(
  "/edit-maintenance-enterprise-staff",
  protect,
  maintenanceEnterpriseController.editMaintenanceEnterpriseStaff
);

// 审核入驻单位人员
router.put(
  "/audit-maintenance-enterprise-staff",
  protect,
  maintenanceEnterpriseController.auditMaintenanceEnterpriseStaff
);

// 编辑入驻单位人员状态
router.put(
  "/edit-maintenance-enterprise-staff-status",
  protect,
  maintenanceEnterpriseController.editMaintenanceEnterpriseStaffStatus
);

// 创建入驻单位结算
router.post(
  "/create-maintenance-enterprise-settlement",
  protect,
  maintenanceEnterpriseController.createMaintenanceEnterpriseSettlementConfig
);

// 获取入驻单位结算列表
router.get(
  "/get-maintenance-enterprise-settlement-list",
  protect,
  maintenanceEnterpriseController.getMaintenanceEnterpriseSettlementConfig
);

// 编辑入驻单位结算
router.put(
  "/edit-maintenance-enterprise-settlement",
  protect,
  maintenanceEnterpriseController.editMaintenanceEnterpriseSettlementConfig
);

// 删除入驻单位结算
router.delete(
  "/delete-maintenance-enterprise-settlement",
  protect,
  maintenanceEnterpriseController.deleteMaintenanceEnterpriseSettlementConfig
);

// 配置结算价格
router.post(
  "/config-maintenance-enterprise-settlement-price",
  protect,
  maintenanceEnterpriseController.editMaintenanceEnterpriseSettlementPrice
);

module.exports = router;
