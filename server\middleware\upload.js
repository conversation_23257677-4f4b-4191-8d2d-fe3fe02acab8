const multer = require("multer");
const path = require("path");
const fs = require("fs");

// 确保上传目录存在
const uploadDir = path.join(__dirname, "../uploads");
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 清理临时文件函数
const cleanTempFiles = () => {
  fs.readdir(uploadDir, (err, files) => {
    if (err) {
      console.error("清理临时文件时发生错误:", err);
      return;
    }

    const now = Date.now();
    files.forEach((file) => {
      const filePath = path.join(uploadDir, file);
      fs.stat(filePath, (err, stats) => {
        if (err) {
          console.error(`获取文件 ${file} 状态时发生错误:`, err);
          return;
        }

        // 如果文件超过24小时则删除
        if (now - stats.mtimeMs > 24 * 60 * 60 * 1000) {
          fs.unlink(filePath, (err) => {
            if (err) {
              console.error(`删除文件 ${file} 时发生错误:`, err);
            } else {
              console.log(`成功删除过期文件: ${file}`);
            }
          });
        }
      });
    });
  });
};

// 设置定时清理任务，每24小时执行一次
setInterval(cleanTempFiles, 24 * 60 * 60 * 1000);
// 启动时执行一次清理
cleanTempFiles();

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 处理原始文件名的中文编码
    const originalname = Buffer.from(file.originalname, "latin1").toString(
      "utf8"
    );
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const fileExt = path.extname(originalname);
    const newFileName = uniqueSuffix + fileExt;

    // 将原始文件名保存到请求对象中，以便后续使用
    file.originalname = originalname;

    cb(null, newFileName);
  },
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 暂时允许所有文件类型
  cb(null, true);

  /* 原始限制代码，暂时注释
  const allowedTypes = ["image/jpeg", "image/png", "image/gif", "video/mp4", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    console.log("拒绝的文件类型:", file.mimetype);
    cb(new Error("不支持的文件类型"), false);
  }
  */
};

// 创建上传中间件
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
});

// 整个模块导出
module.exports = upload;
