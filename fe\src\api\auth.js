import axios from "./axios-instance";
import { useUserStore } from "@/store";
const API_URL = "/api/auth";

// 用户注册
export const register = (userData) => {
  return axios.post(`${API_URL}/register`, userData, {
    headers: {
      checkSensitive: true,
    },
  });
};

// 用户登录
export const login = (credentials) => {
  return axios.post(`${API_URL}/login`, credentials);
};

// 退出登录
export const logout = () => {
  return axios.post(`${API_URL}/logout`);
};

// 获取当前用户信息
export const getCurrentUser = () => {
  return axios.get(`${API_URL}/me`);
};

// 修改密码
export const updatePassword = (data) => {
  return axios.post(`${API_URL}/update-password`, data);
};
