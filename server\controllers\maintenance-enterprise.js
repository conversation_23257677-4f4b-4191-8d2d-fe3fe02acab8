const {
  MaintenanceEnterprise,
  MaintenanceEnterpriseSchema,
} = require("../models/MaintenanceEnterprise");
const FileModel = require("../models/File");
const {
  MaintenanceUser,
  MaintenanceUserSchema,
} = require("../models/MaintenanceUser");
const { WorkOrder } = require("../models/WorkOrder");
const { PlatformUser } = require("../models/PlatformUser");
const { v4: uuid } = require("uuid");
const fs = require("fs");
const { getFilterObj } = require("../utils/filters");
const { registerUser } = require("../utils/user-utils");
const { maskPhoneNumber } = require("../utils/mask-show");

/**
 * @description 新建入驻单位
 * @param {string} companyName 公司名称
 * @param {string} legalPerson 法人
 * @param {string} legalPersonPhone 法人电话
 * @param {string} contactPerson 联系人
 * @param {string} contactPhone 联系人电话
 * @param {string} companyAddress 公司地址
 * @param {string} operationArea 经营区域
 * @param {Array} qualificationFiles 资质文件
 * @param {Array} settlements 结算方式
 * @returns {Object} 创建的入驻单位信息
 */
const createMaintenanceEnterprise = async (req, res) => {
  let adminUser;
  let maintenanceEnterprise;
  try {
    const data = req.body;
    const enterpriseId = uuid();

    // 创建管理员用户
    adminUser = await registerUser({
      username: data.contactPerson,
      phone: data.contactPhone,
      password: "123456@abc", //默认密码
      userType: "MAINTENANCE_UNIT",
      unit: enterpriseId,
      roles: ["管理员"],
    });

    if (!adminUser.success) {
      return res.status(400).json({
        code: 400,
        message: adminUser.message,
      });
    }

    // 创建维保企业

    maintenanceEnterprise = await MaintenanceEnterprise.create({
      id: enterpriseId,
      settlements: [],
      ...data,
      contactPersonId: adminUser.data.userId,
      status: "ENABLED",
    });

    return res.status(200).json({
      code: 200,
      message: "新建入驻单位成功",
      data: {
        adminUser: adminUser.data,
        maintenanceEnterprise,
      },
    });
  } catch (error) {
    // 如果创建维保企业失败,需要删除已创建的管理员用户
    if (adminUser?.success) {
      await MaintenanceUser.findOneAndDelete({
        userId: adminUser.data.userId,
      });
    }
    return res.status(500).json({
      code: 500,
      message: "新建入驻单位失败",
      error: error.message || "未知错误",
    });
  }
};

// 编辑入驻单位
const editMaintenanceEnterprise = async (req, res) => {
  try {
    const data = req.body;
    const maintenanceEnterprise = (
      await MaintenanceEnterprise.findOne({
        id: data.id,
      })
    ).toObject();
    const oldQualificationFiles = maintenanceEnterprise.qualificationFiles;
    const newQualificationFiles = data.qualificationFiles.map(
      (item) => item.fileId
    );
    const deleteQualificationFiles = oldQualificationFiles
      .filter((item) => !newQualificationFiles.includes(item.fileId))
      .map((item) => item.fileId);

    // 删除删除的文件
    if (deleteQualificationFiles.length > 0) {
      await FileModel.deleteFile(deleteQualificationFiles);
    }

    // 更新入驻单位
    await MaintenanceEnterprise.findOneAndUpdate({ id: data.id }, { ...data });

    // 更新联系人
    await MaintenanceUser.findOneAndUpdate(
      { userId: maintenanceEnterprise.contactPersonId },
      { username: data.contactPerson, phone: data.contactPhone }
    );

    res.status(200).json({
      code: 200,
      message: "编辑入驻单位成功",
      data: maintenanceEnterprise,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      code: 500,
      message: "编辑入驻单位失败",
      error: error.message || "未知错误",
    });
  }
};

// 获取入驻单位列表
const getMaintenanceEnterpriseList = async (req, res) => {
  try {
    let { offset, limit, filters, showMask = "true" } = req.query;
    offset = Number(offset);
    limit = Number(limit);
    let filtersObj = {};
    if (filters) {
      filtersObj = getFilterObj(filters, MaintenanceEnterpriseSchema);
    }
    // 返回数据和总数
    const [maintenanceEnterpriseList, total] = await Promise.all([
      //模糊查询
      MaintenanceEnterprise.find(filtersObj)
        .skip(offset)
        .limit(limit)
        .sort({ updatedAt: -1 }),
      MaintenanceEnterprise.countDocuments({}),
    ]);
    const resultList = maintenanceEnterpriseList.map((item) => item.toObject());
    // 查询每个单位的成员数量
    for (const item of resultList) {
      const memberCount = await MaintenanceUser.countDocuments({
        unit: item.id,
      });
      item.memberCount = memberCount;
      if (showMask === "true") {
        item.contactPhone = maskPhoneNumber(item.contactPhone);
        item.legalPersonPhone = maskPhoneNumber(item.legalPersonPhone);
      }
    }
    res.status(200).json({
      code: 200,
      message: "获取入驻单位列表成功",
      data: {
        rows: resultList,
        pageElements: {
          totalElements: total,
          pageSize: limit,
        },
      },
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取入驻单位列表失败",
      error: error.message || "未知错误",
    });
  }
};

// 上传资质文件
async function uploadQualificationFile(req, res) {
  try {
    // 检查是否有文件上传
    if (!req.file) {
      return res.status(400).json({ error: "未检测到上传文件" });
    }

    // 从formData中获取文件信息
    const file = req.file; // Multer或其他中间件处理的文件对象

    // 读取文件内容
    const fileContent = fs.readFileSync(file.path);

    // 准备存储到MongoDB的数据
    const fileData = {
      filename: file.originalname,
      mimeType: file.mimetype, // 确保和模型字段名一致
      size: file.size,
      data: fileContent, // 实际文件内容作为Buffer存储
    };

    // 添加元数据（如果存在）
    if (req.body && req.body.metadata) {
      try {
        // 尝试解析metadata，如果它是字符串形式的JSON
        const metadataStr = req.body.metadata;
        fileData.metadata =
          typeof metadataStr === "string"
            ? JSON.parse(metadataStr)
            : metadataStr;
      } catch (e) {
        fileData.metadata = req.body.metadata;
      }
    } else {
      fileData.metadata = {};
    }

    const savedFile = await FileModel.saveFile(fileData);

    // 删除临时文件（如果不需要保留）
    fs.unlinkSync(file.path);

    return res.status(201).json({
      message: "文件上传成功",
      fileId: savedFile.fileId,
      filename: savedFile.filename,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "文件上传失败",
      error: error.message || "未知错误",
    });
  }
}

/**
 * @description 获取入驻单位详情
 * @param {string} req.params.id 单位ID
 * @returns {Object} 入驻单位详情
 */
const getMaintenanceEnterpriseById = async (req, res) => {
  try {
    const { id } = req.params;
    const maintenanceEnterprise = await MaintenanceEnterprise.findOne({
      id,
    });

    if (!maintenanceEnterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该入驻单位",
      });
    }

    return res.status(200).json({
      code: 200,
      message: "获取入驻单位详情成功",
      data: maintenanceEnterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取入驻单位详情失败",
      error: error.message || "未知错误",
    });
  }
};

/**
 * @description 更新入驻单位状态
 * @param {string} id 单位ID
 * @param {string} status 状态
 * @returns {Object} 更新后的入驻单位信息
 */
const updateMaintenanceEnterpriseStatus = async (req, res) => {
  try {
    const { status, id } = req.body;

    const maintenanceEnterprise = await MaintenanceEnterprise.findOneAndUpdate(
      { id },
      { status }
    );

    if (!maintenanceEnterprise) {
      return res.status(500).json({
        code: 500,
        message: "未找到该入驻单位",
      });
    }

    return res.status(200).json({
      code: 200,
      message: "更新入驻单位状态成功",
      data: maintenanceEnterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "更新入驻单位状态失败",
      error: error.message || "未知错误",
    });
  }
};

/**
 * @description 获取入驻单位人员列表
 * @param {string} unitId 入驻单位ID
 * @param {string} filters 过滤条件
 * @returns {Object} 入驻单位人员列表
 */
const getMaintenanceEnterpriseStaffList = async (req, res) => {
  try {
    const {
      unitId,
      filters,
      offset,
      limit,
      unitName,
      haveCount = "false",
      showMask = "true",
    } = req.query;
    let filtersObj = {};
    if (filters) {
      filtersObj = getFilterObj(filters, MaintenanceUserSchema);
    }
    if (unitId) filtersObj.unit = unitId;

    // 如果存在unitName参数，先查询符合条件的企业ID
    let enterprises;
    if (unitName) {
      enterprises = await MaintenanceEnterprise.find({
        companyName: { $regex: unitName, $options: "i" }, // 使用正则表达式进行模糊查询，i表示不区分大小写
      }).select(
        "id companyName contactPerson contactPhone companyAddress status"
      );

      // 获取符合条件的企业ID列表
      let enterpriseIds = enterprises.map((enterprise) => enterprise.id);

      // 将企业ID条件添加到筛选条件中
      if (enterpriseIds.length > 0) {
        filtersObj.unit = { $in: enterpriseIds };
      } else {
        // 如果没有找到匹配的企业，返回空结果
        return res.json({
          code: 200,
          data: {
            total: 0,
            list: [],
          },
        });
      }
    }

    // 只返回用户名、手机号、角色、状态
    let staffList = await MaintenanceUser.find(filtersObj)
      .select(
        "userId username unit phone roles status createdAt qualifications"
      )
      .skip(offset)
      .limit(limit);
    if (!unitName)
      // 获取每个用户的单位信息
      enterprises = await MaintenanceEnterprise.find({
        id: { $in: staffList.map((item) => item.unit) },
      });

    staffList = staffList.map((item) => item.toObject());

    // 将单位信息添加到每个用户的单位信息中
    staffList.forEach((item) => {
      const unit = enterprises.find((unit) => unit.id === item.unit);
      item.createdAt = item.createdAt.getTime();
      if (unit) {
        item.unitInfo = {
          id: unit.id,
          companyName: unit.companyName,
          contactPerson: unit.contactPerson,
          contactPhone: unit.contactPhone,
          companyAddress: unit.companyAddress,
          status: unit.status,
        };
      }
      if (showMask === "true") {
        item.phone = maskPhoneNumber(item.phone);
      }
    });
    // 如果haveCount为true，则获取每个用户的工单的不同状态的数量
    if (haveCount === "true") {
      for (const item of staffList) {
        const count = await WorkOrder.aggregate([
          {
            $match: {
              maintenanceUserId: item.userId,
            },
          },
          { $group: { _id: "$status", count: { $sum: 1 } } },
        ]);
        item.count = count;
      }
    }

    // // 获取总数
    const total = await MaintenanceUser.countDocuments(filtersObj);

    return res.status(200).json({
      code: 200,
      message: "获取入驻单位人员列表成功",
      data: {
        rows: staffList,
        pageElements: {
          totalElements: total,
          pageSize: limit,
        },
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      code: 500,
      message: "获取入驻单位人员列表失败",
      error: error.message || "未知错误",
    });
  }
};

/**
 * @description 获取入驻单位人员详情
 * @param {string} userId 用户ID
 * @returns {Object} 入驻单位人员详情
 */
const getMaintenanceEnterpriseStaffDetail = async (req, res) => {
  try {
    const { userId } = req.query;
    let user = await MaintenanceUser.findOne({ userId });
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: "未找到该用户",
      });
    }
    user = user.toObject();
    if (user.auditHistory) {
      const auditUser = user.auditHistory.map((item) => item.auditUser);
      const auditUserData = await PlatformUser.find({
        userId: { $in: auditUser },
      });
      user.auditHistory.forEach((item) => {
        item.auditUserName = auditUserData.find(
          (user) => user.userId === item.auditUser
        ).username;
      });
    }

    res.status(200).json({
      code: 200,
      message: "获取入驻单位人员详情成功",
      data: user,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取入驻单位人员详情失败",
      error: error.message || "未知错误",
    });
  }
};

// 新建入驻单位人员
const createMaintenanceEnterpriseStaff = async (req, res) => {
  try {
    const data = req.body;
    const user = await registerUser({
      userId: uuid(),
      ...data,
      password: "123456@abc",
      userType: "MAINTENANCE_UNIT",
      status: "WAITING_AUDIT",
    });
    if (!user.success) {
      return res.status(400).json({
        code: 400,
        message: user.message,
      });
    }
    res.status(200).json({
      code: 200,
      message: "新建入驻单位人员成功",
      data: user.data,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "新建入驻单位人员失败",
      error: error.message || "未知错误",
    });
  }
};

// 编辑入驻单位人员
const editMaintenanceEnterpriseStaff = async (req, res) => {
  try {
    const data = req.body;

    let maintenanceUser = await MaintenanceUser.findOne({
      userId: data.userId,
    });
    if (!maintenanceUser) {
      return res.status(404).json({
        code: 404,
        message: "未找到该用户",
      });
    }
    maintenanceUser = maintenanceUser.toObject();
    const oldQualificationFiles = maintenanceUser.qualifications;
    const newQualificationFiles = data.qualifications.map(
      (item) => item.fileId
    );
    const deleteQualificationFiles = oldQualificationFiles
      .filter((item) => !newQualificationFiles.includes(item.fileId))
      .map((item) => item.fileId);

    // 删除删除的文件
    if (deleteQualificationFiles.length > 0) {
      await FileModel.deleteFile(deleteQualificationFiles);
    }

    // 更新入驻单位人员
    await MaintenanceUser.findOneAndUpdate(
      { userId: data.userId },
      {
        ...data,
        status:
          maintenanceUser.status === "AUDIT_FAILED"
            ? "WAITING_AUDIT"
            : maintenanceUser.status,
      }
    );

    // 更新入驻单位联系人
    await MaintenanceEnterprise.findOneAndUpdate(
      { contactPersonId: data.userId },
      { contactPerson: data.username, contactPhone: data.phone }
    );
    res.status(200).json({
      code: 200,
      message: "编辑入驻单位人员成功",
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "编辑入驻单位人员失败",
      error: error.message || "未知错误",
    });
  }
};

// 审核入驻单位人员
const auditMaintenanceEnterpriseStaff = async (req, res) => {
  try {
    const { userId, status, rejectReason } = req.body;

    const user = await MaintenanceUser.findOne({ userId });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: "未找到该用户",
      });
    }

    user.auditHistory.push({
      status,
      auditTime: new Date().getTime(),
      auditUser: req.user.userId,
      auditReason: rejectReason || "",
    });
    user.status = status === "REJECTED" ? "AUDIT_FAILED" : "ENABLED";
    user.rejectReason = rejectReason || "";
    await user.save();

    res.status(200).json({
      code: 200,
      message: "审核入驻单位人员成功",
      data: user,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "审核入驻单位人员失败",
      error: error.message || "未知错误",
    });
  }
};

// 编辑入驻单位人员状态
const editMaintenanceEnterpriseStaffStatus = async (req, res) => {
  try {
    const { userId, status } = req.body;
    const user = await MaintenanceUser.findOneAndUpdate({ userId }, { status });
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: "未找到该用户",
      });
    }
    res.status(200).json({
      code: 200,
      message: "编辑入驻单位人员状态成功",
      data: user,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "编辑入驻单位人员状态失败",
      error: error.message || "未知错误",
    });
  }
};

// 获取结算配置
const getMaintenanceEnterpriseSettlementConfig = async (req, res) => {
  try {
    const { unitId } = req.query;
    const enterprise = await MaintenanceEnterprise.findOne({
      id: unitId,
    }).select("settlements id");
    res.status(200).json({
      code: 200,
      message: "获取结算配置成功",
      data: enterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取结算配置失败",
      error: error.message || "未知错误",
    });
  }
};

// 生成结算配置
const createMaintenanceEnterpriseSettlementConfig = async (req, res) => {
  try {
    const data = req.body;
    const enterprise = await MaintenanceEnterprise.findOne({ id: data.unitId });
    if (!enterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该单位",
      });
    }

    // 创建新的结算配置对象
    const newSettlement = {
      id: uuid(),
      contractId: data.contractId,
      contractName: data.contractName,
      contractPeriod: data.contractPeriod,
      status: "ENABLED",
      serviceItems: [],
    };
    enterprise.settlements.push(newSettlement);
    await enterprise.save();
    res.status(200).json({
      code: 200,
      message: "生成结算配置成功",
      data: enterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "生成结算配置失败",
      error: error.message || "未知错误",
    });
  }
};

// 编辑结算配置
const editMaintenanceEnterpriseSettlementConfig = async (req, res) => {
  try {
    const data = req.body;
    const enterprise = await MaintenanceEnterprise.findOne({ id: data.unitId });
    if (!enterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该单位",
      });
    }
    const settlement = enterprise.settlements.find(
      (item) => item.id === data.id
    );
    if (!settlement) {
      return res.status(404).json({
        code: 404,
        message: "未找到该结算配置",
      });
    }
    settlement.contractId = data.contractId;
    settlement.contractName = data.contractName;
    settlement.contractPeriod = data.contractPeriod;
    settlement.status = data.status || "ENABLED";
    await enterprise.save();
    res.status(200).json({
      code: 200,
      message: "编辑结算配置成功",
      data: enterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "编辑结算配置失败",
      error: error.message || "未知错误",
    });
  }
};

// 删除结算配置
const deleteMaintenanceEnterpriseSettlementConfig = async (req, res) => {
  try {
    const { id, unitId } = req.body;
    const enterprise = await MaintenanceEnterprise.findOne({ id: unitId });
    if (!enterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该单位",
      });
    }
    enterprise.settlements = enterprise.settlements.filter(
      (item) => item.id !== id
    );
    await enterprise.save();
    res.status(200).json({
      code: 200,
      message: "删除结算配置成功",
      data: enterprise,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "删除结算配置失败",
      error: error.message || "未知错误",
    });
  }
};

// 配置结算价格
const editMaintenanceEnterpriseSettlementPrice = async (req, res) => {
  try {
    const data = req.body;
    const enterprise = await MaintenanceEnterprise.findOne({ id: data.unitId });
    if (!enterprise) {
      return res.status(404).json({
        code: 404,
        message: "未找到该单位",
      });
    }
    const settlement = enterprise.settlements.find(
      (item) => item.id === data.id
    );

    if (!settlement) {
      return res.status(404).json({
        code: 404,
        message: "未找到该结算配置",
      });
    }
    // 如果没有serviceItems字段，初始化为空数组
    if (!settlement.serviceItems) {
      settlement.serviceItems = [];
    }

    // 是否存在服务项
    const priceIndex = settlement.serviceItems.findIndex(
      (item) =>
        item.serviceClassId === data.serviceClassId &&
        item.serviceItemId === data.serviceItemId
    );

    if (priceIndex === -1) {
      settlement.serviceItems.push({
        serviceClassId: data.serviceClassId,
        serviceItemId: data.serviceItemId,
        settlementPrice: data.settlementPrice,
        customTaxRate: data.customTaxRate,
      });
    } else {
      settlement.serviceItems[priceIndex].settlementPrice =
        data.settlementPrice;
      settlement.serviceItems[priceIndex].customTaxRate = data.customTaxRate;
    }

    await enterprise.save();
    res.status(200).json({
      code: 200,
      message: "配置结算价格成功",
      data: settlement,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "配置结算价格失败",
      error: error.message || "未知错误",
    });
  }
};

module.exports = {
  createMaintenanceEnterprise,
  editMaintenanceEnterprise,
  getMaintenanceEnterpriseList,
  uploadQualificationFile,
  getMaintenanceEnterpriseById,
  updateMaintenanceEnterpriseStatus,
  getMaintenanceEnterpriseStaffList,
  getMaintenanceEnterpriseStaffDetail,
  createMaintenanceEnterpriseStaff,
  editMaintenanceEnterpriseStaff,
  auditMaintenanceEnterpriseStaff,
  editMaintenanceEnterpriseStaffStatus,
  createMaintenanceEnterpriseSettlementConfig,
  editMaintenanceEnterpriseSettlementConfig,
  deleteMaintenanceEnterpriseSettlementConfig,
  getMaintenanceEnterpriseSettlementConfig,
  editMaintenanceEnterpriseSettlementPrice,
};
