<!-- 添加人员弹窗 -->
<template>
  <CommonDialog
    v-model:visible="visible"
    :title="dialogTitle"
    width="500px"
    :no-footer="type === 'view'"
    :btn-loading="actionLoading"
    :confirm-callback="handleConfirm"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-suffix="："
      label-width="100px"
    >
      <el-form-item v-if="type !== 'create'" label="工号">
        <span>{{ formData.userId }}</span>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-if="type === 'create'"
          v-model.trim="formData.username"
          placeholder="请输入姓名"
        />
        <span v-else>{{ formData.username }}</span>
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-if="type === 'create'"
          v-model.trim="formData.phone"
          placeholder="请输入联系电话"
        />
        <span v-else>{{ formData.phone }}</span>
      </el-form-item>
      <el-form-item label="角色" prop="roles">
        <el-select
          v-if="type !== 'view'"
          v-model="formData.roles"
          placeholder="请选择角色"
          multiple
          style="width: 100%"
          :loading="dictionaryLoading"
        >
          <el-option
            v-for="item in roleList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <span v-else>{{ formData.roles.join(",") }}</span>
      </el-form-item>

      <el-form-item v-if="type !== 'create'" label="所属单位">
        <span>{{ formData.unitInfo?.companyName || "--" }}</span>
      </el-form-item>
      <el-form-item v-if="type !== 'create'" label="注册时间">
        <span>{{ formatDatetime(formData.createdAt) }}</span>
      </el-form-item>
    </el-form>
  </CommonDialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { CommonDialog } from "@/base-components";
import { cloneDeep } from "lodash";
import {
  addCustomerUnitStaff,
  getDictionaryList,
  editCustomerUnitStaff,
} from "@/api";
import { Message, formatDatetime, toastError, nameValidator } from "@/utils";
import { useUserStore } from "@/store";

const userStore = useUserStore();
const type = ref("");
const visible = ref(false);

const emit = defineEmits(["submit"]);

/* =================================== 字典数据 =================================== */
const dictionaryLoading = ref(false);
const roleList = ref([]);

onMounted(() => {
  getDictionary();
});

function getDictionary() {
  dictionaryLoading.value = true;
  getDictionaryList("repairRoles")
    .then((res) => {
      roleList.value =
        res.data.find((item) => item.groupValue === "repairRoles")?.children ||
        [];
      dictionaryLoading.value = false;
    })
    .catch((err) => {
      toastError(err, "获取字典数据失败");
      dictionaryLoading.value = false;
    });
}

/* =================================== 表单操作 =================================== */
const formRef = ref<FormInstance>();
const initFormData = {
  username: "",
  phone: "",
  roles: [],
  unit: "",
  status: "",
  userId: "",
  createdAt: "",
  unitInfo: null,
};
const formData = ref(cloneDeep(initFormData));
const actionLoading = ref(false);

// 表单校验规则
const rules = ref<FormRules>({
  username: [
    { required: true, message: "请输入姓名", trigger: "blur" },
    { validator: nameValidator, trigger: "blur" },
  ],
  phone: [
    { required: true, message: "请输入联系电话", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  role: [{ required: true, message: "请选择角色", trigger: "change" }],
});

function openDialog(dialogType: "create" | "edit" | "view", data = null) {
  type.value = dialogType;
  visible.value = true;
  if (data) formData.value = cloneDeep(data);
  else formData.value = cloneDeep(initFormData);
  formRef.value?.resetFields();
}

// 弹窗标题
const dialogTitle = computed(() => {
  const titleMap = {
    create: "添加人员",
    edit: "编辑人员",
    view: "人员详情",
  };
  return titleMap[type.value];
});

// 提交表单
const handleConfirm = async () => {
  if (type.value === "view") {
    return;
  }

  if (!formRef.value) return;

  await formRef.value.validate((valid) => {
    if (!valid) return;

    if (type.value === "create") {
      create();
    } else {
      update();
    }
  });
};

// 添加报修人员
function create() {
  actionLoading.value = true;
  addCustomerUnitStaff({
    unit: userStore.unit,
    username: formData.value.username,
    phone: formData.value.phone,
    roles: formData.value.roles,
  })
    .then((res) => {
      Message.success("添加成功");
      visible.value = false;
      actionLoading.value = false;
      emit("submit");
    })
    .catch((err) => {
      toastError(err, "添加失败");
      actionLoading.value = false;
    });
}

// 编辑报修人员
function update() {
  actionLoading.value = true;
  editCustomerUnitStaff({
    userId: formData.value.userId,
    roles: formData.value.roles,
  })
    .then((res) => {
      Message.success("保存成功");
      visible.value = false;
      actionLoading.value = false;
      emit("submit");
    })
    .catch((err) => {
      toastError(err, "保存失败");
      actionLoading.value = false;
    });
}
defineExpose({
  openDialog,
});
</script>

<style lang="less" scoped>
.el-select {
  width: 100%;
}
</style>
