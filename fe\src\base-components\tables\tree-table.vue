<template>
  <div class="tree-table-container">
    <el-table
      header-cell-class-name="tree-table-header"
      :header-cell-style="{
        background: 'rgb(248,249,252)',
        color: '#030814',
        fontSize: '16px',
      }"
      :data="$props.data"
      row-key="id"
      border
    >
      <template v-for="item in $props.columns" :key="item.prop">
        <el-table-column
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :fixed="item.fixed"
          :min-width="item.minWidth"
          :show-overflow-tooltip="true"
        >
          <template #default="{ row }">
            <slot :name="item.prop" :row="row">
              <span style="white-space: nowrap"> {{ row[item.prop] }} </span>
            </slot>
          </template>
        </el-table-column>
      </template>
      <template #empty>
        <el-empty>
          <template #image>
            <img src="@/assets/png/empty.png" alt="notData" />
          </template>
        </el-empty>
      </template>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import type { TableColumnItem } from "@/types";

defineProps<{
  columns: Array<TableColumnItem>;
  data: Array<Record<string, any>>;
}>();
</script>

<style lang="less" scoped>
.tree-table-container {
  height: 100%;
  :deep(.el-table) {
    height: 100%;
    .el-table__body-wrapper {
      height: calc(100% - 48px);
      overflow-y: auto;
    }
    .tree-table-header {
      background-color: var(--el-table-header-bg);
      color: var(--el-table-header-color);
    }
  }
}
</style>
