<template>
  <div v-loading="detailLoading || actionLoading" class="settlement-config">
    <div class="settlement-config-header">
      <el-button :icon="ArrowLeftBold" link @click="handleCancel">
        返回
      </el-button>
    </div>

    <div class="settlement-config-content">
      <div class="section-title">
        <span>结算信息 </span>
      </div>

      <div class="section-hint">
        <div>
          <div>
            当结算为包年、包年【配件价格单独结算】时，才需要配置结算方式
          </div>
          <div>
            当合同时间存在交集时，结算优先级：包年 > 包年配件另算 > 一单一结
          </div>
        </div>
        <div class="section-actions">
          <el-button type="primary" link @click="addSettlement">
            + 添加结算方式
          </el-button>
        </div>
      </div>

      <div class="settlement-list">
        <el-form
          ref="formRef"
          :model="formData"
          label-suffix="："
          label-width="110px"
          :rules="formRules"
        >
          <div
            v-for="(item, index) in formData.settlements"
            :key="index"
            class="settlement-item"
          >
            <el-form-item
              label="结算方式"
              :prop="`settlements.${index}.settlementType`"
              :rules="[
                {
                  required: true,
                  message: '请选择结算方式',
                  trigger: 'change',
                },
              ]"
            >
              <el-select v-model="item.settlementType" placeholder="请选择">
                <el-option
                  v-for="option in settlementTypeOptions"
                  :key="option.value"
                  :value="option.value"
                  :label="option.label"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="关联合同号"
              :prop="`settlements.${index}.contractId`"
              :rules="[
                {
                  required: true,
                  message: '请输入关联合同号',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input v-model.trim="item.contractId" placeholder="请输入" />
            </el-form-item>
            <el-form-item
              label="合同名称"
              :prop="`settlements.${index}.contractName`"
              :rules="[
                { required: true, message: '请输入合同名称', trigger: 'blur' },
              ]"
            >
              <el-input v-model.trim="item.contractName" placeholder="请输入" />
            </el-form-item>
            <el-form-item
              label="合同时间"
              :prop="`settlements.${index}.contractPeriod`"
              :rules="[
                {
                  required: true,
                  message: '请选择合同时间',
                  trigger: 'change',
                  type: 'array',
                },
                {
                  validator: validateContractPeriod,
                  trigger: 'change',
                },
              ]"
            >
              <el-date-picker
                v-model="item.contractPeriod"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                value-format="x"
              />
            </el-form-item>
            <div class="settlement-item-header">
              <div class="settlement-actions">
                <el-button
                  v-if="
                    item.settlementType === SettlementTypeEnum.YEARLY ||
                    item.settlementType ===
                      SettlementTypeEnum.YEARLY_WITHOUT_PARTS
                  "
                  :disabled="item.saveLoading || item.deleteLoading"
                  type="primary"
                  @click="openYearlyProjectDialog(item)"
                >
                  配置包年项目
                </el-button>
                <el-button
                  type="primary"
                  :disabled="item.deleteLoading"
                  :loading="item.saveLoading"
                  @click="validateAndSaveSettlement(item, index)"
                >
                  保存
                </el-button>
                <el-button
                  type="danger"
                  :disabled="item.saveLoading"
                  :loading="item.deleteLoading"
                  @click="removeSettlement(item, index)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>

    <!-- 包年项目配置弹窗 -->
    <YearlyProjectDialog
      ref="yearlyProjectDialogRef"
      :settlement="currentSettlement"
      :unitId="unitId"
      @refresh="getUnitDetail"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ArrowLeftBold } from "@element-plus/icons-vue";
import {
  getCustomerUnitConfig,
  createCustomerUnitConfig,
  editCustomerUnitConfig,
  deleteCustomerUnitConfig,
} from "@/api/customer";
import {
  SettlementTypeEnum,
  CommonStatusEnum,
  shortcuts,
  settlementTypeOptions,
} from "@/configs";
import { toastError, Message, getDayEndTime } from "@/utils";
import YearlyProjectDialog from "./component/YearlyProjectDialog.vue";
import { getDayStartTime } from "../../utils/datetime-util";

const route = useRoute();
const router = useRouter();

// 根据路由参数判断页面状态
const unitId = ref("");
const unitName = ref("");
const detailLoading = ref(false);
const actionLoading = ref(false);

onMounted(async () => {
  unitId.value = route.query.unitId;
  if (unitId.value) {
    getUnitDetail();
  } else {
    router.back();
  }
});

// 获取客户单位结算配置
function getUnitDetail() {
  detailLoading.value = true;

  // 先获取客户单位基本信息，获取单位名称

  getCustomerUnitConfig(unitId.value)
    .then((res) => {
      if (res && res.data && res.data.data) {
        formData.value.settlements = res.data.data.settlements || [];
      } else {
        formData.value.settlements = [];
      }
    })
    .catch((err) => {
      toastError(err, "获取结算配置失败");
    })
    .finally(() => {
      detailLoading.value = false;
    });
}

// 表单数据
const formData = ref({
  settlements: [],
});

// 表单引用
const formRef = ref(null);

// 表单校验规则 - 动态生成
const formRules = ref({});

// 验证合同时间
const validateContractPeriod = (rule, value, callback) => {
  if (!value || value.length !== 2) {
    callback();
    return;
  }

  callback();
};

// 添加结算方式
const addSettlement = () => {
  formData.value.settlements.push({
    id: "",
    contractId: "",
    contractName: "",
    settlementType: SettlementTypeEnum.IMMEDIATELY,
    contractPeriod: [],
    status: CommonStatusEnum.ENABLED,
    isNew: true,
  });
};

// 验证并保存单个结算方式
const validateAndSaveSettlement = (item, index) => {
  // 验证表单
  formRef.value.validateField(
    [
      `settlements.${index}.settlementType`,
      `settlements.${index}.contractId`,
      `settlements.${index}.contractName`,
      `settlements.${index}.contractPeriod`,
    ],
    (valid) => {
      if (!valid) {
        return;
      }

      saveSettlement(item);
    }
  );
};

// 保存单个结算方式
const saveSettlement = (item) => {
  // 设置loading状态
  item.saveLoading = true;
  const requestApi = !item.id
    ? createCustomerUnitConfig
    : editCustomerUnitConfig;

  // 处理合同时间，将结束时间设置为当天的23:59:59
  let contractPeriod = [...item.contractPeriod];
  if (contractPeriod && contractPeriod.length === 2) {
    // 将结束日期转换为Date对象
    const startDate = new Date(Number(contractPeriod[0]));
    const endDate = new Date(Number(contractPeriod[1]));
    contractPeriod[1] = getDayStartTime(startDate);
    contractPeriod[1] = getDayEndTime(endDate);
  }

  const data = {
    unitId: unitId.value,
    contractId: item.contractId,
    contractName: item.contractName,
    settlementType: item.settlementType,
    contractPeriod: contractPeriod,
    status: item.status || "ENABLED",
    serviceItems: item.serviceItems || [],
  };
  if (item.id) data.id = item.id;

  // 使用专门的结算配置API
  requestApi(data)
    .then(() => {
      Message.success("保存成功");
      // 更新结算方式列表
      getUnitDetail();
    })
    .catch((err) => {
      toastError(err, "保存失败");
    })
    .finally(() => {
      item.saveLoading = false;
    });
};

// 删除结算方式
const removeSettlement = (item, index) => {
  // 如果是新添加的，直接从列表中删除
  if (!item.id) {
    formData.value.settlements.splice(index, 1);
    return;
  }

  // 设置loading状态
  item.deleteLoading = true;

  // 调用删除API
  deleteCustomerUnitConfig({
    unitId: unitId.value,
    id: item.id, // 使用_id作为唯一标识
  })
    .then(() => {
      Message.success("删除成功");
      formData.value.settlements.splice(index, 1);
      getUnitDetail();
    })
    .catch((err) => {
      toastError(err, "删除失败");
    })
    .finally(() => {
      item.deleteLoading = false;
    });
};

// 取消
const handleCancel = () => {
  router.back();
};

// 包年项目配置
const yearlyProjectDialogRef = ref();
const currentSettlement = ref(null);

// 打开包年项目配置弹窗
const openYearlyProjectDialog = (settlement) => {
  if (!settlement.id) {
    return Message.warning("请先保存结算配置");
  }
  currentSettlement.value = settlement;
  yearlyProjectDialogRef.value?.openDialog();
};
</script>

<style lang="less" scoped>
.settlement-config {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);

  .settlement-config-header {
    padding: 10px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
  }

  .settlement-config-content {
    padding: 0px 20px 20px;
    box-sizing: border-box;
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0px 10px 0;
      padding-left: 10px;
      border-left: 4px solid #409eff;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .section-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    .section-hint {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      font-weight: 500;
      font-size: 14px;
      color: #666;

      .section-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    .settlement-list {
      margin-bottom: 18px;
      flex: 1;
      min-height: 0px;
      overflow: auto;
      .settlement-item {
        padding: 18px;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        height: fit-content;
        &:not(:first-child) {
          margin-top: 20px;
        }

        .settlement-item-header {
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .settlement-type {
            font-size: 16px;
            font-weight: bold;
          }

          .settlement-actions {
            display: flex;
            gap: 10px;
          }
        }
      }
    }

    .add-settlement {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
  }
}
</style>
