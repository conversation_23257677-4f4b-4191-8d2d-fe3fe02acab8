import { request } from "@/utils/request"
export const getFileData = fileId => {
  return request
    .get(`/file/get-file-data/${fileId}`, {
      responseType: "ArrayBuffer"
    })
    .then(res => {
      return res.data
    })
}

// 删除文件
export const deleteFile = fileIds => {
  return request.delete(`/file/delete-file`, {
    params: {
      fileIds
    }
  })
}

// 上传文件
export const uploadFile = (filePath: string) => {
  return request.uploadFile("/file/upload-file", {
    filePath: filePath
  })
}
