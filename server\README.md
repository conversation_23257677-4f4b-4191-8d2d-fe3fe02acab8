# 用户认证系统

基于 Node.js、Express 和 MongoDB 的用户认证系统，实现了基础的用户注册和登录功能。

## 功能

- 用户注册
- 用户登录
- 获取当前用户信息（需要身份验证）

## 技术栈

- Node.js
- Express
- MongoDB
- JWT 认证
- bcrypt 密码加密

## 安装

1. 安装依赖

```bash
npm install
```

2. 设置环境变量

创建一个`.env`文件并添加以下内容：

```
PORT=3000
MONGO_URI=mongodb://localhost:27017/userAuth
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=30d
```

## 运行

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## API 端点

### 注册用户

```
POST /api/auth/register
```

请求体：

```json
{
  "username": "用户名",
  "phone": "手机号",
  "password": "密码",
  "userType": "用户类型" // 可选值：平台方、报修单位、维保单位，默认为维保单位
}
```

### 用户登录

```
POST /api/auth/login
```

请求体：

```json
{
  "phone": "手机号",
  "password": "密码"
}
```

### 获取当前用户

```
GET /api/auth/me
```

请求头：

```
Authorization: Bearer {token}
```

## 数据验证规则

- 用户名：3-20 个字符
- 手机号：11 位中国大陆手机号
- 密码：最少 6 个字符
- 用户类型：必须是"平台方"、"报修单位"或"维保单位"之一
