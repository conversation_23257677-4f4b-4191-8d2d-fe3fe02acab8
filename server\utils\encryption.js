const crypto = require("crypto");

// AES加密解密配置
const AES_SECRET_KEY = Buffer.from("a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"); // 32字节密钥
const AES_IV = Buffer.from("q1w2e3r4t5y6u7i8"); // 16字节IV

// AES加密
function aesEncrypt(text) {
  if (!text) return "";
  const cipher = crypto.createCipheriv("aes-256-cbc", AES_SECRET_KEY, AES_IV);
  let encrypted = cipher.update(text, "utf8", "base64");
  encrypted += cipher.final("base64");
  return encrypted;
}

// AES解密
function aesDecrypt(encrypted) {
  if (!encrypted) return "";
  const decipher = crypto.createDecipheriv(
    "aes-256-cbc",
    AES_SECRET_KEY,
    AES_IV
  );
  let decrypted = decipher.update(encrypted, "base64", "utf8");
  decrypted += decipher.final("utf8");
  return decrypted;
}

module.exports = {
  aesEncrypt,
  aesDecrypt,
};
