import { request } from "@/utils/request"
import { useUserStore } from "@/store/user"
// 获取工单列表
export function getWorkOrderListApi(params: any) {
  return request.get("/workorder/work-order-list", {
    params
  })
}

// 报修管理员获取工单列表
export function getWorkOrderListByRepairManagerApi(params: any) {
  return request.get("/workorder/work-order-list-by-repair-manager", {
    params
  })
}

// 获取工单详情
export function getWorkOrderDetailApi(id: string) {
  return request.get(`/workorder/work-order-detail?workOrderId=${id}`)
}

// 创建工单
export function createWorkOrderApi(params: any) {
  return request.post("/workorder/create-work-order", { params, checkSensitive: true })
}

// 编辑工单
export function editWorkOrderApi(params: any) {
  return request.put("/workorder/edit-work-order", { params, checkSensitive: true })
}

// 上传附件
export function uploadWorkOrderAttachmentApi(params: any) {
  return request.uploadFile("/workorder/upload-work-order-attachment", {
    filePath: params.filePath
  })
}

// 选择报修管理人确认方案
export function selectRepairManagerConfirmApi(params: any) {
  return request.post("/workorder/select-repair-manager-confirm", { params })
}

// 报修方确认维修方案
export function repairConfirmApi(params: any) {
  return request.post("/workorder/repair-confirm", { params })
}

// 报修方确认完成
export function repairFinishConfirmApi(params: any) {
  return request.post("/workorder/repair-finish-confirm", { params, checkSensitive: true })
}

// 取消工单
export function cancelWorkOrderApi(params: any) {
  return request.post("/workorder/cancel-work-order", { params })
}

// 选择确认完成报修管理人
export function selectFinishManagerConfirmApi(params: any) {
  return request.post("/workorder/select-finish-manager-confirm", { params })
}
