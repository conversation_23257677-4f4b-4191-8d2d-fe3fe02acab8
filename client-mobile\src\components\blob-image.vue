<template>
  <view class="blob-image-container">
    <image v-if="blobUrl" :src="blobUrl" mode="aspectFit" @click="handleClick" />
    <image
      v-else
      style="width: 50rpx; height: 50rpx"
      src="@/static/common/image.svg"
      mode="aspectFit"
      @click="handleClick" />

    <!-- 大图预览 -->
    <view v-if="showPreview" class="image-container" :style="{ top: systemStore.navBarHeight + 'px', height: `calc(100% - ${systemStore.navBarHeight}px)` }">
      <image :src="blobUrl"  controls style="width: 80%; height: 80%" mode="aspectFit"></image>
      <image class="close-icon" src="@/static/common/close.svg" mode="aspectFit" @click="showPreview = false" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from "vue"
import { getFileData } from "@/api"
import { useSystemStore } from "@/store/system"

const systemStore = useSystemStore()

interface PropsType {
  fileId: string
  default?: string
  iconSize?: number
  width?: number
  height?: number
}
const props = withDefaults(defineProps<PropsType>(), { default: "", iconSize: 20 })

const blobUrl = ref("")
const loading = ref(false)

watch(
  () => props.fileId,
  () => {
    if (!props.fileId) return (blobUrl.value = "")
    loading.value = true
    getFileData(props.fileId)
      .then(response => {
        if (response) {
          // #ifdef H5
          blobUrl.value = URL.createObjectURL(new Blob([response], { type: "application/octet-stream" }))
          // #endif
          // #ifndef H5
          blobUrl.value = "data:image/*;base64," + uni.arrayBufferToBase64(response)
          // #endif
          loading.value = false
        }
      })
      .catch(err => {
        blobUrl.value = ""
        loading.value = false
      })
  },
  {
    immediate: true
  }
)

const showPreview = ref(false)
function handleClick() {
  showPreview.value = true
}
</script>

<style lang="scss" scoped>
.blob-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10rpx;
  box-sizing: border-box;
  border: 1rpx solid #ccc;
  image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.image-container {
  width: 100%;
  position: fixed;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  .close-icon {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    cursor: pointer;
    width: 60rpx;
    height: 60rpx;
    z-index: 2;
  }
}
</style>
