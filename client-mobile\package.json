{"name": "client-mobile", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:app-harmony": "uni -p app-harmony", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-weixin": "uni -p mp-weixin", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:app-harmony": "uni build -p app-harmony", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-weixin": "uni build -p mp-weixin", "type-check": "vue-tsc --noEmit", "build-image": "node scripts/build-image.cjs"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-baidu": "3.0.0-4060620250520001", "@dcloudio/uni-mp-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-mp-jd": "3.0.0-4060620250520001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060620250520001", "@dcloudio/uni-mp-lark": "3.0.0-4060620250520001", "@dcloudio/uni-mp-qq": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-mp-xhs": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "@dcloudio/uni-ui": "^1.5.6", "chalk": "^4.1.2", "dayjs": "^1.11.13", "lodash": "^4.17.21", "moment": "^2.30.1", "pinia": "^2.0.0", "pinia-plugin-persistedstate": "^2.4.0", "pkg-types": "^2.1.0", "shelljs": "^0.9.2", "unimport": "^5.0.1", "unplugin-auto-import": "^19.3.0", "vue": "3.5.17", "vue-i18n": "9.14.4", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "@vue/runtime-core": "3.5.17", "@vue/tsconfig": "^0.1.3", "eslint": "^9.9.0", "eslint-plugin-vue": "^9.27.0", "prettier": "^3.3.3", "sass": "^1.77.8", "sass-loader": "^10.1.1", "typescript": "^4.9.5", "vite": "5.2.8", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^1.0.24"}}