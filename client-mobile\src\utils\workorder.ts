import { WorkOrderStatusEnum, SettlementTypeEnum } from "@/configs"

export const getWorkOrderStatus = (status: string) => {
  switch (status) {
    case WorkOrderStatusEnum.WAITING_DISPATCH:
      return {
        label: "待派单",
        type: "warning",
        value: WorkOrderStatusEnum.WAITING_DISPATCH
      }
    case WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH:
      return {
        label: "待维保公司派单",
        type: "warning",
        value: WorkOrderStatusEnum.WAITING_COMPANY_DISPATCH
      }
    case WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME:
      return {
        label: "待维保人员上门",
        type: "warning",
        value: WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME
      }
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN:
      return {
        label: "待维保方出具维修方案",
        type: "warning",
        value: WorkOrderStatusEnum.WAITING_REPAIR_PLAN
      }
    case WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT:
      return {
        label: "待平台审核维修方案",
        type: "warning",
        value: WorkOrderStatusEnum.WAITING_PLATFORM_AUDIT
      }
    case WorkOrderStatusEnum.WAITING_REPAIR_PLAN_MODIFY:
      return {
        label: "待维保方修改维保方案",
        type: "warning",
        value: WorkOrderStatusEnum.WAITING_REPAIR_PLAN_MODIFY
      }
    case WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM:
      return {
        label: "待报修方确认维修方案",
        type: "warning",
        value: WorkOrderStatusEnum.WAITING_REPAIR_CONFIRM
      }
    case WorkOrderStatusEnum.PROCESSING:
      return {
        label: "处理中",
        type: "warning",
        value: WorkOrderStatusEnum.PROCESSING
      }
    case WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM:
      return {
        label: "待报修方确认维修完成",
        type: "warning",
        value: WorkOrderStatusEnum.WAITING_REPAIR_FINISH_CONFIRM
      }
    case WorkOrderStatusEnum.WAITING_PLATFORM_FINISH_CONFIRM:
      return {
        label: "待平台确认完成",
        type: "warning",
        value: WorkOrderStatusEnum.WAITING_PLATFORM_FINISH_CONFIRM
      }
    case WorkOrderStatusEnum.FINISHED:
      return {
        label: "已完成",
        type: "success",
        value: WorkOrderStatusEnum.FINISHED
      }
    case WorkOrderStatusEnum.CANCELLED:
      return {
        label: "已取消",
        type: "info",
        value: WorkOrderStatusEnum.CANCELLED
      }
    case WorkOrderStatusEnum.NO_NEED_REPAIR:
      return {
        label: "无需维修",
        type: "info",
        value: WorkOrderStatusEnum.NO_NEED_REPAIR
      }
    default:
      return {
        label: "未知",
        type: "info",
        value: status
      }
  }
}

// 获取结算方式
export const getSettlementType = (settlementType: string) => {
  switch (settlementType) {
    case SettlementTypeEnum.IMMEDIATELY:
      return "一单一结"
    case SettlementTypeEnum.YEARLY:
      return "包年"
    case SettlementTypeEnum.YEARLY_WITHOUT_PARTS:
      return "包年配件另算"
    default:
      return "--"
  }
}
