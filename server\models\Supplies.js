const mongoose = require("mongoose");

const suppliesSchema = new mongoose.Schema(
  {
    id: {
      type: String,
      required: true,
      unique: true,
    },
    // 物资类型
    type: {
      type: String,
      required: true,
    },
    // 物资名称
    name: {
      type: String,
      required: true,
    },
    // 规格
    specification: {
      type: String,
    },
    // 型号
    model: {
      type: String,
    },
    // 计量单位
    unit: {
      type: String,
    },
    // 成本价
    costPrice: {
      type: Number,
      required: true,
    },
    // 售价
    sellingPrice: {
      type: Number,
      required: true,
    },
    // 费用承担方式
    paymentMethod: {
      type: String,
      // 平台承担或维修单位承担或报修单位自行购买
      enum: ["PLATFORM", "MAINTENANCE_UNIT"],
      required: true,
    },
  },
  {
    timestamps: true,
  }
);
const Supplies = mongoose.model("Supplies", suppliesSchema);

module.exports = Supplies;
