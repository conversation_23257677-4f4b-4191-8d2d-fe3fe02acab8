<template>
  <div v-for="(card, index) in props.colSetting" :key="card.id">
    <DndItem
      v-if="card.id !== 'selection'"
      :id="card.id"
      :checked="card.checked"
      :must="card.must"
      :label="card.label"
      :index="index"
      :move-card="moveCard"
      @check="handleCheckChange"
    />
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash/fp"
import type { TableColSettingItem } from "@/types"
import { watch } from "vue"
import DndItem from "./DndItem.vue"

const props = defineProps<{
  colSetting: Array<TableColSettingItem>
}>()

const emit = defineEmits(["update:colSetting"])

// 勾选状态切换
function handleCheckChange(id: string, nextCheckVal: boolean) {
  const nextColSetting = cloneDeep(props.colSetting)
  const col = nextColSetting.find(item => item.id === id)
  if (col) col.checked = nextCheckVal
  emit("update:colSetting", nextColSetting)
}

const moveCard = (dragId: string, dropId: string) => {
  const nextColSetting = cloneDeep(props.colSetting)
  const fromIndex = nextColSetting.findIndex(item => item.id === dragId)
  const card = nextColSetting[fromIndex]
  const toIndex = nextColSetting.findIndex(item => item.id === dropId)
  nextColSetting.splice(fromIndex, 1)
  nextColSetting.splice(toIndex, 0, card)
  emit("update:colSetting", nextColSetting)
}
</script>
