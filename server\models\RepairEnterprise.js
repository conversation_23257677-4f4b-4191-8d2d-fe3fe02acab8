const mongoose = require("mongoose");
const { aesEncrypt, aesDecrypt } = require("../utils/encryption.js");

const RepairEnterpriseSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, unique: true }, // 企业ID
    companyName: { type: String, required: true }, // 公司名称
    legalPerson: { type: String, required: true }, // 法人姓名
    contactPersonId: { type: String, required: true }, // 联系人ID
    contactPerson: { type: String, required: true }, // 联系人姓名
    contactPhone: {
      type: String,
      required: true,
      set: aesEncrypt,
      get: aesDecrypt,
    }, // 联系人电话
    companyAddress: { type: String, required: true }, // 公司办公地址
    operationArea: { type: [String], required: true }, // 所属区域，可多选
    operationAreaLabel: { type: String, required: true }, // 所属区域标签
    status: {
      type: String,
      enum: ["ENABLED", "DISABLED"],
      default: "ENABLED",
    }, // 状态

    // 结算信息
    settlements: [
      {
        id: { type: String, required: true, unique: true, sparse: true },
        contractId: {
          type: String,
          required: true,
          unique: true,
          sparse: true,
        }, // 关联合同
        contractName: { type: String, required: true },
        settlementType: {
          type: String,
          enum: ["IMMEDIATELY", "YEARLY", "YEARLY_WITHOUT_PARTS"], // 一单一结，包年，包年配件另算
          required: true,
        }, // 结算方式
        contractPeriod: {
          type: [Number],
          required: true,
        },
        status: {
          type: String,
          enum: ["ENABLED", "DISABLED"],
          default: "ENABLED",
        }, // 状态
        //配置项目
        serviceItems: [
          {
            serviceItemIds: { type: [String], required: true }, // 服务项目_id
            serviceClassId: { type: String, required: true }, // 服务类型ID
            _id: false,
          },
        ],
        _id: false,
      },
    ],
    inviteCode: [
      {
        _id: false,
        code: { type: String, required: true, sparse: true, unique: true },
        roles: { type: [String], required: true, sparse: true, unique: true },
      },
    ],
  },
  {
    timestamps: true,
  }
);

// 开启toJSON和toObject的getters选项,以确保在文档转换为JSON或普通对象时,
// 能正确调用Schema中定义的getter方法(如aesDecrypt)来解密敏感数据
RepairEnterpriseSchema.set("toJSON", { getters: true });
RepairEnterpriseSchema.set("toObject", { getters: true });

module.exports = {
  RepairEnterprise: mongoose.model("RepairEnterprise", RepairEnterpriseSchema),
  RepairEnterpriseSchema,
};
