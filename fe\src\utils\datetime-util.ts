import moment from "moment";

// 格式化完整时间
export const formatDatetime = (datetime: string | number) => {
  if (datetime) return moment(datetime).format("YYYY-MM-DD HH:mm:ss");
  else return "--";
};

// 格式化日期
export const formatDate = (date: string | number) => {
  if (date) return moment(date).format("YYYY-MM-DD");
  else return "--";
};

// 获取一天中00:00:00的时间戳
export const getDayStartTime = (date: Date) => {
  return date.setHours(0, 0, 0, 0);
};

// 获取一天中23:59:59的时间戳
export const getDayEndTime = (date: Date) => {
  console.log(date.setHours(23, 59, 59, 999));
  return date.setHours(23, 59, 59, 999);
};

// 按照传入的format进行格式化
export const specifiedFormatDate = (
  date: Date,
  format: string | undefined,
  defaultValue = "--"
) => {
  if (date) return moment(date).format(format);
  else return defaultValue || null;
};

// 获取一天的毫秒数
export const ONE_DAY = 24 * 60 * 60 * 1000;

// 获取今天的毫秒数
export const TODAY = Date.now() - (Date.now() % ONE_DAY) - 8 * 3600 * 1000;

export const dateFormatTypes = [
  {
    valueByEnd: "yyyy-MM-dd",
    name: "yyyy-MM-dd",
    value: "YYYY-MM-DD",
  },
  {
    valueByEnd: "yyyy/MM/dd",
    name: "yyyy/MM/dd",
    value: "YYYY/MM/DD",
  },
  {
    valueByEnd: "yyyy 年 MM 月 dd 日",
    name: "yyyy 年 MM 月 dd 日",
    value: "YYYY 年 MM 月 DD 日",
  },
  {
    valueByEnd: "yyyy/MM/dd HH:mm:ss",
    name: "yyyy/MM/dd HH:mm:ss",
    value: "YYYY/MM/DD HH:mm:ss",
  },
];

// 获取日期选择框type
export const getDateFormatType = (formDate) => {
  switch (formDate) {
    case "yyyy/MM/dd hh:mm:ss":
    case "yyyy/MM/dd HH:mm:ss":
    case "yyyy-MM-dd HH:mm:ss":
      return "datetime";
    default:
      return "date";
  }
};

// 获取近三个月的时间范围
export function getRecentThreeMonthsTimeRange(): any {
  const past = new Date();
  past.setMonth(past.getMonth() - 3);
  past.setHours(0, 0, 0, 0);

  const today = new Date();
  today.setHours(23, 59, 59, 999);
  return [past.getTime(), today.getTime()];
}

// 获取近X（数字）个Y(年、月、日)的时间范围
export function getRecentTimeRange(count, type): any {
  let past = new Date();
  if (type === "day") {
    past.setHours(0, 0, 0, 0);
    const tempPast = past.getTime() - 1000 * 60 * 60 * 24 * count;
    past = new Date(tempPast);
  } else if (type === "month") {
    past.setMonth(past.getMonth() - count);
    past.setHours(0, 0, 0, 0);
  } else if (type === "year") {
    past.setFullYear(past.getFullYear() - count);
    past.setHours(0, 0, 0, 0);
  }

  const today = new Date();
  today.setHours(23, 59, 59, 999);
  return [past.getTime(), today.getTime()];
}
