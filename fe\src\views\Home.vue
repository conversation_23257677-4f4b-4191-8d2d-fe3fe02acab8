<template>
  <div class="home-container">
    <!-- 统计卡片 -->
    <!-- <div class="stat-cards">
      <el-card class="stat-card" shadow="hover">
        <div class="card-content">
          <div class="card-icon new-icon">
            <el-icon><Plus /></el-icon>
          </div>
          <div class="card-data">
            <div class="card-number">0</div>
            <div class="card-title">今日新增工单</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="card-content">
          <div class="card-icon pending-icon">
            <el-icon><Timer /></el-icon>
          </div>
          <div class="card-data">
            <div class="card-number">0</div>
            <div class="card-title">待派单工单</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="card-content">
          <div class="card-icon staff-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="card-data">
            <div class="card-number">0</div>
            <div class="card-title">待审核维保人员</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="card-content">
          <div class="card-icon process-icon">
            <el-icon><Loading /></el-icon>
          </div>
          <div class="card-data">
            <div class="card-number">0</div>
            <div class="card-title">处理中工单</div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="card-content">
          <div class="card-icon done-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="card-data">
            <div class="card-number">0</div>
            <div class="card-title">今日完成工单</div>
          </div>
        </div>
      </el-card>
    </div> -->

    <!-- 近7日数据概览 -->
    <div class="section-header">
      <div class="section-title">近7日数据概览</div>
    </div>

    <!-- <div class="charts-container">
      <el-card class="chart-card" shadow="hover">
        <div class="chart-title">新增工单情况</div>
        <div ref="newOrdersChartRef" class="chart"></div>
      </el-card>

      <el-card class="chart-card" shadow="hover">
        <div class="chart-title">完成工单情况</div>
        <div ref="completedOrdersChartRef" class="chart"></div>
      </el-card>
    </div> -->

    <!-- 待派单工单和通知消息 -->
    <div class="bottom-container">
      <!-- 待派单工单 -->
      <div class="bottom-section">
        <div class="section-header">
          <div class="section-title">待派单工单</div>
          <div
            class="section-more"
            @click="$router.push('/workorder?status=WAITING_DISPATCH')"
          >
            更多 <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <el-table
          :data="pendingOrders"
          stripe
          v-loading="pendingLoading"
          style="width: 100%; height: calc(100% - 34px)"
          size="default"
        >
          <el-table-column prop="workOrderId" label="工单号" min-width="150" />
          <el-table-column
            prop="reportEnterprise"
            label="报修单位"
            min-width="120"
          />
          <el-table-column prop="faultDesc" label="故障描述" min-width="120" />
          <el-table-column prop="reportTime" label="报修时间" min-width="180">
            <template #default="{ row }">
              {{ formatDatetime(row.reportTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="80" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                size="small"
                @click="handleDispatch(row.workOrderId, false, null)"
              >
                派单
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 通知消息 -->
      <!-- <div class="bottom-section">
        <div class="section-header">
          <div class="section-title">通知消息</div>
          <div class="section-more">
            更多 <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <el-table
          :data="notifications"
          stripe
          style="width: 100%"
          size="default"
        >
          <el-table-column prop="content" label="消息内容" />
          <el-table-column prop="time" label="时间" width="180" />
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="scope">
              <el-button
                type="primary"
                text
                size="small"
                @click="viewNotification(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div> -->
    </div>

    <DispatchDialog
      ref="dispatchDialogRef"
      :is-transfer="isTransfer"
      :work-order-id="workOrderId"
      @submit="getWorkOrderList"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import {
  Plus,
  Timer,
  User,
  Loading,
  CircleCheck,
  ArrowRight,
} from "@element-plus/icons-vue";
import * as echarts from "echarts";
import { getWorkOrderListApi } from "@/api";
import { formatDatetime, toastError } from "@/utils";
import DispatchDialog from "./workorder/components/DispatchDialog.vue";

// 待派单工单数据
const pendingLoading = ref(false);
const pendingOrders = ref([]);

// 通知消息数据
const notifications = ref([]);

const lastWeek = ref([]);

onMounted(() => {
  // 获取近七日的时间戳
  const today = new Date();
  const oneWeekAgo = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate() - 7,
    0,
    0,
    0
  );
  lastWeek.value = [oneWeekAgo.getTime(), today.setHours(23, 59, 59, 999)];

  getWorkOrderList();
});

// 获取待派单工单
function getWorkOrderList() {
  pendingLoading.value = true;
  getWorkOrderListApi({
    filters: `status=WAITING_DISPATCH,updatedAt>${lastWeek.value[0]},updatedAt<${lastWeek.value[1]}`,
    offset: 0,
    limit: 50,
  })
    .then((res) => {
      pendingLoading.value = false;

      pendingOrders.value = res.data.data.rows || [];
    })
    .catch((err) => {
      pendingLoading.value = false;
      toastError(err, "获取待派单工单失败");
    });
}

/* ======================================= 列表操作 ======================================= */
const dispatchDialogRef = ref();
const workOrderId = ref("");
const isTransfer = ref(false);
function handleDispatch(orderId, type, data) {
  workOrderId.value = orderId;
  isTransfer.value = type;
  dispatchDialogRef.value.openDialog(data);
}

/* ======================================= 图表相关 ======================================= */
const newOrdersChartRef = ref(null);
const completedOrdersChartRef = ref(null);
let newOrdersChart = null;
let completedOrdersChart = null;

// 初始化图表
const initCharts = () => {
  if (newOrdersChartRef.value && completedOrdersChartRef.value) {
    // 新增工单图表
    newOrdersChart = echarts.init(newOrdersChartRef.value);
    const newOrdersOption = {
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: ["03-06", "03-07", "03-08", "03-09", "03-10", "03-11", "03-12"],
      },
      yAxis: {
        type: "value",
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
      },
      series: [
        {
          name: "新增工单",
          type: "line",
          smooth: true,
          data: [0, 0, 0, 0, 0, 0, 0],
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(64, 158, 255, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(64, 158, 255, 0.1)",
                },
              ],
            },
          },
          itemStyle: {
            color: "#409EFF",
          },
          lineStyle: {
            width: 2,
            color: "#409EFF",
          },
        },
      ],
    };
    newOrdersChart.setOption(newOrdersOption);

    // 完成工单图表
    completedOrdersChart = echarts.init(completedOrdersChartRef.value);
    const completedOrdersOption = {
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: ["03-06", "03-07", "03-08", "03-09", "03-10", "03-11", "03-12"],
      },
      yAxis: {
        type: "value",
        splitLine: {
          lineStyle: {
            type: "dashed",
          },
        },
      },
      series: [
        {
          name: "完成工单",
          type: "line",
          smooth: true,
          data: [0, 0, 0, 0, 0, 0, 0],
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(103, 194, 58, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(103, 194, 58, 0.1)",
                },
              ],
            },
          },
          itemStyle: {
            color: "#67C23A",
          },
          lineStyle: {
            width: 2,
            color: "#67C23A",
          },
        },
      ],
    };
    completedOrdersChart.setOption(completedOrdersOption);
  }
};

// 窗口大小变化时重置图表大小
const resizeCharts = () => {
  if (newOrdersChart) {
    newOrdersChart.resize();
  }
  if (completedOrdersChart) {
    completedOrdersChart.resize();
  }
};

// 查看通知详情
const viewNotification = (row) => {
  ElMessage.info(`查看通知：${row.content}`);
};

onMounted(() => {
  // 初始化图表
  setTimeout(() => {
    initCharts();
    window.addEventListener("resize", resizeCharts);
  }, 200);
});

// 组件销毁时移除事件监听
onUnmounted(() => {
  window.removeEventListener("resize", resizeCharts);
  if (newOrdersChart) {
    newOrdersChart.dispose();
  }
  if (completedOrdersChart) {
    completedOrdersChart.dispose();
  }
});
</script>

<style scoped>
.home-container {
  padding: 0;
  height: 100%;
}

.stat-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 20px;
}

.stat-card {
  width: calc(20% - 10px);
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  box-sizing: border-box;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.card-data {
  display: flex;
  flex-direction: column;
}

.card-number {
  font-size: 20px;
  font-weight: bold;
}

.card-title {
  font-size: 14px;
  color: #606266;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
}

.section-more {
  color: #409eff;
  cursor: pointer;
}

.charts-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 20px;
}

.chart-card {
  width: calc(50% - 10px);
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.chart {
  height: 300px;
}

.bottom-container {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  height: calc(100% - 34px);
}

.bottom-section {
  flex: 1;
  min-width: 0px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  box-sizing: border-box;
}

.pending-icon {
  color: #409eff;
}

.new-icon {
  color: #67c23a;
}

.staff-icon {
  color: #e6a23c;
}

.process-icon {
  color: #f56c6c;
}

.done-icon {
  color: #909399;
}
</style>
