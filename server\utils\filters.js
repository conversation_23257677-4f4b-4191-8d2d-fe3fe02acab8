/**
 * 解析过滤条件字符串并转换为MongoDB查询对象
 * @param {string} filtersStr - 过滤条件字符串，格式如：companyName=aaa,status=ENABLED,sendDatetime>1733932800000,basicInfo.sendDatetime<1734191999999
 * @param {Object} schema - Mongoose Schema对象，用于确定字段类型
 * @returns {Object} MongoDB查询对象
 */
function getFilterObj(filtersStr, schema) {
  if (!filtersStr || typeof filtersStr !== "string" || !schema) {
    return {};
  }

  const filterObj = {};
  const filterParts = filtersStr.split(",");

  filterParts.forEach((part) => {
    let field, operator, value;

    // 处理大于/小于操作符
    if (part.includes(">")) {
      [field, value] = part.split(">");
      operator = ">";
    } else if (part.includes("<")) {
      [field, value] = part.split("<");
      operator = "<";
    } else if (part.includes("=")) {
      [field, value] = part.split("=");
      operator = "=";
    } else {
      // 无效的过滤条件
      return;
    }

    field = field.trim();
    value = value.trim();

    // 获取字段类型
    const fieldType = getFieldType(field, schema);

    // 根据字段类型和操作符处理值
    if (fieldType === "String" && operator === "=") {
      // 字符串类型使用正则匹配
      filterObj[field] = { $regex: value, $options: "i" };
    } else if (["Number", "Date"].includes(fieldType)) {
      // 数字或日期类型处理范围查询
      if (!filterObj[field]) {
        filterObj[field] = {};
      }

      if (operator === ">") {
        filterObj[field].$gt = parseValueByType(value, fieldType);
      } else if (operator === "<") {
        filterObj[field].$lt = parseValueByType(value, fieldType);
      } else if (operator === "=") {
        filterObj[field] = parseValueByType(value, fieldType);
      }
    } else {
      // 其他类型直接赋值
      if (operator === "=") {
        filterObj[field] = value;
      }
    }
  });

  return filterObj;
}

/**
 * 获取字段在Schema中的类型
 * @param {string} field - 字段路径，可能是嵌套的，如 basicInfo.sendDatetime
 * @param {Object} schema - Mongoose Schema对象
 * @returns {string} 字段类型
 */
function getFieldType(field, schema) {
  // 处理嵌套字段
  const fieldParts = field.split(".");
  let currentSchema = schema;
  let currentPath = "";

  for (let i = 0; i < fieldParts.length; i++) {
    const part = fieldParts[i];
    currentPath = currentPath ? `${currentPath}.${part}` : part;

    // 获取当前字段的路径
    const pathSchema = currentSchema.path(currentPath);

    if (!pathSchema && i < fieldParts.length - 1) {
      // 如果是嵌套对象，尝试获取子模式
      const subDoc = currentSchema.path(part);
      if (subDoc && subDoc.schema) {
        currentSchema = subDoc.schema;
        currentPath = "";
      } else {
        // 找不到对应的路径
        return "Unknown";
      }
    } else if (pathSchema) {
      // 找到了字段路径
      return pathSchema.instance; // 返回字段类型 (String, Number, Date 等)
    }
  }

  return "Unknown";
}

/**
 * 根据字段类型解析值
 * @param {string} value - 值的字符串表示
 * @param {string} type - 字段类型
 * @returns {any} 解析后的值
 */
function parseValueByType(value, type) {
  if (type === "Number") {
    return Number(value);
  } else if (type === "Date") {
    return new Date(Number(value));
  }
  return value;
}

module.exports = {
  getFilterObj,
};
