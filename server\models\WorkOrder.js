const mongoose = require("mongoose");
const { aesEncrypt, aesDecrypt } = require("../utils/encryption.js");

const workOrderSchema = new mongoose.Schema(
  {
    //工单号
    workOrderId: {
      type: String,
      unique: true,
      required: true,
    },
    // 维修单位
    maintenanceUnitId: {
      type: String,
    },
    // 维修人
    maintenanceUserId: {
      type: String,
    },
    // 报修单位
    reportUnitId: {
      type: String,
      required: true,
    },
    // 详细地址
    detailLocation: {
      type: String,
      required: true,
    },
    // 报修人
    reporterId: {
      type: String,
      required: true,
    },
    // 报修人联系方式
    reporterPhone: {
      type: String,
      required: true,
      set: aesEncrypt,
      get: aesDecrypt,
    },
    // 确认方案报修管理人
    reportManagerId: {
      type: String,
    },
    // 确认方案报修管理人联系方式
    reportManagerPhone: {
      type: String,
      set: aesEncrypt,
      get: aesDecrypt,
    },
    // 确认完成报修管理人
    finishManagerId: {
      type: String,
    },
    // 确认完成报修管理人联系方式
    finishManagerPhone: {
      type: String,
      set: aesEncrypt,
      get: aesDecrypt,
    },
    // 服务类别
    serviceClass: {
      type: String,
      required: true,
    },
    //报修途径
    reportWay: {
      type: String,
      required: true,
      // 电话报修,自主填报报修
      enum: ["PHONE", "SELF_REPORT"],
    },
    // 故障描述
    faultDesc: {
      type: String,
      required: true,
    },
    // 机器信息
    deviceInfo: {
      type: String,
    },
    // 附件
    attachments: [
      {
        fileName: { type: String, required: true }, // 文件名
        fileId: { type: String, required: true }, // 文件ID
        fileType: { type: String, required: true }, // 文件类型
        _id: false,
      },
    ],
    // 报修时间
    reportTime: {
      type: Number,
      default: () => new Date().getTime(),
    },
    // 工单状态
    status: {
      type: String,
      enum: [
        "WAITING_DISPATCH",
        "WAITING_COMPANY_DISPATCH",
        "WAITING_REPAIR_PERSON_COME",
        "WAITING_REPAIR_PLAN",
        "WAITING_PLATFORM_AUDIT",
        "WAITING_REPAIR_PLAN_MODIFY",
        "WAITING_REPAIR_CONFIRM",
        "PROCESSING",
        "WAITING_REPAIR_FINISH_CONFIRM",
        "WAITING_PLATFORM_FINISH_CONFIRM",
        "FINISHED",
        "CANCELLED",
        "NO_NEED_REPAIR",
      ],
      default: "WAITING_DISPATCH",
    },

    // 维修时间
    repairTime: {
      type: Number,
    },
    // 完成时间
    completeTime: {
      type: Number,
    },
    // 维修结果
    repairResult: {
      type: String,
      default: "",
    },
    // 维修评分
    repairScore: {
      type: Number,
      default: 0,
    },
    // 维修评价
    evaluation: {
      type: String,
      default: "",
    },

    // 服务项目
    serviceItems: [
      {
        _id: false,
        // 服务类别
        serviceClass: {
          type: String,
          required: true,
        },
        // 服务项目_id
        serviceItemId: {
          type: String,
          required: true,
        },
        // 服务项目编号
        code: {
          type: String,
          required: true,
        },
        //服务项目
        serviceItem: {
          type: String,
          required: true,
        },
        // 单位
        unit: {
          type: String,
          required: true,
        },
        // 数量
        quantity: {
          type: Number,
          required: true,
          default: 0,
        },
        // 报修方结算售价
        unitPrice: {
          type: Number,
          required: true,
        },
        // 报修方结算总价
        subtotal: {
          type: Number,
          required: true,
        },
        // 维保方结算id
        maintenanceSettlementId: {
          type: String,
        },
        // 报修方结算id
        reportSettlementId: {
          type: String,
        },
        // 报修方结算方式
        reportUnitSettlementType: {
          type: String,
          enum: ["IMMEDIATELY", "YEARLY", "YEARLY_WITHOUT_PARTS"], // 一单一结，包年，包年配件另算
        },
      },
    ],
    // 配件更换明细
    parts: [
      {
        _id: false,
        id: {
          type: String,
          required: true,
        },
        // 物资类型
        type: {
          type: String,
          required: true,
        },
        // 物资名称
        name: {
          type: String,
          required: true,
        },
        // 规格
        specification: {
          type: String,
        },
        // 型号
        model: {
          type: String,
        },
        // 计量单位
        unit: {
          type: String,
        },
        // 成本价
        costPrice: {
          type: Number,
          required: true,
        },
        // 售价
        sellingPrice: {
          type: Number,
          required: true,
        },
        // 费用承担方式
        paymentMethod: {
          type: String,
          // 平台承担或维修单位承担或报修单位自行购买
          enum: ["PLATFORM", "MAINTENANCE_UNIT"],
          required: true,
        },
      },
    ],
    // 处理意见
    processingOpinion: {
      type: String,
      default: "",
    },
    // 交通距离
    distance: {
      type: Number,
      default: 0,
    },
    //服务费总价
    serviceFee: {
      type: Number,
      default: 0,
    },
    // 交通费
    transportFee: {
      type: Number,
      default: 0,
    },
    // 配件总价
    partsTotal: {
      type: Number,
      default: 0,
    },
    // 总价
    totalPrice: {
      type: Number,
      default: 0,
    },
    // 维修过程
    repairProcess: [
      {
        _id: false,
        // 处理时间
        processTime: {
          type: Number,
          default: new Date().getTime(),
        },
        // 处理人
        processor: {
          type: String,
          required: true,
        },
        // 处理类型
        processType: {
          type: String,
          required: true,
        },
        // 处理结果
        processResult: {
          type: String,
          required: true,
        },
        // 原因
        reason: {
          type: String,
          default: "",
        },
        // 地点位置
        location: {
          type: String,
          default: "",
        },
        // 维修评价
        evaluation: {
          type: String,
          default: "",
        },
        // 维修评分
        repairScore: {
          type: Number,
          default: 0,
        },
        // 维修结果
        repairResult: {
          type: String,
          default: "",
        },
        // 备注
        remark: {
          type: String,
          default: "",
        },
        // 签名
        signatureBase64: {
          type: String,
          default: "",
        },
        // 附件
        attachments: [
          {
            fileName: { type: String, required: true }, // 文件名
            fileId: { type: String, required: true }, // 文件ID
            fileType: { type: String, required: true }, // 文件类型
            _id: false,
          },
        ],
      },
    ],
    // 报修来源类型
    source: {
      type: String,
      enum: ["PLATFORM", "MAINTENANCE_APP", "REPAIR_APP"],
      required: true,
    },
    creatorId: {
      type: String,
      required: true,
    },
    creatorUnitId: {
      type: String,
    },
  },
  {
    timestamps: true,
  }
);

// 生成工单号的方法
workOrderSchema.statics.generateWorkOrderId = function () {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  // 基础日期部分
  const baseId = `${year}${month}${day}`;

  // 生成5位随机数
  const randomNum = Math.floor(Math.random() * 100000)
    .toString()
    .padStart(5, "0");

  return `${baseId}${randomNum}`;
};

// 开启toJSON和toObject的getters选项,以确保在文档转换为JSON或普通对象时,
// 能正确调用Schema中定义的getter方法(如aesDecrypt)来解密敏感数据
workOrderSchema.set("toJSON", { getters: true });
workOrderSchema.set("toObject", { getters: true });

const WorkOrder = mongoose.model("WorkOrder", workOrderSchema);

module.exports = { WorkOrder, workOrderSchema };
