import axios from "axios";
import { useUserStore } from "@/store";
import { SENSITIVE_WORDS } from "@/configs";

const instance = axios.create({
  baseURL: "",
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 如果开启了敏感词过滤，对值进行敏感值检测，如果检测到敏感词，则取消请求
    if (config.headers.checkSensitive) {
      const checkSensitive = (value) => {
        if (!value) return false;
        if (typeof value === "string")
          return SENSITIVE_WORDS.some((word) => value.includes(word));
        if (typeof value === "object") {
          try {
            return SENSITIVE_WORDS.some((word) =>
              JSON.stringify(value).includes(word)
            );
          } catch {
            return false;
          }
        }
        return false;
      };
      if (checkSensitive(config.data) || checkSensitive(config.params)) {
        // 通过抛出 axios 的 CancelToken 取消请求
        throw new axios.Cancel("请求包含敏感词");
      }
    }

    const token = useUserStore().token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

export default instance;
