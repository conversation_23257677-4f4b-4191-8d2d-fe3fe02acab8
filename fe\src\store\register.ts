import { defineStore } from "pinia";
import { UserTypeEnum } from "@/configs";

const useRegisterStore = defineStore("REGISTER", {
  state: () => {
    return {
      userType: UserTypeEnum.PLATFORM,
      unitId: "",
      unitName: "",
      roles: [],
    };
  },
  actions: {
    clear() {
      this.userType = UserTypeEnum.PLATFORM;
      this.unitId = "";
      this.unitName = "";
      this.roles = [];
    },
  },

  persist: true,
});

export default useRegisterStore;
