/* ================ 请求地址 ============== */

// const BASE_LOCAL_URL = "https://jsx-server.signit.cn"
// const BASE_LOCAL_URL = "http://**************:3000"
const BASE_LOCAL_URL = ""
const BASE_PRE_URL = ""
const BASE_PROD_URL = ""

/* ================ 请求前缀 ============== */

let BASE_URL_PREFIX = "/api"
// #ifndef H5
BASE_URL_PREFIX = "/api"
// #endif

/* ================ 根据环境配置地址 ============== */

// 请求域名
let BASE_URL = BASE_LOCAL_URL

// 当前环境 dev trial release develop
let env = "dev"

// #ifdef MP-WEIXIN
const accountInfo = uni.getAccountInfoSync()
env = accountInfo.miniProgram.envVersion
// #endif

console.log(`output->env`, env)

// 正式版
if (env === "release") {
  BASE_URL = BASE_PROD_URL
}
// 体验版
else if (env === "trial") {
  BASE_URL = BASE_PRE_URL
} else {
  BASE_URL = BASE_LOCAL_URL
}

export { BASE_URL, BASE_URL_PREFIX }
