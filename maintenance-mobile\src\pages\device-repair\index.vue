<template>
  <view class="page-container">
    <NavBar title="设备报修" />
    <view class="page-content-with-nav repair-form">
      <form @submit="submitForm">
        <view class="form-section">
          <view class="section-title">基本信息</view>

          <view class="form-item">
            <view class="label"><text class="required">*</text>报修单位</view>
            <picker
              @change="onReportUnitChange"
              :value="reportUnitIndex"
              :range="reportUnits"
              range-key="companyName"
              :disabled="reportUnitsLoading || reportUnits.length === 0">
              <view
                class="picker-value"
                :class="{
                  placeholder: !formData.reportUnitName,
                  loading: reportUnitsLoading,
                  empty: reportUnits.length === 0
                }"
                @click="
                  e => {
                    if (reportUnits.length === 0) showToast('暂无数据')
                  }
                ">
                <view>
                  <uni-icons
                    v-if="reportUnitsLoading"
                    type="spinner-cycle"
                    size="16"
                    color="#999"
                    class="loading-icon"></uni-icons>
                  <text v-if="reportUnitsLoading">加载中...</text>
                  <text v-else>{{ formData.reportUnitName || "请选择" }}</text>
                </view>
                <uni-icons type="right" size="16" color="#999"></uni-icons>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <view class="label"><text class="required">*</text>报修人</view>
            <view v-if="!formData.reportUnitId" class="input disabled">请先选择报修单位</view>
            <picker
              v-else
              @change="onReporterChange"
              :value="reporterIndex"
              :range="reporterList"
              range-key="username"
              :disabled="reporterListLoading || reporterList.length === 0">
              <view
                class="picker-value"
                :class="{
                  placeholder: !formData.reporterName,
                  loading: reporterListLoading,
                  empty: reporterList.length === 0
                }"
                @click="
                  e => {
                    if (reporterList.length === 0) showToast('暂无数据')
                  }
                ">
                <view>
                  <uni-icons
                    v-if="reporterListLoading"
                    type="spinner-cycle"
                    size="16"
                    color="#999"
                    class="loading-icon"></uni-icons>
                  <text v-if="reporterListLoading">加载中...</text>
                  <text v-else>{{ formData.reporterName || "请选择" }}</text>
                </view>
                <uni-icons type="right" size="16" color="#999" v-if="reporterList.length > 0"></uni-icons>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <view class="label"><text class="required">*</text>报修人联系方式</view>
            <view class="input disabled">{{ formData.reporterId ? formData.reporterPhone : "请选择报修人" }}</view>
          </view>

          <view class="form-item">
            <view class="label"><text class="required">*</text>报修类型</view>
            <picker
              @change="onServiceClassChange"
              :value="serviceClassIndex"
              :range="serviceClassOptions"
              range-key="serviceClass"
              :disabled="serviceClassLoading || serviceClassOptions.length === 0">
              <view
                class="picker-value"
                :class="{
                  placeholder: !formData.serviceClass,
                  loading: serviceClassLoading,
                  empty: serviceClassOptions.length === 0
                }"
                @click="
                  e => {
                    if (serviceClassOptions.length === 0) showToast('暂无数据')
                  }
                ">
                <view>
                  <uni-icons
                    v-if="serviceClassLoading"
                    type="spinner-cycle"
                    size="16"
                    color="#999"
                    class="loading-icon"></uni-icons>
                  <text v-if="serviceClassLoading">加载中...</text>
                  <text v-else>{{ getServiceClassName() || "请选择" }}</text>
                </view>
                <uni-icons type="right" size="16" color="#999" v-if="serviceClassOptions.length > 0"></uni-icons>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <view class="label"><text class="required">*</text>详细地点</view>
            <input
              class="input"
              v-model="formData.detailLocation"
              placeholder="请输入"
              maxlength="200"
              @input="updateOrderDetailStore" />
          </view>

          <view class="form-item">
            <view class="label"><text class="required">*</text>故障描述</view>
            <textarea
              class="textarea"
              v-model="formData.faultDesc"
              placeholder="请输入"
              maxlength="200"
              @input="updateOrderDetailStore" />
            <view class="word-count">{{ formData.faultDesc.length }}/200</view>
          </view>

          <view class="form-item">
            <view class="label">机器信息</view>
            <textarea
              class="textarea"
              v-model="formData.deviceInfo"
              placeholder="品牌，型号"
              maxlength="200"
              @input="updateOrderDetailStore" />
            <view class="word-count">{{ formData.deviceInfo.length }}/200</view>
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">附件信息</view>
          <view class="section-hint">请注意上传的数据中是否包含敏感信息</view>

          <view class="upload-list">
            <view class="image-item" v-for="(item, index) in showAttachment" :key="index">
              <template v-if="isImage(item.fileType)">
                <BlobImage
                  v-if="item.status === 'success'"
                  style="width: 200rpx; height: 200rpx"
                  :file-id="item.fileId" />
                <image v-else :src="item.tempPath" style="width: 200rpx; height: 200rpx" mode="aspectFit"></image>
                <view v-if="item.status !== 'success'" class="mask">{{ item.message }}</view>
              </template>
              <template v-else-if="isVideo(item.fileType)">
                <BlobVideo style="width: 200rpx; height: 200rpx" :file-id="item.fileId" />
                <view v-if="item.status !== 'success'" class="mask">{{ item.message }}</view>
              </template>
              <view class="file-name">{{ item.fileName }}</view>
              <text class="delete-icon" @click.stop="deleteImage(item)">×</text>
            </view>
            <view class="upload-item" @click="chooseAttachment">
              <text class="upload-icon">+</text>
            </view>
          </view>
        </view>

        <view class="form-actions">
          <button class="submit-btn" form-type="submit">提交</button>
        </view>
      </form>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue"
import { NavBar, BlobImage, BlobVideo } from "@/components"
import {
  getServiceContentApi,
  createWorkOrderApi,
  uploadWorkOrderAttachmentApi,
  getCustomerUnitList,
  getCustomerUnitStaffList,
  deleteFile
} from "@/api"
import { showToast, showLoading, hideLoading, navigateBack } from "@/utils"
import { onLoad } from "@dcloudio/uni-app"
import { useSystemStore, useWorkOrderDetailStore } from "@/store"

const systemStore = useSystemStore()
const workOrderStore = useWorkOrderDetailStore()

// 表单数据
const formData = reactive({
  reportUnitId: "",
  reportUnitName: "",
  reporterId: "",
  reporterName: "",
  reporterPhone: "",
  detailLocation: "",
  serviceClass: "",
  faultDesc: "",
  deviceInfo: "",
  reportWay: "SELF_REPORT", // 自助报修
  attachments: [] as any[]
})

// 附件显示列表
const showAttachment = ref<any[]>([])

onLoad(async () => {
  getServiceClass()
  await getReportUnits()

  // 如果前面没有页面栈，则恢复store的缓存数据
  if (!systemStore.haveOtherPages()) recoverOrderDetailStoreInfo()
  // 有的话则更新store
  else updateOrderDetailStore()
})

/* ============================================ 报修单位 ============================================ */
//  报修单位列表
const reportUnits = ref<Array<any>>([])
const reportUnitIndex = ref(0)
const reportUnitsLoading = ref(false)

// 报修单位变更
const onReportUnitChange = (e: any) => {
  const index = e.detail.value
  reportUnitIndex.value = index
  const selected = reportUnits.value[index]
  formData.reportUnitId = selected.id
  formData.reportUnitName = selected.companyName

  updateOrderDetailStore()

  getReporterList()
}

// 获取报修单位列表
const getReportUnits = async () => {
  try {
    reportUnitsLoading.value = true
    const res = await getCustomerUnitList({
      offset: 0,
      limit: 1000
    })
    reportUnits.value = res.data.data.rows || []
  } catch (error) {
    showToast("获取报修单位失败")
  } finally {
    reportUnitsLoading.value = false
  }
}

// 数据恢复
function recoverOrderDetailStoreInfo() {
  console.log("恢复数据")
  for (let key in formData) {
    if (key === "attachments") {
      formData[key] = workOrderStore[key] ? [...workOrderStore[key]] : []
      showAttachment.value = [...formData[key]]
    } else if (key === "reportWay") formData[key] = workOrderStore[key] || "SELF_REPORT"
    else formData[key] = workOrderStore[key] || ""
  }

  showAttachment.value.forEach(item => {
    item.status = "success"
  })

  if (formData.reportUnitId) getReporterList()
}

// 数据更新
function updateOrderDetailStore() {
  workOrderStore.setWorkOrderDetail({ ...formData })
}

/* ============================================ 报修人员 ============================================ */
// 报修人员列表
const reporterList = ref<Array<any>>([])
const reporterIndex = ref(0)
const reporterListLoading = ref(false)

// 获取报修人员列表
const getReporterList = async () => {
  try {
    reporterListLoading.value = true
    const res = await getCustomerUnitStaffList({
      offset: 0,
      limit: 1000,
      unitId: formData.reportUnitId,
      filters: "status=ENABLED",
      showMask: "false"
    })
    reporterList.value = res.data.data.rows || []
  } catch (error) {
    showToast("获取报修人失败")
  } finally {
    reporterListLoading.value = false
  }
}

// 报修人员变更
const onReporterChange = (e: any) => {
  const index = e.detail.value
  reporterIndex.value = index
  const selected = reporterList.value[index]
  formData.reporterId = selected.userId
  formData.reporterName = selected.username
  formData.reporterPhone = selected.phone
  updateOrderDetailStore()
}

/* ============================================ 报修类型 ============================================ */
// 报修类型选项
const serviceClassOptions = ref<any[]>([])
const serviceClassIndex = ref(0)
const serviceClassLoading = ref(false)

// 获取报修类型名称
const getServiceClassName = () => {
  if (!formData.serviceClass || serviceClassOptions.value.length === 0) return ""
  const selected = serviceClassOptions.value.find(item => item.id === formData.serviceClass)
  return selected ? selected.serviceClass : ""
}

// 获取报修类型
const getServiceClass = async () => {
  try {
    serviceClassLoading.value = true
    const res = await getServiceContentApi()
    serviceClassOptions.value = res.data.data || []
  } catch (error) {
    showToast("获取报修类型失败")
  } finally {
    serviceClassLoading.value = false
  }
}

// 报修类型变更
const onServiceClassChange = (e: any) => {
  const index = e.detail.value
  serviceClassIndex.value = index
  const selected = serviceClassOptions.value[index]
  formData.serviceClass = selected.id
  updateOrderDetailStore()
}

/* ======================================= 附件 ======================================= */
// 判断是否为图片
function isImage(fileType: string) {
  return fileType && fileType.startsWith("image/")
}

// 判断是否为视频
function isVideo(fileType: string) {
  return fileType && fileType.startsWith("video/")
}

// 选择附件类型
function chooseAttachment() {
  if (showAttachment.value.length >= 9) {
    return uni.showToast({
      title: "最多只能上传9个附件",
      icon: "none"
    })
  }
  uni.showActionSheet({
    itemList: ["上传图片", "上传视频"],
    title: "",
    success: res => {
      if (res.tapIndex === 0) {
        chooseImage()
      } else if (res.tapIndex === 1) {
        chooseVideo()
      }
    }
  })
}

// 选择图片
const chooseImage = () => {
  const remainCount = 9 - showAttachment.value.length
  if (remainCount <= 0) {
    return uni.showToast({
      title: "最多只能上传9个附件",
      icon: "none"
    })
  }
  uni.chooseImage({
    count: remainCount,
    success: res => {
      const tempFilePaths = res.tempFilePaths || []
      for (let i = 0; i < tempFilePaths.length; i++) {
        const tempPath = tempFilePaths[i]
        const fileName = `图片${new Date().getTime()}_${i}`
        showAttachment.value.push({
          tempPath: tempPath,
          fileType: "image/jpeg",
          fileName: fileName,
          fileId: "",
          status: "uploading",
          message: "上传中..."
        })
        uploadAttachment({
          path: tempPath,
          type: "image/jpeg",
          name: fileName
        })
      }
    },
    fail: () => {
      showToast("选择图片失败")
    }
  })
}

// 选择视频
const chooseVideo = () => {
  const remainCount = 9 - showAttachment.value.length
  if (remainCount <= 0) {
    return uni.showToast({
      title: "最多只能上传9个附件",
      icon: "none"
    })
  }
  uni.chooseVideo({
    count: 1,
    success: res => {
      let file = res.tempFile
      file.path = res.tempFilePath
      // #ifdef APP
      file = {
        path: res.tempFilePath,
        type: "video/mp4",
        name: `附件${new Date().getTime()}.mp4`,
        size: res.size
      }
      file.path = res.tempFilePath
      // #endif
      if (file.size > 10 * 1024 * 1024) {
        showToast("文件大小不能超过10M")
        return
      }
      let data = {
        tempPath: file.path,
        fileType: file.type,
        fileName: file.name,
        fileId: "",
        status: "uploading",
        message: "上传中..."
      }

      showAttachment.value.push(data)
      uploadAttachment(file)
    },
    fail: () => {
      showToast("选择视频失败")
    }
  })
}

// 上传附件
const uploadAttachment = (file: any) => {
  uploadWorkOrderAttachmentApi({ filePath: file.path })
    .then(res => {
      const fileId = res.fileId || ""
      const fileName = res.filename || file.name

      showAttachment.value.forEach(item => {
        if (item.tempPath === file.path) {
          item.fileId = fileId
          item.status = "success"
          item.message = ""
        }
      })

      formData.attachments.push({
        fileId: fileId,
        fileName: fileName,
        fileType: file.type
      })

      updateOrderDetailStore()
    })
    .catch(err => {
      showToast(err.message || "上传失败")
      showAttachment.value.forEach(item => {
        if (item.tempPath === file.path) {
          item.status = "error"
          item.message = "上传失败"
        }
      })
    })
}

// 删除图片
const deleteImage = async (file: any) => {
  try {
    if (file.fileId) {
      await deleteFile(file.fileId)
    }

    showAttachment.value = showAttachment.value.filter(
      item => (!item.fileId && item.tempPath !== file.tempPath) || (item.fileId && item.fileId !== file.fileId)
    )

    formData.attachments = formData.attachments.filter(item => item.fileId && item.fileId !== file.fileId)
    updateOrderDetailStore()
  } catch (error) {
    showToast("删除失败")
  }
}

// 提交表单
const submitForm = async () => {
  const uploadingFile = showAttachment.value.filter(file => file.status === "uploading")
  if (uploadingFile.length > 0) return showToast("文件上传中，请等待文件上传后提交")
  // 表单验证
  if (!formData.reportUnitId) {
    return showToast("请选择报修单位")
  }
  if (!formData.reporterId) {
    return showToast("请选择报修人")
  }
  if (!formData.serviceClass) {
    return showToast("请选择报修类型")
  }
  if (!formData.detailLocation) {
    return showToast("请输入详细地址")
  }
  if (!formData.faultDesc) {
    return showToast("请输入故障描述")
  }

  try {
    showLoading("提交中...")
    await createWorkOrderApi({
      ...formData,
      source: "MAINTENANCE_APP"
    })

    uni.showToast({
      title: "报修成功",
      icon: "success"
    })

    // 报修成功自动返回
    navigateBack()
  } catch (error) {
    showToast("报修失败")
  } finally {
    hideLoading()
  }
}
</script>

<style lang="scss">
.page-container {
  background-color: #f5f5f5;
  min-height: 100vh;

  .repair-form {
    padding: 20rpx;
  }

  .form-section {
    background-color: #fff;
    border-radius: 8rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 30rpx;
      font-weight: bold;
      margin-bottom: 30rpx;
      color: #333;
    }
    .section-hint {
      font-size: 26rpx;
      color: #aaa;
    }
  }

  .form-item {
    margin-bottom: 30rpx;

    .label {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;

      .required {
        color: #ff4d4f;
        margin-right: 4rpx;
      }
    }

    .input,
    .textarea,
    .picker-value {
      width: 100%;
      height: 80rpx;
      background-color: #f9f9f9;
      border-radius: 4rpx;
      padding: 20rpx;
      font-size: 28rpx;
      color: #333;
      box-sizing: border-box;
    }

    .input.disabled {
      background-color: #f5f5f5;
      color: #666;
    }

    .input {
      height: 68rpx;
      line-height: 28rpx;
    }

    .textarea {
      height: 160rpx;
    }

    .picker-value {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &.placeholder {
        color: #999;
      }

      &.loading {
        color: #999;

        .loading-icon {
          animation: loading-rotate 1s linear infinite;
          margin-right: 8rpx;
        }
      }

      &.empty {
        color: #999;
      }
    }

    @keyframes loading-rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    .word-count {
      text-align: right;
      font-size: 24rpx;
      color: #999;
      margin-top: 8rpx;
    }
  }

  .upload-list {
    display: flex;
    flex-wrap: wrap;
    margin: 0rpx -10rpx 0 -10rpx;

    .image-item,
    .upload-item {
      width: 200rpx;
      height: 200rpx;
      margin: 10rpx;
      border-radius: 8rpx;
      overflow: hidden;
      position: relative;
    }

    .image-item {
      background-color: #f0f0f0;

      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .mask {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24rpx;
      }

      .file-name {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        font-size: 22rpx;
        padding: 4rpx 8rpx;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .delete-icon {
        position: absolute;
        top: 0;
        right: 0;
        width: 40rpx;
        height: 40rpx;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32rpx;
        z-index: 1;
      }
    }

    .upload-item {
      background-color: #f9f9f9;
      border: 1px dashed #ddd;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .upload-icon {
        font-size: 60rpx;
        color: #999;
        line-height: 1;
      }
    }
  }

  .form-actions {
    padding: 40rpx 0;

    .submit-btn {
      width: 100%;
      height: 90rpx;
      background-color: #3c9cff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 45rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
