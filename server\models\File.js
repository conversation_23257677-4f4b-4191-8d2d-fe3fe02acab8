const mongoose = require("mongoose");
const { Schema } = mongoose;
const { v4: uuid } = require("uuid");

// 定义文件存储模型
const FileSchema = new Schema(
  {
    fileId: {
      type: String,
      required: true,
      unique: true,
    },
    filename: {
      type: String,
      required: true,
    },
    mimeType: {
      type: String,
      required: true,
    },
    size: {
      type: Number,
      required: true,
    },
    data: {
      type: Buffer,
      required: true,
    },
    metadata: {
      type: Object,
      default: {},
    },
  },
  { timestamps: true }
);

/**
 * 保存文件到数据库
 * @param {Object} fileData - 文件数据对象
 * @param {String} fileData.filename - 文件名
 * @param {String} fileData.mimeType - MIME类型
 * @param {Number} fileData.size - 文件大小(字节)
 * @param {Buffer} fileData.data - 文件二进制数据
 * @param {Object} [fileData.metadata] - 可选元数据
 * @returns {Promise<Document>} - Promise解析为保存的文件文档
 */
FileSchema.statics.saveFile = async function (fileData) {
  try {
    // 创建文件文档
    const newFile = new this({
      fileId: uuid(),
      filename: fileData.filename,
      mimeType: fileData.mimeType,
      size: fileData.size,
      data: fileData.data,
      metadata: fileData.metadata || {},
    });

    // 保存到数据库
    const savedFile = await newFile.save();
    return savedFile;
  } catch (error) {
    throw error;
  }
};

/**
 * 根据ID删除文件
 * @param {String} fileId - MongoDB文件ID
 * @returns {Promise<Object>} - Promise解析为删除操作结果
 */
FileSchema.statics.deleteFile = async function (fileIds) {
  try {
    const result = await this.deleteMany({ fileId: { $in: fileIds } });

    if (result.deletedCount === 0) {
      throw new Error("File not found or already deleted");
    }

    return result;
  } catch (error) {
    throw error;
  }
};

/**
 * 根据ID获取文件数据(不包括二进制数据)
 * @param {String} fileId - MongoDB文件ID
 * @returns {Promise<Document>} - Promise解析为文件文档(不含data字段)
 */
FileSchema.statics.getFileDetail = async function (fileId) {
  try {
    const fileDoc = await this.findOne({ fileId }).select("-data");

    if (!fileDoc) {
      throw new Error("File not found");
    }

    return fileDoc;
  } catch (error) {
    throw error;
  }
};

/**
 * 根据ID获取包含二进制数据的完整文件信息
 * @param {String} fileId - MongoDB文件ID
 * @returns {Promise<Document>} - Promise解析为完整文件文档(包含data字段)
 */
FileSchema.statics.getWithData = async function (fileId) {
  try {
    const fullFileDoc = await this.findOne({ fileId });

    if (!fullFileDoc) {
      throw new Error("File not found");
    }

    return fullFileDoc;
  } catch (error) {
    throw error;
  }
};

/**
 * 根据ID只获取文件的二进制数据
 * @param {String} fileId - MongoDB文件ID
 * @returns {Promise<Buffer>} - Promise解析为文件的二进制数据
 */
FileSchema.statics.getFileDataOnly = async function (fileId) {
  try {
    const fileDoc = await this.findOne({ fileId });

    if (!fileDoc) {
      throw new Error("File not found");
    }

    return fileDoc.data;
  } catch (error) {
    throw error;
  }
};

// 创建模型
const FileModel = mongoose.model("File", FileSchema);

module.exports = FileModel;
