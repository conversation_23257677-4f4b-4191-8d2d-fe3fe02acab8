import wxJs from "weixin-js-sdk"
import moment from "moment"
import { useUserStore } from "@/store/user"

export * from "./workorder"
export * from "./form-validator"

export const showLoading = (title: string, mask = true) => {
  return uni.showLoading({ title: title, mask: mask })
}

interface ToastOptions {
  duration?: number
  icon?: "success" | "loading" | "error" | "none"
  mask?: boolean
}

export const showToast = (title: string, options?: ToastOptions) => {
  return uni.showToast({
    title: title,
    duration: options?.duration || 2500,
    icon: options?.icon || "none",
    mask: options?.mask || false
  })
}

export const hideLoading = () => {
  uni.hideLoading()
}

export const throttle = (func: any, time: number) => {
  let start: number = Date.now()
  return function () {
    const end: number = Date.now()
    if (end - start >= time) {
      // @ts-ignore
      func.apply(this, arguments)
      start = Date.now()
    }
  }
}

export const debounce = (func: any, time: number) => {
  let timer: any
  return function () {
    clearTimeout(timer)
    timer = setTimeout(() => {
      // @ts-ignore
      func.apply(this, arguments)
    }, time)
  }
}

export function navigateBack(backUrl?: string, options: Record<string, any> = {}) {
  if (!backUrl) backUrl = useUserStore().token ? "/pages/home/<USER>" : "/pages/login/index"

  const routes = getCurrentPages()

  const delta = options.delta || 1

  console.log(routes.length, delta)

  // 有上一页，则返回上一页
  if (routes.length > delta)
    uni.navigateBack({
      fail: () => {
        uni.reLaunch({ url: backUrl })
      },
      ...options
    })
  // 无上一页，则返回指定页面，默认为返回首页
  else uni.reLaunch({ url: backUrl })
}

/* =============== time =============== */

export const formatTime = (date: any, template: string = "YYYY-MM-DD HH:mm:ss") => {
  return date ? moment(date).format(template) : "--"
}

// 获取一天中00:00:00的时间戳
export const getDayStartTime = (date: Date) => {
  date.setHours(0)
  date.setMinutes(0)
  date.setSeconds(0)
  return date.getTime()
}

// 获取一天中23:59:59的时间戳
export const getDayEndTime = (date: Date) => {
  date.setHours(23)
  date.setMinutes(59)
  date.setSeconds(59)
  return date.getTime()
}

// 睡眠延时
export const sleep = (time: number) => {
  return new Promise<void>(resolve => {
    setTimeout(() => {
      resolve()
    }, time)
  })
}
