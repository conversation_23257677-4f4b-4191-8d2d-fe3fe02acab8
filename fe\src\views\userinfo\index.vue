<template>
  <div class="userinfo-container">
    <div class="userinfo-card">
      <div class="userinfo-header">
        <h2>个人中心</h2>
      </div>
      <div class="userinfo-content">
        <div class="userinfo-item">
          <span class="item-label">姓名：</span>
          <span class="item-value">{{ userStore.username }}</span>
        </div>
        <div class="userinfo-item">
          <span class="item-label">电话：</span>
          <span class="item-value">{{ userStore.phone }}</span>
        </div>

        <div class="userinfo-action">
          <el-button type="primary" @click="openPasswordDialog">
            修改密码
          </el-button>
        </div>
      </div>
    </div>

    <!-- 修改密码弹窗 -->
    <CommonDialog
      v-model:visible="passwordDialogVisible"
      title="修改登录密码"
      :btn-loading="passwordLoading"
      :confirm-callback="handleUpdatePassword"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-suffix="："
        label-width="120px"
      >
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input
            v-model.trim="passwordForm.oldPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model.trim="passwordForm.newPassword"
            type="password"
            placeholder="新密码至少8位，数字+字母+特殊字符组合"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input
            v-model.trim="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { CommonDialog } from "@/base-components";
import { useUserStore } from "@/store";
import { updatePassword } from "@/api";
import { Message, toastError, mixPasswordValidator } from "@/utils";
import { UserTypeEnum } from "@/configs/enums/user";

const userStore = useUserStore();

// 获取用户类型标签
function getUserTypeLabel(type: string) {
  const typeMap = {
    [UserTypeEnum.PLATFORM]: "平台方",
    [UserTypeEnum.MAINTENANCE_UNIT]: "维保单位",
    [UserTypeEnum.REPAIR_UNIT]: "报修单位",
  };
  return typeMap[type] || type;
}

// 密码修改弹窗
const passwordDialogVisible = ref(false);
const passwordLoading = ref(false);
const passwordFormRef = ref<FormInstance>();
const passwordForm = ref({
  oldPassword: "",
  newPassword: "",
  confirmPassword: "",
});

// 密码验证函数
const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value === "") {
    callback(new Error("请再次输入密码"));
  } else if (value !== passwordForm.value.newPassword) {
    callback(new Error("两次输入密码不一致"));
  } else {
    callback();
  }
};

// 密码表单验证规则
const passwordRules = ref<FormRules>({
  oldPassword: [{ required: true, message: "请输入当前密码", trigger: "blur" }],
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { validator: mixPasswordValidator, trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: "blur" },
  ],
});

// 打开修改密码弹窗
function openPasswordDialog() {
  passwordDialogVisible.value = true;
  passwordForm.value = {
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  };
  // 重置表单验证
  passwordFormRef.value?.resetFields();
}

// 提交密码修改
async function handleUpdatePassword() {
  if (!passwordFormRef.value) return;

  await passwordFormRef.value.validate(async (valid) => {
    if (!valid) return;

    passwordLoading.value = true;
    try {
      await updatePassword({
        phone: userStore.phone,
        oldPassword: passwordForm.value.oldPassword,
        newPassword: passwordForm.value.newPassword,
        userType: userStore.userType,
      });
      Message.success("密码修改成功");
      passwordDialogVisible.value = false;
    } catch (err) {
      toastError(err, "密码修改失败");
    } finally {
      passwordLoading.value = false;
    }
  });
}
</script>

<style lang="less" scoped>
.userinfo-container {
  padding: 20px;
}

.userinfo-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.userinfo-header {
  background-color: #f5f7fa;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;

  h2 {
    margin: 0;
    font-size: 18px;
    color: #303133;
  }
}

.userinfo-content {
  padding: 20px;
}

.userinfo-item {
  display: flex;
  margin-bottom: 16px;
  font-size: 15px;
  line-height: 24px;

  .item-label {
    width: 100px;
    color: #606266;
  }

  .item-value {
    flex: 1;
    color: #303133;
  }
}

.userinfo-action {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}
</style>
