const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");

// 加载环境变量
dotenv.config();

// 导入路由
const authRoutes = require("./routes/auth");
const workOrderRoutes = require("./routes/workorder");
const maintenanceEnterpriseRoutes = require("./routes/maintenance-enterprise");
const dictionaryRoutes = require("./routes/dictionary");
const fileRoutes = require("./routes/file");
const repairEnterpriseRoutes = require("./routes/repair-enterprise");
const serviceContentRoutes = require("./routes/service-content");
const suppliesRoutes = require("./routes/supplies");
// 初始化Express应用
const app = express();

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true, limit: "100mb" }));
app.use(cors());

// 路由
app.use("/api/auth", authRoutes);
app.use("/api/workorder", workOrderRoutes);
app.use("/api/maintenance-enterprise", maintenanceEnterpriseRoutes);
app.use("/api/dictionary", dictionaryRoutes);
app.use("/api/file", fileRoutes);
app.use("/api/repair-enterprise", repairEnterpriseRoutes);
app.use("/api/service-content", serviceContentRoutes);
app.use("/api/supplies", suppliesRoutes);
// 基础路由
app.get("/", (req, res) => {
  res.send("即时修管理平台API正在运行");
});

// 连接MongoDB
mongoose
  .connect(process.env.MONGO_URI)
  .then(() => {
    console.log("MongoDB连接成功");
    // 启动服务器
    const PORT = process.env.PORT || 3000;
    app.listen(PORT, () => {
      console.log(`服务器运行在端口 ${PORT}`);
    });
  })
  .catch((err) => {
    console.error("MongoDB连接失败:", err.message);
    process.exit(1);
  });

// 处理未处理的promise错误
process.on("unhandledRejection", (err) => {
  console.log("未处理的错误:", err.message);
  process.exit(1);
});
