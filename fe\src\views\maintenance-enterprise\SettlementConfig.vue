<template>
  <div v-loading="detailLoading || actionLoading" class="settlement-config">
    <div class="settlement-config-header">
      <el-button :icon="ArrowLeftBold" link @click="handleCancel">
        返回
      </el-button>
    </div>

    <div class="settlement-config-content">
      <div class="section-title">
        <span>结算信息</span>
        <div class="section-actions">
          <el-button type="primary" link @click="addSettlement">
            + 添加结算方式
          </el-button>
        </div>
      </div>

      <div class="settlement-list">
        <el-form
          ref="formRef"
          :model="formData"
          label-suffix="："
          label-width="110px"
          :rules="formRules"
        >
          <div
            v-for="(item, index) in formData.settlements"
            :key="index"
            class="settlement-item"
          >
            <el-form-item
              label="关联合同号"
              :prop="`settlements.${index}.contractId`"
              :rules="[
                {
                  required: true,
                  message: '请输入关联合同号',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model.trim="item.contractId"
                placeholder="请输入关联合同号"
              />
            </el-form-item>
            <el-form-item
              label="合同名称"
              :prop="`settlements.${index}.contractName`"
              :rules="[
                {
                  required: true,
                  message: '请输入合同名称',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model.trim="item.contractName"
                placeholder="请输入合同名称"
              />
            </el-form-item>
            <el-form-item
              label="合同时间"
              :prop="`settlements.${index}.contractPeriod`"
              :rules="[
                {
                  required: true,
                  message: '请选择合同时间',
                  trigger: 'change',
                },
                {
                  validator: validateContractPeriod,
                  trigger: 'change',
                },
              ]"
            >
              <el-date-picker
                v-model="item.contractPeriod"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :shortcuts="shortcuts"
                value-format="x"
              />
            </el-form-item>
            <div class="settlement-item-header">
              <div class="settlement-actions">
                <el-button
                  type="primary"
                  :disabled="item.saveLoading || item.deleteLoading"
                  @click="openServicePriceDialog(item)"
                >
                  配置服务项目结算价格
                </el-button>
                <el-button
                  type="primary"
                  :disabled="item.deleteLoading"
                  :loading="item.saveLoading"
                  @click="validateAndSaveSettlement(item, index)"
                >
                  保存
                </el-button>
                <el-button
                  type="danger"
                  :disabled="item.saveLoading"
                  :loading="item.deleteLoading"
                  @click="removeSettlement(item, index)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>

  <!-- 服务项目结算价格配置弹窗 -->
  <ServicePriceDialog
    ref="servicePriceDialogRef"
    :settlement="currentSettlement"
    :enterpriseId="enterpriseId"
    @refresh="getEnterpriseDetail"
  />
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ArrowLeftBold } from "@element-plus/icons-vue";
import {
  SettlementTypeEnum,
  CommonStatusEnum,
  shortcuts,
  settlementTypeOptions,
} from "@/configs";
import { toastError, Message, getDayStartTime, getDayEndTime } from "@/utils";
import ServicePriceDialog from "./component/ServicePriceDialog.vue";
import {
  createMaintenanceEnterpriseSettlement,
  editMaintenanceEnterpriseSettlement,
  getMaintenanceEnterpriseSettlementList,
  deleteMaintenanceEnterpriseSettlement,
} from "@/api";

const route = useRoute();
const router = useRouter();

// 根据路由参数判断页面状态
const enterpriseId = ref("");
const enterpriseName = ref("");
const detailLoading = ref(false);
const actionLoading = ref(false);

onMounted(async () => {
  enterpriseId.value = route.query.enterpriseId;
  if (enterpriseId.value) {
    getEnterpriseDetail();
  } else {
    router.back();
  }
});

// 获取维修企业结算配置
function getEnterpriseDetail() {
  detailLoading.value = true;

  getMaintenanceEnterpriseSettlementList(enterpriseId.value)
    .then((res) => {
      formData.value.settlements = res.data.data.settlements || [];
    })
    .catch((err) => {
      toastError(err, "获取结算配置失败");
    })
    .finally(() => {
      detailLoading.value = false;
    });
}

// 表单数据
const formData = ref({
  settlements: [],
});

// 表单引用
const formRef = ref(null);

// 表单校验规则 - 动态生成
const formRules = ref({});

// 验证合同时间是否有交叉
const validateContractPeriod = (rule, value, callback) => {
  if (!value || value.length !== 2) {
    callback();
    return;
  }

  const currentIndex = parseInt(
    rule.field.match(/settlements\.(\d+)\.contractPeriod/)[1]
  );
  const currentStartTime = value[0];
  const currentEndTime = value[1];

  // 检查与其他合同时间是否有交叉
  for (let i = 0; i < formData.value.settlements.length; i++) {
    if (i === currentIndex) continue; // 跳过当前项

    const otherPeriod = formData.value.settlements[i].contractPeriod;
    if (!otherPeriod || otherPeriod.length !== 2) continue;

    const otherStartTime = otherPeriod[0];
    const otherEndTime = otherPeriod[1];

    // 检查是否有交叉
    // 情况1：当前开始时间在其他合同期间内
    // 情况2：当前结束时间在其他合同期间内
    // 情况3：其他合同开始时间在当前合同期间内
    // 情况4：其他合同结束时间在当前合同期间内
    if (
      (currentStartTime >= otherStartTime &&
        currentStartTime <= otherEndTime) ||
      (currentEndTime >= otherStartTime && currentEndTime <= otherEndTime) ||
      (otherStartTime >= currentStartTime &&
        otherStartTime <= currentEndTime) ||
      (otherEndTime >= currentStartTime && otherEndTime <= currentEndTime)
    ) {
      callback(new Error("合同时间不能与其他合同时间交叉"));
      return;
    }
  }

  callback();
};

// 添加结算方式
const addSettlement = () => {
  // 检查是否已有未保存的结算方式
  const hasUnsaved = formData.value.settlements.some(
    (item) =>
      !item.id &&
      (!item.contractId ||
        !item.contractName ||
        !item.contractPeriod ||
        item.contractPeriod.length !== 2)
  );

  if (hasUnsaved) {
    Message.warning("请先完成并保存当前的结算方式");
    return;
  }

  formData.value.settlements.push({
    id: "",
    contractId: "",
    contractName: "",
    contractPeriod: [],
    status: CommonStatusEnum.ENABLED,
    isNew: true,
  });
};

// 验证并保存单个结算方式
const validateAndSaveSettlement = (item, index) => {
  // 验证表单
  formRef.value.validateField(
    [
      `settlements.${index}.contractId`,
      `settlements.${index}.contractName`,
      `settlements.${index}.contractPeriod`,
    ],
    (valid) => {
      if (!valid) {
        return;
      }

      // 额外验证合同时间是否交叉
      if (item.contractPeriod && item.contractPeriod.length === 2) {
        const currentStartTime = item.contractPeriod[0];
        const currentEndTime = item.contractPeriod[1];

        // 检查与其他合同时间是否有交叉
        for (let i = 0; i < formData.value.settlements.length; i++) {
          if (i === index) continue; // 跳过当前项

          const otherPeriod = formData.value.settlements[i].contractPeriod;
          if (!otherPeriod || otherPeriod.length !== 2) continue;

          const otherStartTime = otherPeriod[0];
          const otherEndTime = otherPeriod[1];

          // 检查是否有交叉
          if (
            (currentStartTime >= otherStartTime &&
              currentStartTime <= otherEndTime) ||
            (currentEndTime >= otherStartTime &&
              currentEndTime <= otherEndTime) ||
            (otherStartTime >= currentStartTime &&
              otherStartTime <= currentEndTime) ||
            (otherEndTime >= currentStartTime && otherEndTime <= currentEndTime)
          ) {
            Message.error("合同时间不能与其他合同时间交叉");
            return;
          }
        }
      }

      saveSettlement(item);
    }
  );
};

// 保存单个结算方式
const saveSettlement = (item) => {
  // 设置loading状态
  item.saveLoading = true;

  // 处理合同时间，将结束时间设置为当天的23:59:59
  let contractPeriod = [...item.contractPeriod];
  if (contractPeriod && contractPeriod.length === 2) {
    // 将结束日期转换为Date对象
    const startDate = new Date(Number(contractPeriod[0]));
    const endDate = new Date(Number(contractPeriod[1]));
    contractPeriod[0] = getDayStartTime(startDate);
    contractPeriod[1] = getDayEndTime(endDate);
  }

  // 实际API调用应该类似如下：

  const requestApi = !item.id
    ? createMaintenanceEnterpriseSettlement
    : editMaintenanceEnterpriseSettlement;
  const data = {
    unitId: enterpriseId.value,
    contractId: item.contractId,
    contractName: item.contractName,
    contractPeriod: contractPeriod,
    status: item.status || "ENABLED",
    serviceItems: item.serviceItems || [],
  };
  if (item.id) data.id = item.id;

  // 使用专门的结算配置API
  requestApi(data)
    .then(() => {
      Message.success("保存成功");
      // 更新结算方式列表
      getEnterpriseDetail();
    })
    .catch((err) => {
      toastError(err, "保存失败");
    })
    .finally(() => {
      item.saveLoading = false;
    });
};

// 删除结算方式
const removeSettlement = (item, index) => {
  if (!item.id) {
    // 如果是新添加的，直接从列表中删除
    formData.value.settlements.splice(index, 1);
    return;
  }

  // 设置loading状态
  item.deleteLoading = true;

  deleteMaintenanceEnterpriseSettlement({
    unitId: enterpriseId.value,
    id: item.id,
  })
    .then(() => {
      Message.success("删除成功");
      formData.value.settlements.splice(index, 1);
    })
    .catch((err) => {
      toastError(err, "删除失败");
    })
    .finally(() => {
      item.deleteLoading = false;
    });
};

// 取消
const handleCancel = () => {
  router.back();
};

// 当前选中的结算方式
const currentSettlement = ref(null);
const servicePriceDialogRef = ref(null);

// 打开服务项目结算价格配置弹窗
const openServicePriceDialog = (item) => {
  if (!item.id) return Message.warning("请先保存结算配置");
  currentSettlement.value = item;
  servicePriceDialogRef.value?.openDialog();
};
</script>

<style lang="less" scoped>
.settlement-config {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);

  .settlement-config-header {
    padding: 10px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
  }

  .settlement-config-content {
    padding: 0px 20px 20px;
    box-sizing: border-box;
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0;
      padding-left: 10px;
      border-left: 4px solid #409eff;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .section-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }

    .settlement-list {
      flex: 1;
      min-height: 0;
      overflow-y: auto;

      .settlement-item {
        margin-bottom: 20px;
        padding: 20px;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        background-color: #fafafa;

        .settlement-item-header {
          display: flex;
          justify-content: flex-end;
          margin-top: 10px;

          .settlement-actions {
            display: flex;
            gap: 10px;
          }
        }
      }
    }
  }
}
</style>
