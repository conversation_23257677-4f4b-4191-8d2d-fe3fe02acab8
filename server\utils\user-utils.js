const { v4: uuid } = require("uuid");
const { PlatformUser } = require("../models/PlatformUser");
const { MaintenanceUser } = require("../models/MaintenanceUser");
const { RepairUser } = require("../models/RepairUser");

// 注册用户
const registerUser = async (data) => {
  const userModel =
    data.userType === "PLATFORM"
      ? PlatformUser
      : data.userType === "MAINTENANCE_UNIT"
      ? MaintenanceUser
      : RepairUser;
  // 检查用户是否已存在
  const existingUser = await userModel.findOne({
    $or: [{ phone: data.phone }],
  });
  if (existingUser) {
    return {
      success: false,
      message: "用户名或手机号已被注册",
    };
  }

  // 创建用户
  const user = await userModel.create({
    userId: uuid(),
    ...data,
  });

  return {
    success: true,
    message: "注册成功",
    data: user,
  };
};

module.exports = {
  registerUser,
};
