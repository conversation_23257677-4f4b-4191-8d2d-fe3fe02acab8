import { request } from "@/utils/request"
import { UserTypeEnum } from "@/configs"

// 登录
export function login(data: { phone: string; password: string; userType: UserTypeEnum; source: string }) {
  return request.post("/auth/login", { params: data })
}

export const logout = () => {
  return request.post("/auth/logout")
}

// 修改密码
export function updatePassword(data: {
  phone: string
  oldPassword: string
  newPassword: string
  userType: UserTypeEnum
}) {
  return request.post("/auth/update-password", { params: data })
}

// 获取入驻单位人员列表
export function getMaintenanceEnterpriseStaffList(params: any) {
  return request.get(`/maintenance-enterprise/get-maintenance-enterprise-staff-list`, {
    params: params
  })
}

// 获取报修单位列表
export const getCustomerUnitList = (params: any) => {
  return request.get("/repair-enterprise/get-repair-enterprise-list", {
    params: params
  })
}

// 获取报修人员列表
export const getCustomerUnitStaffList = (params: any) => {
  return request.get("/repair-enterprise/get-repair-user-list", {
    params: params
  })
}
