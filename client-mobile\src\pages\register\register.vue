<template>
  <view class="register-root page-container">
    <NavBar title="注册新用户" />

    <scroll-view
      class="page-content-with-nav form-container"
      scroll-y
      :style="{ height: `calc(100% - ${systemStore.navBarHeight}px - 10px)` }">
      <form>
        <view class="section">
          <view class="form-item">
            <view class="label"><span class="required">*</span> 用户名 </view>
            <input name="phone" v-model="formData.userName" placeholder="请输入用户名" />
          </view>
          <view class="form-item">
            <view class="label"><span class="required">*</span> 手机号 </view>
            <input name="phone" v-model="formData.phone" placeholder="请输入手机号" />
          </view>
          <view class="form-item">
            <view class="label"><span class="required">*</span> 密码 </view>
            <input
              name="phone"
              v-model="formData.password"
              type="password"
              placeholder="请输入至少8位数字、大小写字母和字符的组合" />
          </view>
          <view class="form-item">
            <view class="label"><span class="required">*</span> 确认密码 </view>
            <input name="phone" v-model="formData.confirmPassword" type="password" placeholder="请确认密码" />
          </view>
          <view class="form-item">
            <view class="label"><span class="required">*</span> 邀请码 </view>
            <input name="phone" v-model="formData.code" placeholder="请输入邀请码" />
          </view>
          <button class="register-btn" @click="submit">注册</button>
        </view>
      </form>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { NavBar } from "@/components"
import { reactive } from "vue"
import { useSystemStore } from "@/store/system"
import { mixPasswordValidator, mobilePhoneValidator, nameValidator, showToast, navigateBack } from "@/utils"
import { registerByCodeApi } from "@/api"

const systemStore = useSystemStore()

const formData = reactive({
  phone: "",
  userName: "",
  password: "",
  confirmPassword: "",
  code: ""
})

function submit() {
  if (!formData.userName) return showToast("用户名不可为空")
  const nameError = nameValidator(formData.userName)
  if (nameError) return showToast(nameError)

  if (!formData.phone) return showToast("手机号不可为空")
  const phoneError = mobilePhoneValidator(formData.phone)
  if (phoneError) return showToast(phoneError)

  if (!formData.password) return showToast("密码不可为空")
  const passwordError = mixPasswordValidator(formData.password)
  if (passwordError) return showToast(passwordError)

  if (!formData.confirmPassword) return showToast("请再次确认您的密码")
  if (formData.password !== formData.confirmPassword) return showToast("两次输入的密码不一致")
  if (!formData.code) return showToast("邀请码不可为空")
  uni.showLoading({
    title: "登录中"
  })

  registerByCodeApi({
    username: formData.userName,
    phone: formData.phone,
    password: formData.password,
    code: formData.code
  })
    .then(res => {
      uni.hideLoading()
      showToast("注册成功")
      navigateBack("/pages/login/login")
    })
    .catch(err => {
      uni.hideLoading()
      showToast(err.message)
    })
}
</script>

<style lang="scss" scoped>
.register-root {
  background-color: #fff;
  height: 100%;

  .section {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 40rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;

    .form-item {
      margin-bottom: 20rpx;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 10rpx;

      .label {
        font-size: 28rpx;
        color: #666;
        flex-shrink: 0;
        line-height: 1.2;
        .required {
          color: #f56c6c;
          margin-right: 10rpx;
        }
      }

      picker {
        width: 100%;
      }

      .picker-container {
        width: 100%;
        position: relative;
      }

      input,
      textarea,
      .picker-value {
        width: 100%;
        background-color: #f8f8f8;
        padding: 16rpx;
        box-sizing: border-box;
        border-radius: 8rpx;
        font-size: 28rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.loading {
          color: #999;

          .loading-icon {
            animation: loading-rotate 1s linear infinite;
            margin-right: 8rpx;
          }
        }

        &.empty {
          color: #999;
        }
      }

      @keyframes loading-rotate {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }

      input {
        height: 68rpx;
      }

      textarea {
        height: 160rpx;
      }

      .value {
        flex: 1;
        min-width: 0rpx;
        font-size: 28rpx;
        line-height: 1.2;
        color: #333;
        box-sizing: border-box;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
    .register-btn {
      margin-top: 40rpx;
      background: $uni-color-primary;
      color: #fff;
    }
  }
}
</style>
