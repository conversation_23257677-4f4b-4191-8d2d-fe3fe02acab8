const express = require("express");
const router = express.Router();
const serviceContentController = require("../controllers/service-content");
const { protect } = require("../middleware/auth");

// 获取服务类型
router.get("/service-class", protect, serviceContentController.getServiceClass);

// 添加服务类型
router.post(
  "/add-service-class",
  protect,
  serviceContentController.addServiceClass
);

// 编辑服务类型
router.put(
  "/edit-service-class",
  protect,
  serviceContentController.editServiceClass
);

// 删除服务类型
router.delete(
  "/delete-service-class/:id",
  protect,
  serviceContentController.deleteServiceClass
);

// 获取所有服务项目
router.get("/service-items", protect, serviceContentController.getServiceItem);

// 获取维保方指定合同的服务项目
router.get(
  "/maintenance-service-items",
  protect,
  serviceContentController.getMaintenanceServiceItem
);

// 获取报修方当前生效的服务项目列表
router.get(
  "/repair-service-items",
  protect,
  serviceContentController.getRepairServiceItem
);

// 添加服务项目
router.post(
  "/add-service-item",
  protect,
  serviceContentController.addServiceItem
);

// 编辑服务项目
router.put(
  "/edit-service-item",
  protect,
  serviceContentController.editServiceItem
);

// 删除服务项目
router.delete(
  "/delete-service-item/:_id",
  protect,
  serviceContentController.deleteServiceItem
);

// 批量设置某个服务类别的所有服务项目的结算价
router.post(
  "/set-service-items-price",
  protect,
  serviceContentController.setServiceItemsPrice
);

module.exports = router;
