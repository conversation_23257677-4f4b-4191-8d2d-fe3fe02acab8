import { request } from "@/utils/request"
import { useUserStore } from "@/store/user"
// 获取工单列表
export function getWorkOrderListApi(params: any) {
  return request.get("/workorder/work-order-list", {
    params
  })
}

// 获取工单详情
export function getWorkOrderDetailApi(id: string) {
  return request.get(`/workorder/work-order-detail?workOrderId=${id}`)
}

// 创建工单
export function createWorkOrderApi(params: any) {
  return request.post("/workorder/create-work-order", { params, checkSensitive: true })
}

// 编辑工单
export function editWorkOrderApi(params: any) {
  return request.put("/workorder/edit-work-order", { params, checkSensitive: true })
}

// 上传附件
export function uploadWorkOrderAttachmentApi(params: any) {
  return request.uploadFile("/workorder/upload-work-order-attachment", {
    filePath: params.filePath
  })
}

// 获取工单数量
export function getWorkOrderCountApi(params: any) {
  return request.get("/workorder/work-order-count", { params })
}

// 工单无需维修
export function setMaintenanceFree(params: any) {
  return request.post("/workorder/maintenance-free", { params, checkSensitive: true })
}

// 确认到达
export function confirmArrival(params: any) {
  return request.post("/workorder/confirm-arrival", { params })
}

// 处理与报价
export function processAndPrice(params: any) {
  return request.post("/workorder/process-and-price", { params, checkSensitive: true })
}

// 处理与报价
export function cancelProcessAndPriceApi(params: any) {
  return request.post("/workorder/cancel-process-and-price", { params, checkSensitive: true })
}

// 退单
export function returnOrder(params: any) {
  return request.post("/workorder/return-order", { params, checkSensitive: true })
}

// 完成维修
export function completeRepair(params: any) {
  return request.post("/workorder/complete-repair", { params, checkSensitive: true })
}

// 派单
export function dispatchOrderApi(params: any) {
  return request.post("/workorder/dispatch", { params })
}

// 获取保修方价格
export function getRepairPrice(params: any) {
  return request.post("/workorder/get-repair-price", { params })
}
