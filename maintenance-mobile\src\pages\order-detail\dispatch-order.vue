<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar title="选择派单工人" />

    <!-- 页面内容 -->
    <view class="page-content-with-nav dispatch-page">
      <!-- 搜索框 -->
      <view class="search-bar">
        <input
          type="text"
          v-model="searchKeyword"
          placeholder="搜索工人姓名"
          confirm-type="search"
          @confirm="handleSearch" />
        <view class="search-icon">
          <uni-icons v-if="searchKeyword" type="closeempty" size="18" color="#999" @click="clearSearch"></uni-icons>
        </view>
      </view>

      <!-- 工人列表 -->
      <scroll-view
        class="staff-list"
        scroll-y="true"
        @scrolltolower="loadMore"
        refresher-enabled="true"
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh">
        <view class="staff-item" v-for="(item, index) in staffList" :key="index" @click="selectStaff(item)">
          <view class="staff-info">
            <view class="staff-name">{{ item.username || "--" }}{{ item.phone ? `(${item.phone})` : "" }}</view>
            <view class="staff-status">
              {{ item.waitingCount || 0 }}待上门 | {{ item.waitingPlanCount || 0 }}待出具方案 |
              {{ item.processingCount || 0 }}进行中
            </view>
          </view>
          <view class="staff-select" :class="{ selected: selectedStaffId === item.userId }">
            <uni-icons v-if="selectedStaffId === item.userId" type="checkmarkempty" size="20" color="#fff"></uni-icons>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="loading-more" v-if="hasMore && !loading && staffList.length > 0">
          <text>上拉加载更多</text>
        </view>
        <view class="loading-more" v-if="loading && !refreshing">
          <text>加载中...</text>
        </view>
        <view class="no-more" v-if="!hasMore && staffList.length > 0">
          <text>没有更多了</text>
        </view>
        <view class="empty-tip" v-if="!loading && staffList.length === 0">
          <text>暂无数据</text>
        </view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="fixed-bottom">
        <button class="confirm-btn" :disabled="!selectedStaffId" @click="handleConfirmDispatch">确认派单</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue"
import { NavBar } from "@/components"
import { getMaintenanceEnterpriseStaffList } from "@/api/user"
import { dispatchOrderApi } from "@/api/workorder"
import { onLoad } from "@dcloudio/uni-app"
import { useUserStore } from "@/store/user"
import { showToast, showLoading, hideLoading, navigateBack } from "@/utils"
import { WorkOrderStatusEnum } from "@/configs"
import { CommonStatusEnum } from "@/configs/enum/user"
const userStore = useUserStore()

// 工单ID
const workOrderId = ref("")

// 分页相关
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMore = ref(true)
const loading = ref(false)
const refreshing = ref(false)

// 搜索相关
const searchKeyword = ref("")

// 人员列表
const staffList = ref<any[]>([])
const selectedStaffId = ref("")

// 页面加载
onLoad(option => {
  if (option?.workOrderId) {
    workOrderId.value = option.workOrderId
    // 初始加载数据
    getStaffList()
  }
})

// 获取工人列表
const getStaffList = async (reset = false) => {
  if (reset) {
    pageNum.value = 1
    staffList.value = []
    hasMore.value = true
  }

  if (!hasMore.value && !reset) return

  try {
    loading.value = true

    const params: any = {
      limit: pageSize.value,
      offset: (pageNum.value - 1) * pageSize.value,
      unitId: userStore.unit,
      haveCount: "true",
      filters: `status=${CommonStatusEnum.ENABLED}`,
      showMask: "false"
    }
    if (searchKeyword.value) {
      params.filters += `,username=${searchKeyword.value}`
    }

    const res = await getMaintenanceEnterpriseStaffList(params)

    if (res && res.data && res.data.data) {
      const list = res.data.data.rows || []
      const totalCount = res.data.data.pageElements.totalElements || 0

      list.forEach((item: any) => {
        item.waitingCount =
          item.count.find((count: any) => count._id === WorkOrderStatusEnum.WAITING_REPAIR_PERSON_COME)?.count || 0
        item.waitingPlanCount =
          item.count.find((count: any) => count._id === WorkOrderStatusEnum.WAITING_REPAIR_PLAN)?.count || 0

        item.processingCount = item.count.find((count: any) => count._id === WorkOrderStatusEnum.PROCESSING)?.count || 0
      })

      if (reset) {
        staffList.value = list || []
      } else {
        staffList.value = [...staffList.value, ...(list || [])]
      }

      total.value = totalCount
      hasMore.value = staffList.value.length < totalCount
      pageNum.value++
    }
  } catch (error) {
    console.error("获取工人列表失败", error)
    showToast("获取工人列表失败")
  } finally {
    loading.value = false
    if (refreshing.value) {
      refreshing.value = false
    }
  }
}

// 搜索
const handleSearch = () => {
  getStaffList(true)
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ""
  getStaffList(true)
}

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  getStaffList(true)
}

// 加载更多
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    getStaffList()
  }
}

// 选择工人
const selectStaff = (staff: any) => {
  selectedStaffId.value = staff.userId
}

// 确认派单
const handleConfirmDispatch = async () => {
  if (!selectedStaffId.value) {
    showToast("请选择工人")
    return
  }

  try {
    showLoading("派单中...", true)

    const params = {
      maintenanceUnitId: userStore.unit,
      maintenanceUserId: selectedStaffId.value,
      isTransfer: false,
      isPlatform: false,
      workOrderId: workOrderId.value
    }

    await dispatchOrderApi(params)

    hideLoading()
    showToast("派单成功")

    // 派单成功后，触发刷新工单详情页面
    uni.$emit("refreshOrderDetail")
    uni.$emit("repairOrderListRefresh")

    // 返回上一页
    navigateBack("", {
      delta: 2
    })
  } catch (error) {
    hideLoading()
    showToast("派单失败")
    console.error("派单失败", error)
  }
}
</script>

<style lang="scss" scoped>
.dispatch-page {
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  box-sizing: border-box;

  .search-bar {
    margin: 20rpx;
    background: #fff;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    position: relative;

    input {
      flex: 1;
      height: 80rpx;
      font-size: 28rpx;
      padding-right: 60rpx;
    }

    .search-icon {
      position: absolute;
      right: 30rpx;
    }
  }

  .staff-list {
    height: calc(100vh - 340rpx);
    margin-bottom: 100rpx;
    .staff-item {
      margin: 20rpx;
      background: #fff;
      border-radius: 12rpx;
      padding: 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .staff-info {
        flex: 1;

        .staff-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 10rpx;
        }

        .staff-status {
          font-size: 26rpx;
          color: #999;
        }
      }

      .staff-select {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        border: 2rpx solid #ddd;
        display: flex;
        justify-content: center;
        align-items: center;

        &.selected {
          background-color: $uni-color-primary;
          border-color: $uni-color-primary;
        }
      }
    }

    .loading-more,
    .no-more,
    .empty-tip {
      text-align: center;
      padding: 30rpx 0;
      color: #999;
      font-size: 26rpx;
    }
  }

  .fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx;
    background: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

    .confirm-btn {
      height: 90rpx;
      line-height: 90rpx;
      background-color: $uni-color-primary;
      color: #fff;
      font-size: 32rpx;
      border-radius: 45rpx;

      &[disabled] {
        background-color: #ccc;
      }
    }
  }
}
</style>
