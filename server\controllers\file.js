const File = require("../models/File");
const fs = require("fs");

/**
 * @description 获取文件二进制数据
 * @param {string} fileId 文件ID
 * @returns {Buffer} 文件二进制数据
 */
const getFileData = async (req, res) => {
  try {
    const { fileId } = req.params;

    // 使用 File 模型的 getFileDataOnly 方法只获取文件二进制数据
    const fileData = await File.getFileDataOnly(fileId);

    // 直接返回二进制数据
    res.setHeader("Content-Type", "application/octet-stream");
    return res.send(fileData);
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "获取文件数据失败",
      error: error.message || "未知错误",
    });
  }
};

/**
 * @description 删除指定文件
 * @param {string} fileId 文件ID
 * @returns {Object} 删除操作结果
 */
const deleteFile = async (req, res) => {
  try {
    const { fileIds } = req.body;

    // 使用 File 模型的 deleteFile 方法删除文件
    const result = await File.deleteFile(fileIds);

    return res.status(200).json({
      code: 200,
      message: "删除文件成功",
      data: result,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "删除文件失败",
      error: error.message || "未知错误",
    });
  }
};

/**
 * @description 上传文件
 */
const uploadFile = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 400,
        message: "未检测到上传文件",
      });
    }

    const file = req.file;

    // 读取文件内容
    const fileContent = fs.readFileSync(file.path);

    // 准备存储到MongoDB的数据
    const fileData = {
      filename: file.originalname,
      mimeType: file.mimetype, // 确保和模型字段名一致
      size: file.size,
      data: fileContent, // 实际文件内容作为Buffer存储
    };

    // 添加元数据（如果存在）
    if (req.body && req.body.metadata) {
      try {
        // 尝试解析metadata，如果它是字符串形式的JSON
        const metadataStr = req.body.metadata;
        fileData.metadata =
          typeof metadataStr === "string"
            ? JSON.parse(metadataStr)
            : metadataStr;
      } catch (e) {
        fileData.metadata = req.body.metadata;
      }
    } else {
      fileData.metadata = {};
    }

    const savedFile = await File.saveFile(fileData);

    // 删除临时文件
    fs.unlinkSync(file.path);

    return res.status(201).json({
      message: "附件上传成功",
      fileId: savedFile.fileId,
      filename: savedFile.filename,
    });
  } catch (error) {
    return res.status(500).json({
      code: 500,
      message: "附件上传失败",
      error: error.message || "未知错误",
    });
  }
};

module.exports = {
  getFileData,
  deleteFile,
  uploadFile,
};
