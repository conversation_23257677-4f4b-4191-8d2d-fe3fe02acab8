<!-- 用于显示溢出的文本内容，鼠标悬浮时显示完整内容
 用法
 <overflow-tooltip :content="content" :maxWidth="maxWidth" />
 参数
 content: [String, Number, Boolean]，必填，显示的内容
 maxWidth: String，可选，最大宽度，默认100% -->

<template>
  <div
    id="test"
    ref="contentRef"
    class="overflow-tooltip"
    :title="title"
    :style="{ maxWidth: props.maxWidth }"
  >
    {{ content }}
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch, onUnmounted } from "vue";

const props = defineProps({
  content: {
    type: [String, Number, Boolean],
    required: true,
    default: "",
  },
  maxWidth: {
    type: String,
    default: "100%",
  },
});

const title = ref<any>(null);
const contentRef = ref<any>(null);
const observer = new MutationObserver(setTooltip);

onMounted(() => {
  // 监听div的改变
  setTooltip();
  observer.observe(contentRef.value, { childList: true });
});

onUnmounted(() => {
  observer.disconnect();
});

// div发生改变则设置title
function setTooltip() {
  const el = contentRef.value;
  // 获取元素的padding
  const elComputed = window.getComputedStyle(el, "");
  const padding =
    parseInt(elComputed.paddingLeft.replace("px", "")) +
    parseInt(elComputed.paddingRight.replace("px", ""));
  const range = document.createRange();
  range.setStart(el, 0);
  range.setEnd(el, el.childNodes.length);
  setTimeout(() => {
    const rangeWidth = range.getBoundingClientRect().width;
    // 文字超长则设置title
    if (
      rangeWidth + padding > el.offsetWidth ||
      el.scrollWidth > el.offsetWidth
    ) {
      title.value = props.content;
    } else title.value = "";
  });
}
</script>

<style lang="less" scoped>
.overflow-tooltip {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 1.2;
  vertical-align: middle;
}
</style>
