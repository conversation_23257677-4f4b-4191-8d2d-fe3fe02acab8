<template>
  <div class="common-table-root" :class="customClass">
    <div class="common-table-header">
      <div>
        <slot name="header"></slot>
      </div>
      <div v-if="tableId" class="setting" style="align-self: flex-end">
        <el-popover
          placement="bottom"
          :width="240"
          trigger="click"
          :visible="columnConfigVisible"
          :append-to-body="false"
          popper-class="common-table-setting-popper"
        >
          <template #reference>
            <div
              class="table-setting-btn"
              @click="columnConfigVisible = !columnConfigVisible"
            >
              <span class="ri-settings-4-line setting-icon"></span>
              <span class="table-setting-text">字段显示设置</span>
            </div>
          </template>
          <template #default>
            <div class="popover-ref">
              <div class="table-setting-placeholder">
                请选择您需要显示的列字段
              </div>
              <div class="common-table-setting-check-wrapper">
                <el-checkbox
                  :model-value="isCheckAll"
                  :indeterminate="isIndeterminate"
                  @change="handleCheckAllChange"
                >
                  全选
                </el-checkbox>
                <DndList v-model:colSetting="customTableColumns" />
              </div>
              <!-- <div style="padding: 12px">
                <el-button @click="cancelTableSetting()">取消</el-button>
                <el-button type="primary" @click="saveTableSetting()">保存</el-button>
                <el-button type="primary" plain @click="resetTableSetting()">重置</el-button>
              </div> -->
            </div>
          </template>
        </el-popover>
      </div>
    </div>
    <el-table
      v-loading="tableData.loading"
      :data="tableData.rows"
      :border="props.border"
      header-cell-class-name="common-table"
      :header-cell-style="{
        background: 'rgb(248,249,252)',
        color: '#030814',
        fontSize: '16px',
      }"
      :size="props.size"
      @selection-change="handleSelect"
      @row-click="handleRowClick"
    >
      <!-- 表格左侧选择栏 -->
      <el-table-column
        v-if="props.showSelected"
        type="selection"
        width="55"
      ></el-table-column>
      <!-- 表格左侧选择栏 -->
      <el-table-column
        v-if="props.showIndex"
        type="index"
        width="75"
        fixed="left"
        label="序号"
      ></el-table-column>

      <template v-for="item in customTableColumns" :key="item.prop">
        <el-table-column
          v-if="item.prop && item.checked"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          :fixed="item.fixed"
          :sortable="item.sortable"
          :min-width="item.minWidth"
          :show-overflow-tooltip="true"
          :render-header="item?.renderHeader"
        >
          <template #default="{ row }">
            <slot :name="item.prop" :row="row">
              <span style="white-space: nowrap">{{ row[item.prop] }}</span>
            </slot>
          </template>
        </el-table-column>
      </template>
      <template #empty>
        <el-empty>
          <template #image>
            <img src="@/assets/png/empty.png" alt="notData" />
          </template>
        </el-empty>
      </template>
    </el-table>

    <el-pagination
      v-if="props.pagination"
      :current-page="pageConfig.currentPage"
      :page-size="pageConfig.pageSize"
      :total="tableData.total"
      :size="props.size"
      background
      :page-sizes="[10, 25, 50, 100]"
      layout="sizes, prev, pager, next, jumper, total"
      @size-change="handleSizeChange"
      @current-change="handlePageChange"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, toRaw, watch, ref, onMounted, computed } from "vue";
import { toastError } from "@/utils";
import type { TableColumnItem, TableColSettingItem } from "@/types";
import { cloneDeep } from "lodash/fp";
import DndList from "./DndList.vue";

/*-------------------------传入的props和table整体状态---------------------*/
interface propsType {
  columns: Array<TableColumnItem>;
  requestApi: (params: any) => any;
  // 可能获取数据前有别的函数需要先调用，这是获取数据的函数可以写入dataCallback中，用异步返回数据，requestApi里面调用它的先前函数
  dataCallback:
    | ((data: Record<string, any>) => {
        tableRows: Array<Record<string, any>>;
        total: number;
      })
    | ((
        data: Record<string, any>
      ) => Promise<{ tableRows: Array<Record<string, any>>; total: number }>);
  pagination?: boolean;
  border?: boolean;
  showSelected?: boolean;
  showIndex?: boolean;
  customClass?: string;
  requestParams?: Record<string, any>;
  initialPageSize?: number; // 初始每页条数
  canRefresh?: boolean;
  tableId?: string; // 用于标识表格自定义列配置,只有表格列数在10以上时才需要传
  size?: "default" | "" | "small" | "large";
}
const props = withDefaults(defineProps<propsType>(), {
  border: false,

  pagination: true,
  showSelected: false,
  showIndex: false,
  customClass: "",
  requestParams: () => ({}),
  initialPageSize: 25,
  canRefresh: true,
  tableId: "",
  size: "",
});

/* ======================== 表格自定义配置 ======================== */

let initColumnsConfig = toRaw(props).columns.map((item) => ({
  ...item,
  id: item.prop || item.type || "",
  checked: true,
}));

const customTableColumns = ref<Array<TableColSettingItem>>(initColumnsConfig);
const columnConfigVisible = ref(false);

// 是否勾选全部
const isCheckAll = computed(() =>
  customTableColumns.value.every((item) => item.checked)
);
// 是否部分勾选
const isIndeterminate = computed(
  () =>
    customTableColumns.value.some((item) => item.checked) && !isCheckAll.value
);

// 取消配置
function cancelTableSetting() {
  columnConfigVisible.value = false;
  customTableColumns.value = cloneDeep(initColumnsConfig);
}

// 保存配置
function saveTableSetting() {
  columnConfigVisible.value = false;
  initColumnsConfig = cloneDeep(customTableColumns.value);
}

watch(
  () => customTableColumns.value,
  (val) => {
    if (columnConfigVisible.value) {
      setTimeout(() => {
        columnConfigVisible.value = false;
      }, 2000);
    }
  },
  {
    deep: true,
  }
);

// 重置表格列配置
function resetTableSetting() {
  const resetColumnsConfig = toRaw(props).columns.map((item) => ({
    ...item,
    id: item.prop || item.type || "",
    checked: true,
  }));
  customTableColumns.value = cloneDeep(resetColumnsConfig);
  initColumnsConfig = cloneDeep(resetColumnsConfig);
}

// 监听tableColumns改变，动态修改表格列
watch(
  () => props.columns,
  (val) => {
    if (!val) return;
    customTableColumns.value = val.map((item) => ({
      ...item,
      id: item.prop || item.type || "",
      checked: true,
    }));
  },
  { deep: true }
);

// 全选状态切换
function handleCheckAllChange() {
  if (isCheckAll.value) {
    customTableColumns.value.forEach((item) => {
      if (!item.must) item.checked = false;
    });
  } else {
    customTableColumns.value.forEach((item) => {
      item.checked = true;
    });
  }
}

// 加载时获取自定义表格配置
onMounted(() => {
  if (!props.tableId) return;
  tableData.configLoading = true;
  customTableColumns.value = cloneDeep(initColumnsConfig);
});

/* ==================== 分页配置 ====================== */

const pageConfig = reactive({
  currentPage: 1,
  pageSize: 0,
});

onMounted(() => {
  pageConfig.pageSize = props.initialPageSize;
});

function handleSizeChange(pageSize: number) {
  pageConfig.pageSize = pageSize;
  pageConfig.currentPage = 1;
}

function handlePageChange(page: number) {
  pageConfig.currentPage = page;
}

/* ==================== 表格操作事件 ====================== */

const emits = defineEmits(["rowClick", "update:canRefresh"]);
const selected = ref([]);

// 数据项选择
function handleSelect(result: Array<never>) {
  selected.value = result;
}

// 数据项点击
function handleRowClick(row: Record<string, any>) {
  emits("rowClick", row);
}

/* ==================== 数据获取和刷新 ====================== */

interface tableDatatype {
  loading: boolean;
  rows: Array<Record<string, any>>;
  total: number;
  configLoading: boolean;
}
const tableData: tableDatatype = reactive({
  loading: false,
  rows: [],
  total: 30,
  configLoading: false,
});

defineExpose({
  refreshTableData,
  getTableData: () => {
    return {
      rows: tableData.rows,
      total: tableData.total,
    };
  },
  changeTableData(tableRows, total) {
    tableData.rows = tableRows;
    tableData.total = total;
  },
  selected: selected,
  tableData: tableData,
  customTableColumns: customTableColumns,
});

// 请求参数变化后重置页码
watch(
  () => props.requestParams,
  () => (pageConfig.currentPage = 1),
  { immediate: true, deep: true }
);

watch(
  [() => pageConfig, () => props.requestParams, () => props.canRefresh],
  () => refreshTableData(),
  {
    immediate: true,
    deep: true,
  }
);

async function refreshTableData() {
  if (!props.canRefresh) return;
  let params = { ...props.requestParams };
  tableData.loading = true;

  if (props.pagination) {
    params.offset = pageConfig.pageSize * (pageConfig.currentPage - 1);
    params.limit = params.limit || pageConfig.pageSize;
  }

  if (params.limit <= 0) return;

  await props
    .requestApi(params)
    .then(async (res: any) => {
      if (res?.totalElements === 0) {
        tableData.rows = [];
        tableData.total = 0;
        tableData.loading = false;
        return;
      }
      const { tableRows, total } = await props.dataCallback(res);
      tableData.rows = tableRows;
      tableData.total = total;
      tableData.loading = false;
    })
    .catch((err) => {
      toastError(err, "获取数据失败");
      tableData.rows = [];
      tableData.total = 0;
    })
    .finally(() => {
      tableData.loading = false;
    });
}
</script>

<style lang="less" scoped>
.common-table-root {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  .common-table-header {
    display: flex;
    justify-content: space-between;
    color: #666;
    margin-bottom: 10px;

    .table-setting-text {
      margin-left: 4px;
      font-size: var(--custom-large-font-size);
    }
  }
  // :deep(.el-table__header) {
  //   background-color: var(--el-table-header-bg);
  //   color: var(--el-table-header-color);
  // }
  :deep(.el-table) {
    flex: 1;
    min-height: 300px;
    .el-table__cell {
      font-size: var(--custom-default-font-size);
    }
    .common-table {
      flex: 1;
      margin-top: 8px;

      :deep(.el-scrollbar__view) {
        height: 100%;
      }

      :deep(.el-empty__description) {
        margin-top: 0;
      }
    }

    .el-scrollbar__view {
      height: 100%;
    }

    .el-empty__description {
      margin-top: 0;
    }
  }

  :deep(.el-pagination) {
    margin-top: 20px;
    align-self: flex-end;
  }
}
</style>

<style lang="less">
// 表格设置popover样式
.common-table-setting-popper {
  padding: 0px !important;
  .table-setting-placeholder {
    padding: 10px;
    font-size: var(--custom-small-font-size);
    color: rgb(3 8 20 / 65.5%);
    font-style: normal;
    border-bottom: 1px solid rgb(225 226 230);
  }

  .common-table-setting-check-wrapper {
    max-height: 240px;
    overflow-y: auto;
    padding: 0px 10px;
  }
}
</style>
