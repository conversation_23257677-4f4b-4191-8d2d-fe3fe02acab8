<template>
  <div class="staff-root">
    <div class="staff-header">
      <div class="staff-header-title">
        <span>人员管理</span>
      </div>
      <div class="staff-header-action">
        <el-button
          v-if="userStore.userType === 'MAINTENANCE_UNIT'"
          type="primary"
          :icon="Plus"
          @click="$router.push('/maintenance-enterprise/staff-detail')"
        >
          新建人员
        </el-button>
      </div>
    </div>
    <div class="staff-container">
      <div class="staff-search">
        <SearchContainer
          @queryBtnClick="handleSearch"
          @resetBtnClick="handleReset"
        >
          <el-form-item label="人员姓名">
            <el-input
              v-model.trim="searchForm.username"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item
            v-if="userStore.userType === UserTypeEnum.PLATFORM"
            label="所属企业"
          >
            <el-input
              v-model.trim="searchForm.unitName"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="全部"
              style="width: 100%"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="item in userStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </SearchContainer>
      </div>
      <div class="staff-content">
        <CommonTable
          :columns="staffColumns"
          :requestApi="getMaintenanceEnterpriseStaffList"
          :requestParams="requestParams"
          :dataCallback="dataCallback"
        >
          <template #roles="{ row }">
            <span>{{ row.roles.join(",") }}</span>
          </template>
          <template #unit="{ row }">{{ row.unitInfo?.companyName }}</template>

          <template #status="{ row }">
            <el-tag :type="row.status.type" effect="light">
              {{ row.status.label }}
            </el-tag>
          </template>
          <template #createdAt="{ row }">
            {{ formatDatetime(row.createdAt) }}
          </template>
          <template #operations="{ row }">
            <el-button link type="primary" @click="handleView(row)">
              查看
            </el-button>
            <el-button
              v-if="row.status === '待审核'"
              link
              type="primary"
              @click="handleAudit(row)"
            >
              审核
            </el-button>
          </template>
        </CommonTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SearchContainer, CommonTable } from "@/base-components";
import { reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { staffColumns } from "./config";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/store";
import { getMaintenanceEnterpriseStaffList } from "@/api";
import { userStatusOptions, UserTypeEnum } from "@/configs";
import { convertUserStatus, formatDatetime } from "@/utils";
import { useRouter, useRoute } from "vue-router";

const userStore = useUserStore();
const router = useRouter();
const route = useRoute();

const searchForm = reactive({
  username: "",
  unitName: route.query.unitName || "",
  status: "",
});

const requestParams = reactive({
  unitId: userStore.unit,
  filters: "",
  unitName: route.query.unitName || "",
});

function dataCallback(res: any) {
  const data = res.data?.data?.rows || [];
  data.forEach((item: any) => {
    item.status = convertUserStatus(item.status);
  });
  return {
    tableRows: data,
    total: res.data?.data?.pageElements?.totalElements || 0,
  };
}

// 查看详情
const handleView = (row: any) => {
  router.push(`/maintenance-enterprise/staff-detail?userId=${row.userId}`);
};

// 审核
const handleAudit = (row: any) => {
  ElMessage.info(`审核人员:${row.name} 的申请（功能开发中）`);
};

function handleSearch() {
  const filters = [] as string[];
  for (let key in searchForm) {
    if (searchForm[key] && key !== "unitName") {
      filters.push(`${key}=${searchForm[key]}`);
    }
  }
  requestParams.filters = filters.join(",");
  requestParams.unitName = searchForm.unitName;
}

function handleReset() {
  searchForm.username = "";
  searchForm.unitName = "";
  searchForm.status = "";
  requestParams.filters = "";
  requestParams.unitName = "";
}
</script>

<style lang="less" scoped>
.staff-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .staff-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;
  }

  .staff-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .staff-search {
      flex-shrink: 0;
      background: #f5f5f5;
      border-radius: 10px;
      padding: 0px 20px;
      box-sizing: border-box;
    }

    .staff-content {
      margin-top: 10px;
      flex: 1;
      min-height: 0px;
    }
  }
}
</style>
