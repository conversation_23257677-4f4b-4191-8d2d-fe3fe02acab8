// 维修企业类型定义
export interface MaintenanceEnterprise {
  id: string;
  companyName: string;
  legalPerson: string;
  legalPersonPhone?: string;
  contactPersonId: string;
  contactPerson: string;
  contactPhone: string;
  companyAddress: string;
  operationArea: string[];
  operationAreaLabel: string;
  status: "ENABLED" | "DISABLED";
  qualificationFiles: Array<{
    fileName: string;
    fileId: string;
    fileType: string;
  }>;
  settlements: Array<{
    contractId: string;
    settlementType: "IMMEDIATELY" | "YEARLY" | "YEARLY_WITHOUT_PARTS";
    contractPeriod: number[];
    status: "ENABLED" | "DISABLED";
  }>;
}

// 维修人员类型定义
export interface MaintenanceUser {
  userId: string;
  username: string;
  userType: string;
  gender?: string;
  phone: string;
  roles: string[];
  status: "ENABLED" | "WAITING_AUDIT" | "AUDIT_FAILED" | "DISABLED";
  lastLoginTime?: number;
  unit: string;
  areas?: string[];
  workTypes?: string[];
  qualifications?: Array<{
    fileName: string;
    fileId: string;
    fileType: string;
  }>;
  rejectReason?: string;
  auditHistory?: Array<{
    status: string;
    auditTime: number;
    auditUser: string;
    auditUserName?: string;
  }>;
  unitInfo?: {
    id: string;
    companyName: string;
    contactPerson: string;
    contactPhone: string;
    companyAddress: string;
    status: string;
  };
}
