<template>
  <div class="staff-root">
    <div class="staff-header">
      <div class="staff-header-title">
        <span>报修人员管理</span>
      </div>
      <div class="staff-header-action">
        <el-button
          v-if="userStore.userType === UserTypeEnum.REPAIR_UNIT"
          type="primary"
          :icon="Plus"
          @click="addStaffDialogRef.openDialog('create')"
        >
          添加人员
        </el-button>
        <el-button
          v-if="userStore.userType === UserTypeEnum.REPAIR_UNIT"
          type="primary"
        >
          邀请人员
        </el-button>
      </div>
    </div>
    <div class="staff-container">
      <div class="staff-search">
        <SearchContainer
          @queryBtnClick="handleSearch"
          @resetBtnClick="handleReset"
        >
          <el-form-item label="人员姓名">
            <el-input
              v-model.trim="searchForm.username"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item
            v-if="userStore.userType === UserTypeEnum.PLATFORM"
            label="所属单位"
          >
            <el-input
              v-model.trim="searchForm.unitName"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请输入"
              style="width: 100%"
              clearable
            >
              <el-option label="全部" value="" />
              <el-option label="正常" :value="UserStatusEnum.ENABLED" />
              <el-option label="已禁用" :value="UserStatusEnum.DISABLED" />
            </el-select>
          </el-form-item>
        </SearchContainer>
      </div>
      <div class="staff-content">
        <CommonTable
          ref="tableRef"
          :columns="staffColumns"
          :requestApi="getCustomerUnitStaffList"
          :requestParams="requestParams"
          :dataCallback="dataCallback"
        >
          <template #roles="{ row }">
            <div class="role-tags">
              {{ row.roles.join(",") }}
            </div>
          </template>
          <template #status="{ row }">
            <el-tag :type="row.status.type" effect="light">
              {{ row.status.label }}
            </el-tag>
          </template>
          <template #unit="{ row }">
            {{ row.unitInfo?.companyName || "--" }}
          </template>
          <template #createdAt="{ row }">
            {{ formatDatetime(row.createdAt) }}
          </template>
          <template #operations="{ row }">
            <el-button link type="primary" @click="handleView(row)">
              查看
            </el-button>
            <template v-if="userStore.userType === UserTypeEnum.REPAIR_UNIT">
              <el-button
                v-if="row.status.value === UserStatusEnum.DISABLED"
                link
                type="primary"
                @click="handleChangeStatus(row, UserStatusEnum.ENABLED)"
              >
                启用
              </el-button>
              <el-button
                v-else
                link
                type="primary"
                @click="handleChangeStatus(row, UserStatusEnum.DISABLED)"
              >
                禁用
              </el-button>
            </template>
          </template>
        </CommonTable>
      </div>
    </div>

    <AddStaffDialog
      ref="addStaffDialogRef"
      @submit="tableRef?.refreshTableData()"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { SearchContainer, CommonTable } from "@/base-components";
import { reactive } from "vue";
import { staffColumns } from "./config";
import { Plus } from "@element-plus/icons-vue";
import { useUserStore } from "@/store";
import { UserTypeEnum, UserStatusEnum } from "@/configs";
import AddStaffDialog from "./component/AddStaffDialog.vue";
import { getCustomerUnitStaffList, updateCustomerUnitStaffStatus } from "@/api";
import { convertUserStatus, formatDatetime } from "@/utils";
import { useRoute } from "vue-router";
import { Message, SystemPrompt, toastError } from "../../utils";

const userStore = useUserStore();
const route = useRoute();
const tableRef = ref();
const addStaffDialogRef = ref();

const searchForm = reactive({
  username: "",
  unitName: route.query.unitName || "",
  status: "",
});

const requestParams = reactive({
  unitId: userStore.unit,
  filters: "",
  unitName: route.query.unitName || "",
});

// 搜索
function handleSearch() {
  const filters = [] as string[];
  for (let key in searchForm) {
    if (searchForm[key] && key !== "unitName") {
      filters.push(`${key}=${searchForm[key]}`);
    }
  }
  requestParams.filters = filters.join(",");
  requestParams.unitName = searchForm.unitName;
}

// 重置
function handleReset() {
  searchForm.username = "";
  searchForm.unitName = "";
  searchForm.status = "";
  requestParams.filters = "";
  requestParams.unitName = "";
}

// 数据回调
function dataCallback(res: any) {
  const data = res.data.data;
  data.rows.forEach((item: any) => {
    item.status = convertUserStatus(item.status);
  });
  return {
    tableRows: data.rows || [],
    total: data.pageElements.total || 0,
  };
}

// 查看详情
const handleView = (row: any) => {
  if (userStore.userType === UserTypeEnum.REPAIR_UNIT) {
    addStaffDialogRef.value.openDialog("edit", row);
  } else {
    addStaffDialogRef.value.openDialog("view", row);
  }
};

// 修改状态
const handleChangeStatus = (row: any, status: string) => {
  SystemPrompt(
    `确定${status === UserStatusEnum.ENABLED ? "启用" : "禁用"}该人员吗？`
  ).then((res) => {
    updateCustomerUnitStaffStatus({ userId: row.userId, status })
      .then((res) => {
        Message.success(
          `${status === UserStatusEnum.ENABLED ? "启用" : "禁用"}成功`
        );
        tableRef.value.refreshTableData();
      })
      .catch((err) => {
        toastError(
          err,
          `${status === UserStatusEnum.ENABLED ? "启用" : "禁用"}失败`
        );
      });
  });
};
</script>

<style lang="less" scoped>
.staff-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .staff-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;
    min-height: 53px;
    box-sizing: border-box;
  }

  .staff-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .staff-search {
      flex-shrink: 0;
      background: #f5f5f5;
      border-radius: 10px;
      padding: 0px 20px;
      box-sizing: border-box;
    }

    .staff-content {
      margin-top: 10px;
      flex: 1;
      min-height: 0px;
    }
  }
}

.role-tags {
  white-space: normal;
  word-break: break-all;
  line-height: 1.5;
}
</style>
