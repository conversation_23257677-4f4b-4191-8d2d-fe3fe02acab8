// blob转base64
export function blobToBase64(blob: Blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(blob)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}

// url图片转base64
export function getUrlBase64(url) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.src = url
    img.onload = function () {
      const canvas = document.createElement("canvas")
      canvas.width = img.width
      canvas.height = img.height
      const ctx = canvas.getContext("2d")
      ctx?.drawImage(img, 0, 0, img.width, img.height)
      resolve(canvas.toDataURL())
    }
  })
}

// 选取文件函数
export const selectFile = (function () {
  const input = document.createElement("input")
  input.type = "file"
  input.style.opacity = "0"

  return function (accept = "", size) {
    const accepts = accept
      .split(",")
      .map(type => type.trim())
      .filter(type => type)
    input.accept = accepts.join(",")

    const acceptTests = accepts.map(type => {
      if (/^\./.test(type)) {
        //为后缀
        return {
          target: "name", //检查名称
          regExp: new RegExp(`${type.replace(".", "\\.")}$`, "i")
        }
      } else {
        //为MIME类型
        if (/\/\*$/.test(type)) {
          //泛匹配
          return {
            target: "type", //检查名称
            regExp: new RegExp(`^${type.replace("*", "\\S+")}$`, "i")
          }
        } else {
          return {
            target: "type", //检查名称
            regExp: new RegExp(`^${type}$`, "i")
          }
        }
      }
    })

    return new Promise((resolve, reject) => {
      input.onchange = e => {
        if (!input.files) return
        const file = input.files[0]
        if (!file) return

        if (size && file.size > size) {
          reject(new TypeError("ERROR_FILE_SIZE"))
          return
        }

        let result = true
        if (acceptTests.length > 0) {
          result = acceptTests.some(test => {
            return test.regExp.test(file[test.target])
          })
        }

        if (result) {
          resolve(file)
        } else {
          reject(new TypeError("ERROR_FILE_TYPE"))
        }

        input.value = ""
      }

      //兼容IE Input不在DOM树上无法触发选择的问题
      document.body.appendChild(input)
      input.click()
      document.body.removeChild(input)
    })
  }
})()

interface DownloadFileParams {
  fileData: any
  fileType: string
  fileName: string
}

// 下载文件
export function downloadFile({ fileData, fileType, fileName }: DownloadFileParams) {
  const blob = new Blob([fileData], { type: fileType })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement("a")
  link.style.display = "none"
  link.href = url
  link.setAttribute("download", fileName)
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export function getFileSize(size) {
  if (size < 1024 * 1024) return (size / 1024).toFixed(2) + "KB"
  else if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(2) + "MB"
  else if (size < 1024 * 1024 * 1024 * 1024) return (size / (1024 * 1024 * 1024)).toFixed(2) + "GB"
}

export function blobToFile(blob, fileName, fileType = "") {
  return new File([blob], fileName, { lastModified: new Date().getTime(), type: fileType || blob.type })
}
