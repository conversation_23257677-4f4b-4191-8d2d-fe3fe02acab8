import { onReady } from "@dcloudio/uni-app"
import { onUnload } from "@dcloudio/uni-app"
import { onUnmounted } from "vue"

export const useDisableGesture = (diff: number) => {
  // #ifdef H5
  onReady(() => {
    console.log("disable gesture")
    document.body.addEventListener("touchstart", recordCoord)
    document.body.addEventListener("touchmove", forbiddenBrowserGesture, { passive: false })
  })

  onUnload(() => {
    document.body.removeEventListener("touchstart", recordCoord)
    document.body.removeEventListener("touchmove", forbiddenBrowserGesture)
  })

  onUnmounted(() => {
    document.body.removeEventListener("touchstart", recordCoord)
    document.body.removeEventListener("touchmove", forbiddenBrowserGesture)
  })

  let startX = 0

  const recordCoord = (e: TouchEvent) => {
    startX = e.touches[0].clientX
  }

  const forbiddenBrowserGesture = (e: TouchEvent) => {
    console.log("test")
    if (e.touches[0].clientX - startX > diff) {
      e.preventDefault()
      e.stopPropagation()
    }
  }
  // #endif
}
