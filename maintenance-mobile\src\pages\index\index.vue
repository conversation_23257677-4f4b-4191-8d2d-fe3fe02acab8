<template>
  <view class="content">
    <image class="logo" src="/static/logo.png" mode="aspectFit" />
    <view class="text-area">
      <text class="title">{{ title }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"

const title = ref("即时修维修系统")

onMounted(() => {
  // 自动跳转到登录页
  setTimeout(() => {
    uni.reLaunch({
      url: "/pages/login/login"
    })
  }, 1500)
})
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #ffffff;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-top: 200rpx;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50rpx;
}

.text-area {
  display: flex;
  justify-content: center;
}

.title {
  font-size: 36rpx;
  color: #3c9cff;
  font-weight: bold;
}
</style>
