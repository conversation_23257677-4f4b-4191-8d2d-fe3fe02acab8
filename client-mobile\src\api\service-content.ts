import { request } from "@/utils/request"

// 获取服务类别
export function getServiceContentApi() {
  return request.get("/service-content/service-class")
}

// 获取所有服务项目
export function getServiceItemApi(params: any) {
  return request.get("/service-content/service-items", {
    params
  })
}

// 获取维保方指定合同的服务项目
export function getMaintenanceServiceItemApi(params: any) {
  return request.get("/service-content/maintenance-service-items", {
    params
  })
}

// 获取报修方当前生效的服务项目列表
export function getRepairServiceItemApi(params: any) {
  return request.get("/service-content/repair-service-items", {
    params
  })
}
