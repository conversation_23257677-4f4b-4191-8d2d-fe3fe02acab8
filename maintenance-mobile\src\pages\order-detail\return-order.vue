<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar title="退单" />

    <!-- 页面内容 -->
    <view class="page-content-with-nav return-order">
      <view class="form-section">
        <view class="form-item">
          <view class="label"><span class="required">*</span>退单原因</view>
          <view class="picker-container" @click="showReasonPicker">
            <view class="picker-value">{{ selectedReason || "请选择退单原因" }}</view>
            <view class="picker-arrow">
              <uni-icons type="right" size="16"></uni-icons>
            </view>
          </view>
        </view>

        <view class="form-item">
          <view class="label"><span class="required">*</span>备注</view>
          <textarea class="textarea" v-model="remark" placeholder="请输入备注信息" />
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="fixed-bottom">
        <button class="btn-confirm" @click="submitReturnOrder">确定</button>
      </view>
    </view>

    <!-- 退单原因选择器 -->
    <uni-popup ref="reasonPopup" type="bottom">
      <view class="picker-popup">
        <view class="picker-header">
          <view class="picker-title">退单原因</view>
          <view class="picker-close" @click="closeReasonPicker">
            <uni-icons type="close" size="20"></uni-icons>
          </view>
        </view>
        <view class="picker-content">
          <view class="picker-item" v-for="(item, index) in reasonOptions" :key="index" @click="selectReason(item)">
            {{ item }}
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import { NavBar } from "@/components"
import { onLoad } from "@dcloudio/uni-app"
import { returnOrder } from "@/api"
import { showToast, showLoading, hideLoading, navigateBack } from "@/utils"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

// 页面数据
const workOrderId = ref("")
const selectedReason = ref("")
const remark = ref("")
const reasonOptions = ref(["缺少配件，等待采购", "其他"])
const reasonPopup = ref<any>(null)

// 页面加载
onLoad(option => {
  if (option?.workOrderId) {
    workOrderId.value = option.workOrderId
  }
})

// 确保弹窗组件正确初始化
onMounted(() => {
  // 等待DOM更新后再获取refs
  setTimeout(() => {
    if (!reasonPopup.value) {
      console.warn("退单原因选择器未正确初始化")
    }
  }, 500)
})

// 显示退单原因选择器
const showReasonPicker = () => {
  reasonPopup.value?.open()
}

// 关闭退单原因选择器
const closeReasonPicker = () => {
  reasonPopup.value?.close()
}

// 选择退单原因
const selectReason = (reason: string) => {
  selectedReason.value = reason
  closeReasonPicker()
}

// 提交退单
const submitReturnOrder = async () => {
  if (!selectedReason.value) {
    return showToast("请选择退单原因")
  }
  if (!remark.value) {
    return showToast("请输入备注信息")
  }

  try {
    showLoading("退单中...", true)

    await returnOrder({
      workOrderId: workOrderId.value,
      maintenanceUnitId: userStore.unit,
      reason: selectedReason.value,
      remark: remark.value
    })

    hideLoading()
    showToast("退单成功")

    // 触发工单详情页刷新
    uni.$emit("refreshOrderDetail")
    uni.$emit("repairOrderListRefresh")

    // 返回上一页
    setTimeout(() => {
      navigateBack("", {
        delta: 2
      })
    }, 1500)
  } catch (error: any) {
    hideLoading()
    showToast(error.message || "退单失败")
  }
}
</script>

<style lang="scss" scoped>
.return-order {
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  height: 100%;

  .form-section {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }

  .form-item {
    margin-bottom: 30rpx;

    .label {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
      font-weight: 500;
      .required {
        color: #f56c6c;
        margin-right: 10rpx;
      }
    }

    .picker-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 80rpx;
      border: 1px solid #eee;
      border-radius: 8rpx;
      padding: 0 20rpx;

      .picker-value {
        font-size: 28rpx;
        color: #333;
      }

      .picker-arrow {
        color: #999;
      }
    }

    .textarea {
      width: 100%;
      height: 240rpx;
      border: 1px solid #eee;
      border-radius: 8rpx;
      padding: 20rpx;
      box-sizing: border-box;
      font-size: 28rpx;
    }
  }

  .fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);

    .btn-confirm {
      height: 90rpx;
      line-height: 90rpx;
      background-color: $uni-color-primary;
      color: #fff;
      border-radius: 45rpx;
      font-size: 32rpx;
      width: 100%;
    }
  }
}

.picker-popup {
  background-color: #fff;
  border-radius: 12rpx 12rpx 0 0;
  overflow: hidden;

  .picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    border-bottom: 1px solid #eee;

    .picker-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .picker-close {
      padding: 10rpx;
    }
  }

  .picker-content {
    max-height: 60vh;
    overflow-y: auto;

    .picker-item {
      padding: 30rpx 24rpx;
      font-size: 30rpx;
      color: #333;
      border-bottom: 1px solid #f5f5f5;

      &:active {
        background-color: #f9f9f9;
      }
    }
  }
}
</style>
