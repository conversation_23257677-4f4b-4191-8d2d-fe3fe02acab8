<template>
  <view class="page-container">
    <!-- 自定义导航栏 -->
    <NavBar
      title="选择服务类别与项目"
      :back-url="`/pages/order-detail/process-price?workOrderId=${workOrderId}&isModify=${isModify}`" />

    <!-- 页面内容 -->
    <view class="page-content-with-nav select-service">
      <!-- 服务类别与项目列表 -->
      <view class="service-container">
        <!-- 左侧服务类别列表 -->
        <scroll-view class="category-list" scroll-y>
          <view
            v-for="(category, index) in serviceCategories"
            :key="index"
            class="category-item"
            :class="{ active: currentCategoryIndex === index }"
            @click="selectCategory(index)">
            {{ category.serviceClass }}
          </view>
        </scroll-view>

        <!-- 右侧服务项目列表 -->
        <view class="service-list-container">
          <!-- 搜索框 -->
          <view class="search-box">
            <input class="search-input" v-model="searchKeyword" placeholder="搜索服务项目" @input="handleSearch" />
          </view>

          <scroll-view
            class="service-list"
            scroll-y
            @scrolltolower="loadMoreServiceItems"
            :refresher-triggered="refreshing"
            refresher-enabled
            @refresherrefresh="refreshServiceItems">
            <view
              v-for="(item, index) in serviceItems"
              :key="index"
              class="service-item"
              :class="{
                incompatible: !item.isCompatible
              }"
              @click="toggleSelectServiceItem(item)">
              <view class="service-item-content">
                <view class="checkbox">
                  <view class="checkbox-inner" :class="{ checked: isServiceItemSelected(item) }"></view>
                </view>
                <view class="service-info">
                  <view class="info-code">{{ item.code }}</view>
                  <view class="info-title">{{ item.serviceItem }}</view>
                  <view class="info-hint">结算方式：{{ getSettlementType(item.reportUnitSettlementType) }}</view>
                </view>
              </view>
            </view>

            <!-- 加载更多提示 -->
            <view class="loading-more" v-if="loading">
              <uni-icons type="spinner-cycle" size="20" color="#999"></uni-icons>
              <text>加载中...</text>
            </view>

            <!-- 无数据提示 -->
            <view class="empty-tip" v-if="serviceItems.length === 0 && !loading">
              <text>暂无服务项目</text>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="fixed-bottom">
        <view class="selected-count" v-if="selectedServiceItems.length > 0">
          已选择 {{ selectedServiceItems.length }} 项
        </view>
        <button class="next-btn" :disabled="selectedServiceItems.length === 0" @click="goToNextStep">下一步</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from "vue"
import { NavBar } from "@/components"
import { onLoad } from "@dcloudio/uni-app"
import { showToast, showLoading, hideLoading, getSettlementType } from "@/utils"
import { getServiceContentApi, getRepairServiceItemApi } from "@/api"
import { useWorkOrderDetailStore, type ServiceItemType } from "../../store/work-order-detail-store"
import { SettlementTypeEnum } from "@/configs"
const workOrderDetailStore = useWorkOrderDetailStore()

// 工单ID
const workOrderId = ref("")
const isModify = ref(false)

// 搜索关键词
const searchKeyword = ref("")

// 定义服务类别接口
interface ServiceCategory {
  id: string
  serviceClass: string
  [key: string]: any
}

// 服务类别列表
const serviceCategories = ref<ServiceCategory[]>([])

// 服务项目列表
const serviceItems = ref<ServiceItemType[]>([])

// 选中的服务项目
const selectedServiceItems = ref<ServiceItemType[]>([])

// 当前选中项目的结算方式
let currentSettlementType = ""

// 当前选中的类别索引
const currentCategoryIndex = ref(0)

// 当前页码
const currentPage = ref(1)

// 每页数量
const pageSize = ref(20)

// 是否有更多数据
const hasMore = ref(true)

// 是否正在加载
const loading = ref(false)

// 是否正在刷新
const refreshing = ref(false)

// 检查服务项目是否被选中
const isServiceItemSelected = (item: ServiceItemType): boolean => {
  return selectedServiceItems.value.some(selected => selected.serviceItemId === item.serviceItemId)
}

// 选择服务类别
const selectCategory = (index: number): void => {
  if (currentCategoryIndex.value === index) {
    return
  }

  currentCategoryIndex.value = index
  searchKeyword.value = "" // 清空搜索关键词
  currentPage.value = 1 // 重置页码
  hasMore.value = true // 重置是否有更多数据
  serviceItems.value = [] // 清空服务项目列表

  // 获取该类别下的服务项目
  getServiceItems()
}

// 处理搜索
const handleSearch = (): void => {
  // 如果有搜索关键词，不按类别过滤，直接从服务器获取搜索结果
  if (searchKeyword.value) {
    currentPage.value = 1 // 重置页码
    hasMore.value = true // 重置是否有更多数据
    serviceItems.value = [] // 清空服务项目列表
    getServiceItems()
  }
}

// 切换选择服务项目
const toggleSelectServiceItem = (item: ServiceItemType): void => {
  const index = selectedServiceItems.value.findIndex(selected => selected.serviceItemId === item.serviceItemId)

  if (index > -1) {
    // 如果已选中，则取消选中
    selectedServiceItems.value.splice(index, 1)

    // 如果没有选中的项目了，重置当前结算方式
    if (selectedServiceItems.value.length === 0) {
      currentSettlementType = ""
      // 刷新当前页面的服务项目，使所有项目可选
      refreshCurrentPageItems()
    } else {
      // 仍有选中项目，刷新当前页面的服务项目，更新可选状态
      refreshCurrentPageItems()
    }
  } else {
    // 如果未选中，检查结算方式是否一致
    if (
      selectedServiceItems.value.length > 0 &&
      currentSettlementType &&
      item.reportUnitSettlementType !== currentSettlementType
    ) {
      // 结算方式不一致，显示提示
      showToast(`结算方式与当前所选项目不一致，不可勾选。当前结算方式为：${getSettlementType(currentSettlementType)}`)
      return
    }

    // 如果是第一个选中的项目，设置当前结算方式
    if (selectedServiceItems.value.length === 0) {
      currentSettlementType = item.reportUnitSettlementType
    }

    // 选中项目
    selectedServiceItems.value.push({ ...item, reportUnitSettlementType: item.reportUnitSettlementType })

    // 刷新当前页面的服务项目，更新可选状态
    refreshCurrentPageItems()
  }
}

// 前往下一步
const goToNextStep = () => {
  if (selectedServiceItems.value.length === 0) {
    return showToast("请选择至少一个服务项目")
  }
  const tempItems = selectedServiceItems.value.map(item => {
    return {
      serviceClass: item.serviceClass,
      serviceItemId: item.serviceItemId,
      code: item.code,
      serviceItem: item.serviceItem,
      unit: item.unit,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      subtotal: item.unitPrice * item.quantity,
      reportUnitSettlementType: item.settlementType,
      reportSettlementId: item.settlementId
    }
  })

  // 将选中的服务项目直接存储在 store 中
  workOrderDetailStore.setSelectedServiceItems(tempItems)

  // 跳转到添加服务项目页面
  uni.navigateTo({
    url: `/pages/order-detail/add-service?workOrderId=${workOrderId.value}&isModify=${isModify.value}`
  })
}

// 获取服务类别列表
const getServiceCategories = async () => {
  try {
    showLoading("加载中...")
    const res = await getServiceContentApi()
    serviceCategories.value = res.data.data || []

    if (serviceCategories.value.length > 0) {
      // 获取第一个类别下的服务项目
      getServiceItems()
    }

    hideLoading()
  } catch (error) {
    hideLoading()
    showToast("获取服务类别失败")
    console.error("获取服务类别失败", error)
  }
}

// 获取服务项目列表
const getServiceItems = async () => {
  if (!hasMore.value || loading.value) {
    return
  }

  loading.value = true

  try {
    const params = {
      offset: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value,
      filters: `serviceClass=${serviceCategories.value[currentCategoryIndex.value]?.id}`,
      repairUnitId: workOrderDetailStore.reportUnitId
    }

    // 如果有搜索关键词，添加到过滤条件
    if (searchKeyword.value) {
      params.filters += `,serviceItem=${searchKeyword.value}`
    }

    const res = await getRepairServiceItemApi(params)
    const data = res.data.data || {}
    const rows = data.rows || []
    rows.forEach((item: any) => {
      item.reportUnitSettlementType = item.settlementType
    })
    const items = rows
    const totalElements = data.pageElements?.totalElements || 0

    // 处理获取到的服务项目，标记已选中的项目
    const processedItems = items.map((item: ServiceItemType) => {
      // 检查结算方式是否与当前选中的结算方式一致
      // 如果没有选中项目，则所有项目都可选
      const isCompatible =
        selectedServiceItems.value.length === 0 ||
        !currentSettlementType ||
        item.reportUnitSettlementType === currentSettlementType

      return {
        ...item,
        serviceItemId: item._id,
        // 添加一个属性，表示该项目是否可选（结算方式兼容）
        isCompatible
      }
    })

    if (currentPage.value === 1) {
      serviceItems.value = processedItems
    } else {
      serviceItems.value = [...serviceItems.value, ...processedItems]
    }

    refreshCurrentPageItems()

    // 判断是否有更多数据
    hasMore.value = serviceItems.value.length < totalElements

    // 更新页码
    if (hasMore.value) {
      currentPage.value++
    }
  } catch (error: any) {
    showToast(error.message || "获取服务项目失败")
    console.error("获取服务项目失败", error)
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 加载更多服务项目
const loadMoreServiceItems = () => {
  if (searchKeyword.value) {
    return // 搜索模式下不支持加载更多
  }

  getServiceItems()
}

// 刷新服务项目
const refreshServiceItems = () => {
  refreshing.value = true
  currentPage.value = 1
  hasMore.value = true
  getServiceItems()
}

// 刷新当前页面的服务项目，更新可选状态
const refreshCurrentPageItems = () => {
  // 更新当前页面的服务项目，标记哪些是可选的
  console.log(serviceItems.value, selectedServiceItems.value, currentSettlementType)
  serviceItems.value = serviceItems.value.map((item: ServiceItemType) => {
    // 检查结算方式是否与当前选中的结算方式一致
    // 如果没有选中项目，则所有项目都可选
    const isCompatible =
      selectedServiceItems.value.length === 0 ||
      !currentSettlementType ||
      item.reportUnitSettlementType === currentSettlementType

    return {
      ...item,
      isCompatible
    }
  })
}

// 监听搜索关键词变化
watch(searchKeyword, newVal => {
  if (!newVal) {
    // 如果搜索关键词被清空，恢复显示当前类别下的服务项目
    currentPage.value = 1
    hasMore.value = true
    serviceItems.value = []
    getServiceItems()
  }
})

// 初始化已选中的服务项目
const initSelectedServiceItems = () => {
  // 从 store 中获取已有的服务项目
  const storeServiceItems = workOrderDetailStore.serviceItems || []

  // 如果 store 中有服务项目，则初始化选中状态
  if (storeServiceItems.length > 0) {
    // 过滤出非临时选中的项目（已添加到工单的项目）
    const existingItems = storeServiceItems.filter(item => !item.isSelected)

    if (existingItems.length > 0) {
      // 检查所有项目的结算方式是否一致
      const firstSettlementType = existingItems[0].reportUnitSettlementType
      const allSameSettlementType = existingItems.every(item => item.reportUnitSettlementType === firstSettlementType)

      if (!allSameSettlementType) {
        // 如果结算方式不一致，显示提示并清空选中项目
        showToast("已选服务项目的结算方式不一致，已重置选择")
        return
      }

      // 设置当前结算方式为第一个项目的结算方式
      currentSettlementType = firstSettlementType || SettlementTypeEnum.IMMEDIATELY

      // 将已有项目添加到选中列表
      selectedServiceItems.value = existingItems.map(item => ({
        ...item,
        // 确保保留原有数量
        quantity: item.quantity
      }))
    }
  }
}

// 页面加载
onLoad(option => {
  if (option?.workOrderId) {
    workOrderId.value = option.workOrderId
  }
  if (option?.isModify) {
    isModify.value = option?.isModify
  }

  // 初始化已选中的服务项目
  initSelectedServiceItems()

  // 获取服务类别和项目数据
  getServiceCategories()
})
</script>

<style lang="scss" scoped>
.select-service {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; // 为底部按钮留出空间

  .service-container {
    display: flex;
    flex: 1;
    background-color: #fff;
    margin: 0;
    height: calc(100vh - 44px - 120rpx);
    overflow: hidden;

    .category-list {
      width: 30%;
      background-color: #f5f5f5;
      height: 100%;
      box-sizing: border-box;

      .category-item {
        padding: 30rpx 20rpx;
        font-size: 28rpx;
        color: #333;
        border-bottom: 1rpx solid #eee;
        text-align: center;
        word-break: break-all;
        display: flex;
        align-items: center;
        justify-content: center;

        &.active {
          background-color: #fff;
          color: $uni-color-primary;
          font-weight: bold;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6rpx;
            height: 36rpx;
            background-color: $uni-color-primary;
          }
        }
      }
    }

    .service-list-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      width: 70%;
      height: 100%;
      box-sizing: border-box;

      .search-box {
        padding: 20rpx;
        background-color: #fff;
        box-sizing: border-box;

        .search-input {
          height: 70rpx;
          background-color: #f5f5f5;
          border-radius: 35rpx;
          padding: 0 30rpx;
          font-size: 28rpx;
          box-sizing: border-box;
        }
      }

      .service-list {
        flex: 1;
        padding: 0 20rpx;
        height: calc(100% - 110rpx);
        box-sizing: border-box;

        .service-item {
          padding: 20rpx 0;
          border-bottom: 1rpx solid #eee;

          &.incompatible {
            opacity: 0.6;
          }

          .service-item-content {
            display: flex;
            align-items: center;

            .checkbox {
              width: 40rpx;
              height: 40rpx;
              border: 1rpx solid #ddd;
              border-radius: 50%;
              margin-right: 20rpx;
              margin-top: 4rpx;
              display: flex;
              justify-content: center;
              align-items: center;
              flex-shrink: 0;

              .checkbox-inner {
                width: 24rpx;
                height: 24rpx;
                border-radius: 50%;

                &.checked {
                  background-color: $uni-color-primary;
                }
              }
            }

            .service-info {
              flex: 1;
              overflow: hidden;
              word-break: break-all;
              display: flex;
              flex-direction: column;
              line-height: 1.4;
              gap: 6rpx;
              .info-code {
                font-size: 28rpx;
                color: #333;
                font-weight: bold;
              }
              .info-title {
                font-size: 28rpx;
                color: #333;
              }
              .info-hint {
                font-size: 24rpx;
                color: #999;
              }
              .incompatible-hint {
                font-size: 24rpx;
                color: #ff4d4f;
                margin-top: 4rpx;
              }
            }
          }
        }

        .loading-more {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 20rpx 0;
          color: #999;
          font-size: 24rpx;

          uni-icons {
            margin-right: 10rpx;
            animation: loading-rotate 1s linear infinite;
          }
        }

        .empty-tip {
          text-align: center;
          padding: 40rpx 0;
          color: #999;
          font-size: 28rpx;
        }
      }
    }
  }

  .tips-box {
    background-color: #fff9c4;
    padding: 20rpx;
    margin: 0 20rpx 20rpx;
    border-radius: 8rpx;

    .tips-title {
      display: flex;
      font-size: 26rpx;
      color: #ff9800;
      margin-bottom: 10rpx;

      .tips-icon {
        margin-right: 10rpx;
      }
    }
  }

  .fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    z-index: 100;

    .selected-count {
      font-size: 28rpx;
      color: #666;
      flex: 1;
    }

    .next-btn {
      width: 240rpx;
      height: 80rpx;
      line-height: 80rpx;
      background-color: $uni-color-primary;
      color: #fff;
      font-size: 30rpx;
      border-radius: 40rpx;
      text-align: center;

      &[disabled] {
        background-color: #ccc;
      }
    }
  }
}

@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
