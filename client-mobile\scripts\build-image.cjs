const fs = require("fs")
const { resolve } = require("path")
const chalk = require("chalk")
const shell = require("shelljs")
const moment = require("moment")

const versionTag = process.argv.slice(2)[0] || moment().format("YYYYMMDD-HHmm")

// 构建
const buildStart = Date.now()
console.log(chalk.blue("===开始构建==="))
shell.exec(`uni build`)
console.log(chalk.green("构建成功，耗时", (Date.now() - buildStart) / 1000, "秒"))

// 注入版本时间戳打印
console.log(chalk.yellow("注入版本时间戳..."))
const data = fs.readFileSync(resolve(__dirname, "../dist/build/h5/index.html"), { encoding: "utf-8" })
fs.writeFileSync(
  resolve(__dirname, "../dist/build/h5/index.html"),
  data.replace("</body>", `<script>console.log('version_${versionTag}:${new Date()}')</script></body>`)
)

const remoteHub = "registry.cn-chengdu.aliyuncs.com/signit-fe"
const imageName = `jishixiu-client-mobile:${versionTag}`

// 构建并发布docker镜像
const buildImageStart = Date.now()
console.log(chalk.blue("===开始生成镜像==="))
shell.exec(`docker build -t ${imageName} .`)
console.log(chalk.blue("===镜像构建完成，开始发布==="))
shell.exec(`docker tag ${imageName} ${remoteHub}/${imageName}`)
shell.exec(`docker push ${remoteHub}/${imageName}`)
shell.exec(`docker rmi ${imageName}`)
console.log(chalk.blue("===镜像发布完成==="))
console.log(chalk.green("镜像发布成功，耗时", (Date.now() - buildImageStart) / 1000, "秒"))
