const { WorkOrder } = require("../models/WorkOrder");
const { RepairEnterprise } = require("../models/RepairEnterprise");
const { RepairUser } = require("../models/RepairUser");
const { MaintenanceEnterprise } = require("../models/MaintenanceEnterprise");
const { MaintenanceUser } = require("../models/MaintenanceUser");
const { ServiceClass } = require("../models/ServiceClass");
const { maskPhoneNumber } = require("./mask-show");

function generateUniqueRandomCode(codeLength) {
  const characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let code = "";

  while (code.length < codeLength) {
    const randomIndex = Math.floor(Math.random() * characters.length);

    code += characters[randomIndex];
  }

  return code;
}

async function getWorkOrderListUtil(params) {
  try {
    const {
      filtersObj,
      offset,
      limit,
      reporter,
      repair,
      commonStatus,
      showMask,
    } = params;
    if (commonStatus && !filtersObj.status) {
      filtersObj.status = { $in: commonStatus.split(",") };
    }

    if (reporter) {
      // 获取报修单位
      const reporterUnit = await RepairEnterprise.find({
        companyName: { $regex: reporter, $options: "i" },
      });
      if (reporterUnit.length > 0)
        filtersObj.reportUnitId = reporterUnit.map((item) => item.id);
      // 获取报修人
      const reporterUser = await RepairUser.find({
        username: { $regex: reporter, $options: "i" },
      });
      if (reporterUser.length > 0)
        filtersObj.reporterId = reporterUser.map((item) => item.userId);
    }
    if (repair) {
      // 获取维修单位
      const repairUnit = await MaintenanceEnterprise.find({
        companyName: { $regex: repair, $options: "i" },
      });
      if (repairUnit.length > 0)
        filtersObj.maintenanceUnitId = repairUnit.map((item) => item.id);
      // 获取维修人
      const repairUser = await MaintenanceUser.find({
        username: { $regex: repair, $options: "i" },
      });
      if (repairUser.length > 0)
        filtersObj.maintenanceUserId = repairUser.map((item) => item.userId);
    }

    // 获取工单列表
    const workOrderList = await WorkOrder.find(filtersObj)
      .sort({ updatedAt: -1 })
      .skip(Number(offset))
      .limit(Number(limit));
    // 获取维护单位id
    const maintenanceEnterpriseId = workOrderList.map(
      (item) => item.maintenanceUnitId
    );
    const maintenanceEnterprise = await MaintenanceEnterprise.find({
      id: { $in: maintenanceEnterpriseId },
    }).select("-_id id companyName");

    // 获取报修单位id
    const reportEnterpriseId = workOrderList.map((item) => item.reportUnitId);
    const reportEnterprise = await RepairEnterprise.find({
      id: { $in: reportEnterpriseId },
    }).select("-_id id companyName");

    // 获取报修人id
    const reporterId = workOrderList.map((item) => item.reporterId);
    const reportUser = await RepairUser.find({
      userId: { $in: reporterId },
    }).select("-_id userId username");

    // 获取维修人id
    const maintenanceUserId = workOrderList.map(
      (item) => item.maintenanceUserId
    );
    const maintenanceUser = await MaintenanceUser.find({
      userId: { $in: maintenanceUserId },
    }).select("-_id userId username");

    // 获取报修类型id
    const serviceId = workOrderList.map((item) => item.serviceClass);
    const service = await ServiceClass.find({
      id: { $in: serviceId },
    }).select("-_id id serviceClass");

    const resultList = workOrderList.map((item) => item.toObject());

    resultList.forEach((item) => {
      item.maintenanceEnterprise = maintenanceEnterprise.find(
        (enterprise) => enterprise.id === item.maintenanceUnitId
      )?.companyName;
      item.reportEnterprise = reportEnterprise.find(
        (enterprise) => enterprise.id === item.reportUnitId
      )?.companyName;
      item.reporter = reportUser.find(
        (user) => user.userId === item.reporterId
      )?.username;
      item.maintenanceUser = maintenanceUser.find(
        (user) => user.userId === item.maintenanceUserId
      )?.username;
      item.serviceClassLabel = service.find(
        (service) => service.id === item.serviceClass
      )?.serviceClass;
      if (showMask === "true") {
        if (item.reporterPhone)
          item.reporterPhone = maskPhoneNumber(item.reporterPhone);

        if (item.reportManagerPhone)
          item.reportManagerPhone = maskPhoneNumber(item.reportManagerPhone);

        if (item.finishManagerPhone)
          item.finishManagerPhone = maskPhoneNumber(item.finishManagerPhone);
      }
    });

    // 获取总数
    const total = await WorkOrder.countDocuments(filtersObj);

    return {
      workOrderList: resultList,
      total,
    };
  } catch (error) {
    throw error;
  }
}

// 获取报修方价格
async function getRepairPriceUtil(params) {
  try {
    let {
      serviceItems,
      settlementType,
      distance,
      workOrderId,
      partsTotal = "",
    } = params;
    // 服务费
    let serviceFee = 0;
    // 一单一结才需要计算服务费
    if (settlementType === "IMMEDIATELY")
      serviceFee = serviceItems?.reduce((acc, item) => {
        acc += item.subtotal;
        return acc;
      }, 0);
    // 交通费 (一单一结时，超出50KM，¥ 2.00/KM)
    const transportFee =
      settlementType === "IMMEDIATELY" && distance > 50
        ? (distance - 50) * 2
        : 0;
    // 配件总价
    if (partsTotal === undefined || partsTotal === null || partsTotal === "") {
      const workOrder = await WorkOrder.findOne({ workOrderId });
      partsTotal = workOrder.partsTotal || 0;
    }

    // 总价
    const totalPrice = serviceFee + transportFee + partsTotal;
    return {
      serviceFee,
      transportFee,
      totalPrice,
      partsTotal,
    };
  } catch (error) {
    throw error;
  }
}

// 获取报修方生效的结算方式
async function getEffectiveSettlementTypeUtil(settlements, serviceItemId) {
  // 目前生效的优先级最高的结算方式
  let highestPrioritySettlement = null;

  // 如果settlements/serviceItemId为空或不是数组，直接返回null
  if (
    !settlements ||
    !serviceItemId ||
    !Array.isArray(settlements) ||
    settlements.length === 0
  ) {
    return highestPrioritySettlement;
  }

  // 获取当前时间在contractPeriod时间戳数组内且status状态为ENABLED启用的结算方式
  const now = new Date();
  const activeSettlements = settlements.filter((item) => {
    const { contractPeriod, status } = item;
    // 验证条件：status状态为ENABLED启用，contractPeriod时间戳数组长度为2，且当前时间在contractPeriod时间戳数组内
    if (
      status !== "ENABLED" ||
      !contractPeriod ||
      contractPeriod.length !== 2
    ) {
      return false;
    }

    const start = new Date(contractPeriod[0]);
    const end = new Date(contractPeriod[1]);
    return now >= start && now <= end;
  });

  if (activeSettlements.length === 0) {
    // 没有生效的结算方式
    return highestPrioritySettlement;
  }

  // 按照优先级排序：YEARLY > YEARLY_WITHOUT_PARTS > IMMEDIATELY
  const priorityMap = {
    YEARLY: 3,
    YEARLY_WITHOUT_PARTS: 2,
    IMMEDIATELY: 1,
  };

  // 如果指定了serviceItemId，筛选包含该serviceItemId的结算方式
  const matchingSettlements = activeSettlements.filter((settlement) => {
    // 如果没有serviceItems，直接返回false
    if (
      !settlement.serviceItems ||
      !Array.isArray(settlement.serviceItems) ||
      settlement.serviceItems.length === 0
    ) {
      return false;
    }

    // 检查对应的serviceItemId
    return settlement.serviceItems.some((serviceItem) => {
      if (serviceItem.serviceItemIds) {
        return serviceItem.serviceItemIds.includes(serviceItemId);
      }

      return false;
    });
  });

  // 如果没有匹配的结算方式，返回null
  if (matchingSettlements.length === 0) {
    return highestPrioritySettlement;
  }

  // 按照优先级排序匹配的结算方式
  matchingSettlements.sort((a, b) => {
    return priorityMap[b.settlementType] - priorityMap[a.settlementType];
  });

  // 获取优先级最高的结算方式
  highestPrioritySettlement = matchingSettlements[0];
  return highestPrioritySettlement;
}

module.exports = {
  getWorkOrderListUtil,
  getRepairPriceUtil,
  getEffectiveSettlementTypeUtil,
  generateUniqueRandomCode,
};
