import { request } from "@/utils/request"

export const login = data => {
  return request.post("/auth/login", {
    params: data
  })
}
export const registerByCodeApi = data => {
  return request.post("/auth/register-client-user-by-code", {
    params: data
  })
}

export const logout = () => {
  return request.post("/auth/logout")
}

export const updatePassword = (data: any) => {
  return request.post("/auth/update-password", {
    params: data
  })
}

// 获取报修人员列表
export const getCustomerUnitStaffList = (params: any) => {
  return request.get("/repair-enterprise/get-repair-user-list", {
    params: params
  })
}
