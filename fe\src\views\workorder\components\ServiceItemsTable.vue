<template>
  <div class="service-items-table">
    <div class="service-items-header">
      <div class="section-title">服务类别与项目</div>
      <div v-if="editable" class="action-buttons">
        <el-button type="primary" link @click="handleAddService"
          >+ 添加</el-button
        >
      </div>
    </div>

    <el-table :data="detail.serviceItems" border style="width: 100%">
      <el-table-column prop="code" label="编号" min-width="100" />
      <el-table-column prop="serviceClassName" label="服务类别" min-width="150">
        <template #default="scope">
          {{ getServiceClassName(scope.row.serviceClass) }}
        </template>
      </el-table-column>
      <el-table-column prop="serviceItem" label="服务项目" min-width="200" />
      <el-table-column prop="unit" label="单位" min-width="80" />
      <el-table-column prop="unitPrice" label="单价" min-width="80">
        <template #default="scope">
          {{ formatPrice(scope.row.unitPrice) }}
        </template>
      </el-table-column>
      <el-table-column prop="quantity" label="数量" min-width="125">
        <template #default="scope">
          <el-input-number
            v-if="editable"
            :model-value="scope.row.quantity"
            :min="1"
            :max="999"
            @input="(value) => changeQuantity(value, scope.row.serviceItemId)"
          />
          <span v-else>{{ formatPrice(scope.row.quantity) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="subtotal" label="金额" min-width="80">
        <template #default="scope">
          {{ formatPrice(scope.row.subtotal) }}
        </template>
      </el-table-column>
      <el-table-column v-if="editable" label="操作" min-width="80">
        <template #default="scope">
          <el-button type="danger" link @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加服务项目弹窗 -->
    <CommonDialog
      v-model:visible="dialogVisible"
      title="添加服务类别与项目"
      width="500px"
    >
      <el-form :model="form" label-width="80px" ref="formRef" :rules="rules">
        <el-form-item label="服务类别" prop="serviceClass">
          <el-select
            v-model="form.serviceClass"
            placeholder="请选择服务类别"
            style="width: 100%"
            @change="handleServiceClassChange"
            :loading="loading.serviceClass"
          >
            <el-option
              v-for="item in serviceClassOptions"
              :key="item.id"
              :label="item.serviceClass"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="服务项目" prop="serviceItemId">
          <el-select
            v-model="form.serviceItemId"
            placeholder="请选择服务项目"
            style="width: 100%"
            popper-class="service-item-select-popper"
            @change="handleServiceItemChange"
            :loading="loading.serviceItem"
          >
            <el-option
              v-for="item in serviceItemOptions"
              :key="item._id"
              :value="item._id"
              :label="item.serviceItem"
              :disabled="
                props.reportUnitSettlementType !== '' &&
                item.settlementType !== props.reportUnitSettlementType
              "
            >
              <template #default>
                <div class="service-item-name">{{ item.serviceItem }}</div>
                <div class="service-item-type">
                  结算方式：{{ getSettlementType(item.settlementType) }}
                </div>
              </template>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="编号">
          <el-input v-model="form.code" disabled />
        </el-form-item>
        <el-form-item label="单位">
          <el-input v-model="form.unit" disabled />
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input-number v-model="form.quantity" :min="1" :max="999" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleConfirmAdd"
            :loading="loading.submit"
          >
            确定
          </el-button>
        </span>
      </template>
    </CommonDialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from "vue";
import { getServiceContentApi, getRepairServiceItemApi } from "@/api";
import { Message, toastError, getSettlementType } from "@/utils";
import { SettlementTypeEnum } from "@/configs";
import { CommonDialog } from "@/base-components";
import { cloneDeep } from "lodash";

const props = defineProps({
  workOrderDetail: {
    type: Object,
  },
  editable: {
    type: Boolean,
    default: false,
  },
  reportUnitSettlementType: {
    type: String,
    default: "",
  },
});

const detail = computed({
  get: () => props.workOrderDetail,
  set: (val) => {
    emit("update:workOrderDetail", val);
  },
});

const emit = defineEmits(["update:workOrderDetail"]);

function changeQuantity(value, serviceItemId) {
  const index = detail.value.serviceItems.findIndex(
    (item) => item.serviceItemId === serviceItemId
  );
  detail.value.serviceItems[index].quantity =
    !value || value <= 0 ? 1 : parseInt(value);
  console.log(detail.value.serviceItems[index].quantity);

  if (index !== -1) {
    detail.value.serviceItems[index].subtotal =
      detail.value.serviceItems[index].unitPrice *
      detail.value.serviceItems[index].quantity;
  }

  emit("update:workOrderDetail", detail.value);
}

const rules = {
  serviceItemId: [
    {
      required: true,
      message: "请选择服务项",
      trigger: "change",
    },
  ],
  quantity: [
    {
      required: true,
      message: "请输入数量",
      trigger: "blur",
    },
  ],
  serviceClass: [
    {
      required: true,
      message: "请选择服务类别",
      trigger: "change",
    },
  ],
};

// 格式化价格
const formatPrice = (price) => {
  if (price === undefined || price === null) return "0.00";
  return Number(price).toFixed(2);
};

// 弹窗相关
const dialogVisible = ref(false);
const formRef = ref(null);
const form = reactive({
  serviceItemId: "",
  code: "",
  serviceClass: "",
  serviceItem: "",
  unit: "",
  unitPrice: 0,
  quantity: 1,
  settlementType: "",
});

// 加载状态
const loading = reactive({
  serviceClass: false,
  serviceItem: false,
  submit: false,
});

// 选项数据
const serviceClassOptions = ref([]);
const serviceItemOptions = ref([]);

// 获取服务类别
const getServiceClass = async () => {
  loading.serviceClass = true;
  try {
    const res = await getServiceContentApi();
    serviceClassOptions.value = res.data.data || [];
  } catch (error) {
    toastError(error, "获取服务类别失败");
  } finally {
    loading.serviceClass = false;
  }
};

function getServiceClassName(serviceClass) {
  const selected = serviceClassOptions.value.find(
    (item) => item.id === serviceClass
  );
  return selected ? selected.serviceClass : "--";
}

// 获取服务项目
const getServiceItems = async (serviceClass) => {
  if (!serviceClass) return;

  loading.serviceItem = true;
  try {
    const res = await getRepairServiceItemApi({
      offset: 0,
      limit: 1000,
      filters: `serviceClass=${serviceClass}`,
      repairUnitId: detail.value.reportUnitId,
    });
    const data = res.data.data.rows;
    serviceItemOptions.value = data;
  } catch (error) {
    toastError(error, "获取服务项目失败");
  } finally {
    loading.serviceItem = false;
  }
};

// 服务类别变更
const handleServiceClassChange = (value) => {
  form.serviceItemId = "";
  form.code = "";
  form.unit = "";
  form.unitPrice = 0;

  // 获取该类别下的服务项目
  getServiceItems(value);
};

// 服务项目变更
const handleServiceItemChange = (value) => {
  const selectedItem = serviceItemOptions.value.find(
    (item) => item._id === value
  );
  if (selectedItem) {
    form.code = selectedItem.code || "";
    form.serviceItem = selectedItem.serviceItem || "";
    form.unit = selectedItem.unit || "";
    form.unitPrice = selectedItem.unitPrice || 0;
    form.settlementType = selectedItem.settlementType || 1;
  }
};

// 添加服务项目
const handleAddService = () => {
  // 重置表单
  form.serviceItemId = "";
  form.code = "";
  form.serviceClass = "";
  form.serviceItem = "";
  form.unit = "";
  form.unitPrice = 0;
  form.quantity = 1;
  form.settlementType = "";

  dialogVisible.value = true;
  formRef.value?.clearValidate();
};

// 确认添加
const handleConfirmAdd = async () => {
  formRef.value?.validate(async (valid) => {
    if (!valid) return;

    loading.submit = true;

    try {
      // 创建新的服务项目对象
      const newItem = {
        serviceItemId: form.serviceItemId,
        code: form.code,
        serviceClass: form.serviceClass,
        serviceItem: form.serviceItem,
        unit: form.unit,
        unitPrice: form.unitPrice,
        quantity: form.quantity,
        subtotal: form.unitPrice * form.quantity,
        reportUnitSettlementType: form.settlementType,
      };

      // 如果没有服务项目数组，创建一个
      if (!detail.value.serviceItems) {
        detail.value.serviceItems = [];
      }

      // 检查是否已存在相同ID的项目
      const existingIndex = detail.value.serviceItems.findIndex(
        (i) => i.serviceItemId === newItem.serviceItemId
      );

      if (existingIndex >= 0) {
        // 如果已存在，报已添加
        return Message.warning("该服务项已添加，无需重复添加");
      } else {
        // 如果不存在，添加新项目
        detail.value.serviceItems.push(newItem);
      }

      // 通知父组件更新
      emit("update:workOrderDetail", detail.value);

      // 关闭弹窗
      dialogVisible.value = false;
      Message.success("添加成功");
    } catch (error) {
      toastError(error, "添加失败");
    } finally {
      loading.submit = false;
    }
  });
};

// 删除服务项目
const handleDelete = (row) => {
  try {
    // 过滤掉要删除的项目
    detail.value.serviceItems = detail.value.serviceItems.filter(
      (i) => i.serviceItemId !== row.serviceItemId
    );

    // 通知父组件更新
    emit("update:workOrderDetail", detail.value);

    Message.success("删除成功");
  } catch (error) {
    toastError(error, "删除失败");
  }
};

// 组件挂载时获取服务类别
onMounted(() => {
  getServiceClass();
});
</script>

<style lang="less" scoped>
.service-items-table {
  margin-bottom: 20px;

  .service-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0;
      padding-left: 10px;
      border-left: 4px solid #409eff;
    }
  }

  .settlement-type-info {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #fdf6ec;
    color: #e6a23c;
    border-radius: 4px;
  }
}
</style>

<style lang="less">
.service-item-select-popper {
  .el-select-dropdown__item {
    height: auto;
    line-height: 1.2;
    padding: 10px;
    box-sizing: border-box;
    .service-item-type {
      color: #999;
      margin-top: 5px;
    }
  }
}
</style>
