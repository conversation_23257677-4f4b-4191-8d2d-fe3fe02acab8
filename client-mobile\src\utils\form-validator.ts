export const mobilePhoneReg = /^1[3-9][0-9]{9}$/ // 手机号正则
const telephoneReg = /[0-9]{3,4}[-][0-9]{8}/ // 固定电话正则
export const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(.[a-zA-Z0-9_-]{2,})+$/ // 邮箱正则
export const chineseNameReg = /^[\u4E00-\u9FA5]{2,10}(·[\u4E00-\u9FA5]{2,10}){0,2}$/ // 姓名正则
export const englishNameReg = /^[a-zA-Z]{1,20}( [a-zA-Z]{1,20}){0,3}$/ // 英文名正则
export const companyNameReg = /^[-（）().,·▪•、，&+#@\u4e00-\u9fa5A-Za-z0-9\u2E80-\uFE4F]{2,100}$/ //企业名称正则

// 固定电话校验
export const telephoneValidator = (value: string) => {
  if (!value) return ""
  else if (telephoneReg.test(value)) return ""
  else return "固定电话格式不正确"
}

// 手机号码校验
export const mobilePhoneValidator = (value: string) => {
  if (!value) return ""
  else if (mobilePhoneReg.test(value)) return ""
  else return "手机号码格式不正确"
}

// 电话号码校验
export const phoneNumberValidator = (value: string) => {
  const mobilePhoneReg = /^1[3-9][0-9]{9}$/
  const telephoneReg = /[0-9]{3,4}[-][0-9]{8}/
  if (!value) return ""
  else if (mobilePhoneReg.test(value) || telephoneReg.test(value)) return ""
  else return "电话号码格式不正确"
}

// 企业名称校验
export const companyNameValidator = (value: string) => {
  const reg = /^[-（）().,·▪•、，&+#@\u4e00-\u9fa5A-Za-z0-9\u2E80-\uFE4F]{2,100}$/
  if (!reg.test(value)) return "请输入为2-50个中文或英文的字符"
  return ""
}

// 身份证号校验
export const IdNumberValidator = (value: string) => {
  const id18Reg = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/ // 18位身份证
  const id15Reg = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/ // 15位身份证
  if (!value) return ""
  else if (id18Reg.test(value) || id15Reg.test(value)) return ""
  else return "请输入正确的身份证号码"
}

// 邮箱校验
export const emailValidator = (value: string) => {
  if (!value) return ""
  else if (emailReg.test(value)) return ""
  else return "邮箱格式不正确"
}

// 账号验证(手机号+邮箱)
export const accountValidator = (value: string) => {
  const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]{2,})+$/
  const mobilePhoneReg = /^1[3-9][0-9]{9}$/
  if (!value) return ""
  else if (emailReg.test(value) || mobilePhoneReg.test(value)) return ""
  else return "请输入正确的手机号或邮箱"
}

// 密码校验（包含数字、大、小写字母、字符其中三种的组合，长度不低于8,不高于32)
export const mixPasswordValidator = (value: string) => {
  if (value === "") {
    return "请输入密码"
  } else if (value.length < 8) {
    return "密码长度不能少于8位"
  } else if (value.length > 32) {
    return "密码长度不能超过32位"
  } else {
    const reg =
      /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])|(?=.*\d)(?=.*[a-z])(?=.*[!@#$%^&*.])|(?=.*\d)(?=.*[A-Z])(?=.*[!@#$%^&*.])|(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*.]).{8,32}$/
    if (reg.test(value)) {
      return ""
    } else {
      return "请输入数字、大写字母、小写字母、特殊字符中的任意三种组合"
    }
  }
}

// 密码校验（至少包含数字、字母和字符的组合，长度不低于6,不高于32)
export const oldPasswordValidator = (value: string) => {
  if (value === "") {
    return "请输入密码"
  } else {
    const reg =
      /^(?=(.*[a-zA-Z].*[a-zA-Z])|.*[a-zA-Z].*[!@#$%^&*()-_+=]|.*[!@#$%^&*()-_+=].*[a-zA-Z])(?=(.*\d.*\d)|.*\d.*[!@#$%^&*()-_+=]|.*[!@#$%^&*()-_+=].*\d).{6,32}$/
    if (reg.test(value)) {
      return ""
    } else {
      return "请输入至少6位数字、大小写字母和字符的组合"
    }
  }
}

// 密码校验，6位数字
export const numberPasswordValidator = (value: string) => {
  const passwordReg = /^[0-9]{6}$/
  if (!value) return ""
  else if (passwordReg.test(value)) return ""
  else return "请输入6位数字密码"
}

// 姓名校验
export const nameValidator = (value: string) => {
  const chineseNameReg = /^[\u4E00-\u9FA5]{2,10}(·[\u4E00-\u9FA5]{2,10}){0,2}$/
  const englishNameReg = /^[a-zA-Z]{1,20}( [a-zA-Z]{1,20}){0,3}$/
  if (!value) return ""
  else if (["admin", "user", "root"].includes(value)) return "不合法的姓名"
  else if (chineseNameReg.test(value) || englishNameReg.test(value)) return ""
  else return "不合法的姓名"
}

// 合同编号中的数据标识校验
export const numberMarkValidator = (value: string) => {
  const Reg = /^[A-Za-z0-9]+(-[A-Za-z0-9]+)*$/gi
  if (!value) return ""
  else if (Reg.test(value)) return ""
  else return "格式有误"
}

// 港澳通行证校验
export const HKAndMacauPermitValidator = (value: string) => {
  const MacauReg = /^[H|M|C|W|][0-9]{8,10}$/
  if (!MacauReg.test(value)) {
    return "请输入正确的港澳证件号码"
  } else {
    return ""
  }
}

//台胞证与护照号码的校验规则
export const otherCardValidator = (value: string) => {
  const TaiwanReg = /^[a-zA-Z0-9]{5,18}$/
  if (!TaiwanReg.test(value)) {
    return "请输入正确的证件号码"
  } else {
    return ""
  }
}

// 金额校验
export const amountValidator = (value: string) => {
  const amountReg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/
  if (!value) return ""
  else if (amountReg.test(value)) return ""
  else return "请输入有效金额数字(例如：99.99)"
}

// 金额校验
export const bankAmountValidator = (value: string) => {
  const amountReg = /^[0]{1}([.]([0-9]){2})?$/
  if (!value) return ""
  else if (amountReg.test(value)) return ""
  else return "请输入0~1的两位小数金额"
}

// 年份校验
export const yearValidator = (value: string) => {
  const yearReg = /^\d{4}$/
  if (!value) return ""
  else if (yearReg.test(value)) return ""
  else return "请输入有效的年份"
}

// 合同模板中的有效数字校验
export const numberValidator = (value: string) => {
  const numberReg = /^[0-9]+(\.[0-9]+)?$/
  if (!value) return ""
  else if (numberReg.test(value)) return ""
  else return "请输入有效数字"
}

// 校验合同编号合法性
export const validateContractNumberFn = (value: string) => {
  const data = value.trim()
  if (!data) return ""
  const reg = /^[A-Za-z0-9]+(-[A-Za-z0-9]+)*$/gi
  if (!reg.test(data)) {
    return "请输入正确的合同编号"
  }
  if (value.length > 50) {
    return "合同编号长度不能超过50个字符"
  }
  return ""
}

//统一社会信用代码校验
export const creditCodeValidator = (value: string) => {
  const creditCodeReg = /^(?:(?![IOZSV])[\dA-Z]){2}\d{6}(?:(?![IOZSV])[\dA-Z]){10}$/
  if (value) {
    if (!creditCodeReg.test(value)) {
      return "请输入正确的统一社会信用代码"
    } else {
      return ""
    }
  } else {
    return ""
  }
}

//注册编号
export const registeredCertificateCodeValidator = (value: string) => {
  const creditCodeReg = /^[a-zA-Z0-9]{5,18}$/
  if (value) {
    if (!creditCodeReg.test(value)) {
      return "请输入正确的注册证书编号"
    } else {
      return ""
    }
  } else {
    return ""
  }
}

//银行卡号校验
export const bankCardNoValidator = (value: string) => {
  const reg = /^[0-9]{9,25}$/
  if (value) {
    if (!reg.test(value)) {
      return "请输入9~25位数字的收款账号!"
    } else return ""
  } else {
    return ""
  }
}

//验证码校验
export const verifyCodeValidator = (value: string) => {
  const codeReg = /^[0-9]{6}$/
  if (!value) return ""
  else if (codeReg.test(value)) return ""
  else return "验证码格式不正确"
}
