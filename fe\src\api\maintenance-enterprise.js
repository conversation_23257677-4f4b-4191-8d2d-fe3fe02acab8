import axios from "./axios-instance";
// 上传资质文件
export function uploadQualificationFile(file) {
  // 确保传入的是有效的File对象
  if (!(file instanceof File)) {
    console.error("上传的不是有效的File对象:", file);
    return Promise.reject(new Error("上传的不是有效的File对象"));
  }

  const formData = new FormData();
  formData.append("file", file);

  return axios.post(
    `/api/maintenance-enterprise/upload-qualification-file`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
}

// 新建入驻单位
export function createMaintenanceEnterprise(data) {
  return axios.post(
    `/api/maintenance-enterprise/create-maintenance-enterprise`,
    data,
    {
      headers: {
        checkSensitive: true,
      },
    }
  );
}

// 编辑入驻单位
export function editMaintenanceEnterprise(data) {
  return axios.put(
    `/api/maintenance-enterprise/edit-maintenance-enterprise`,
    data,
    {
      headers: {
        checkSensitive: true,
      },
    }
  );
}

// 获取入驻单位列表
export function getMaintenanceEnterpriseList(params) {
  return axios.get(
    `/api/maintenance-enterprise/get-maintenance-enterprise-list`,
    { params }
  );
}

// 获取入驻单位详情
export function getMaintenanceEnterpriseDetail(id) {
  return axios.get(
    `/api/maintenance-enterprise/get-maintenance-enterprise-detail/${id}`
  );
}

// 更新入驻单位状态
export function updateMaintenanceEnterpriseStatus(data) {
  return axios.put(
    `/api/maintenance-enterprise/update-maintenance-enterprise-status`,
    data
  );
}

// 获取入驻单位人员列表
export function getMaintenanceEnterpriseStaffList(params) {
  return axios.get(
    `/api/maintenance-enterprise/get-maintenance-enterprise-staff-list`,
    { params }
  );
}

// 获取入驻单位人员详情
export function getMaintenanceEnterpriseStaffDetail(userId) {
  return axios.get(
    `/api/maintenance-enterprise/get-maintenance-enterprise-staff-detail`,
    { params: { userId } }
  );
}

// 新建入驻单位人员
export function createMaintenanceEnterpriseStaff(data) {
  return axios.post(
    `/api/maintenance-enterprise/create-maintenance-enterprise-staff`,
    data,
    {
      headers: {
        checkSensitive: true,
      },
    }
  );
}

// 编辑入驻单位人员
export function editMaintenanceEnterpriseStaff(data) {
  return axios.put(
    `/api/maintenance-enterprise/edit-maintenance-enterprise-staff`,
    data,
    {
      headers: {
        checkSensitive: true,
      },
    }
  );
}

// 审核入驻单位人员
export function auditMaintenanceEnterpriseStaff(data) {
  return axios.put(
    `/api/maintenance-enterprise/audit-maintenance-enterprise-staff`,
    data,
    {
      headers: {
        checkSensitive: true,
      },
    }
  );
}

// 编辑入驻单位人员状态
export function editMaintenanceEnterpriseStaffStatus(data) {
  return axios.put(
    `/api/maintenance-enterprise/edit-maintenance-enterprise-staff-status`,
    data
  );
}

// 获取入驻单位结算信息
export function getMaintenanceEnterpriseSettlementList(unitId) {
  return axios.get(
    `/api/maintenance-enterprise/get-maintenance-enterprise-settlement-list`,
    { params: { unitId } }
  );
}

// 创建结算信息
export function createMaintenanceEnterpriseSettlement(data) {
  return axios.post(
    `/api/maintenance-enterprise/create-maintenance-enterprise-settlement`,
    data,
    {
      headers: {
        checkSensitive: true,
      },
    }
  );
}

// 编辑结算信息
export function editMaintenanceEnterpriseSettlement(data) {
  return axios.put(
    `/api/maintenance-enterprise/edit-maintenance-enterprise-settlement`,
    data,
    {
      headers: {
        checkSensitive: true,
      },
    }
  );
}

// 删除结算信息
export function deleteMaintenanceEnterpriseSettlement(data) {
  return axios.delete(
    `/api/maintenance-enterprise/delete-maintenance-enterprise-settlement`,
    { data }
  );
}

// 配置结算价格
export function editMaintenanceEnterpriseSettlementPrice(data) {
  return axios.post(
    `/api/maintenance-enterprise/config-maintenance-enterprise-settlement-price`,
    data,
    {
      headers: {
        checkSensitive: true,
      },
    }
  );
}
