<template>
  <el-form
    :size="props.size"
    class="page-search-form"
    @keydown.enter="$emit('queryBtnClick')"
  >
    <slot></slot>
    <el-form-item>
      <el-button
        :size="props.size"
        :icon="RefreshLeft"
        @click="$emit('resetBtnClick')"
      >
        重置
      </el-button>
      <el-button
        :size="props.size"
        type="primary"
        :icon="Search"
        @click="$emit('queryBtnClick')"
      >
        查询
      </el-button>
      <slot name="action"></slot>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { h } from "vue";
import { RefreshLeft, Search } from "@element-plus/icons-vue";

const props = defineProps({
  size: {
    type: String,
    default: "default",
  },
});

defineEmits(["resetBtnClick", "queryBtnClick"]);
</script>

<style lang="less" scoped>
.page-search-form {
  display: flex;
  column-gap: 20px;
  padding-top: 20px;
  flex-wrap: wrap;

  :deep(.el-date-editor) {
    width: 240px;
  }
  :deep(.el-form-item__content) {
    min-width: 220px;
  }
}
</style>
