import { TableColumnItem } from "@/types";

export const serviceClassColumns: Array<TableColumnItem> = [
  { prop: "serviceClassName", label: "服务类型", minWidth: 180 },
  { prop: "code", label: "服务项目编号", minWidth: 180 },
  { prop: "serviceItem", label: "服务项目", minWidth: 250 },
  { prop: "unit", label: "单位", minWidth: 100 },
  { prop: "unitPrice", label: "售价", minWidth: 120 },
  { prop: "maxPrice", label: "成本最高限价", minWidth: 120 },
  { prop: "taxRate", label: "税率", minWidth: 100 },
  { prop: "operations", label: "操作", minWidth: 120, fixed: "right" },
];
