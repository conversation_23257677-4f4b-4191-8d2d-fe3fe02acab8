<template>
  <div v-loading="loading || submitLoading" class="admission-detail">
    <div class="admission-detail-header">
      <el-button :icon="ArrowLeftBold" link @click="handleCancel"
        >返回</el-button
      >
      <div>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button
          v-if="enterpriseId && formData.status === CommonStatusEnum.DISABLED"
          type="primary"
          @click="changeEnterpriseStatus(CommonStatusEnum.ENABLED)"
        >
          启用
        </el-button>
        <el-button
          v-if="enterpriseId && formData.status === CommonStatusEnum.ENABLED"
          type="danger"
          @click="changeEnterpriseStatus(CommonStatusEnum.DISABLED)"
        >
          禁用
        </el-button>
      </div>
    </div>

    <div class="admission-detail-content">
      <el-form
        class="admission-detail-form"
        :model="formData"
        label-suffix="："
        label-width="120px"
        :rules="formRules"
        ref="formRef"
      >
        <!-- 基本信息 -->
        <div class="section-title">基本信息</div>
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model.trim="formData.companyName" placeholder="请输入" />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="法人姓名" prop="legalPerson">
              <el-input
                v-model.trim="formData.legalPerson"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人联系电话" prop="legalPersonPhone">
              <el-input
                v-model.trim="formData.legalPersonPhone"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人姓名" prop="contactPerson">
              <el-input
                v-model.trim="formData.contactPerson"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人电话" prop="contactPhone">
              <el-input
                v-model.trim="formData.contactPhone"
                placeholder="请输入"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="公司办公地址" prop="companyAddress">
          <el-input
            v-model.trim="formData.companyAddress"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label="作业区域" prop="operationArea">
          <el-select
            v-model="formData.operationArea"
            multiple
            placeholder="请选择"
            :loading="dictionaryLoading"
          >
            <el-option
              v-for="item in operationAreaOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 资质信息 -->
        <div class="section-title">资质信息</div>
        <el-form-item label="资质文件" prop="qualificationFiles">
          <div class="upload-container">
            <div class="upload-header">
              <span style="color: #aaa">
                （支持类型PDF、DOC、DOCX、JPG、JPEG、PNG，请注意上传的数据中是否包含敏感信息）
              </span>
              <span>
                资质文件包含（营业执照、安全生产许可证、根据业务需要由相关部门颁发的资质证书，如特种作业操作证等）
              </span>
            </div>
            <div class="qualification-file-list">
              <div
                v-for="(item, index) in showFileList"
                :key="index"
                class="qualification-file-item"
              >
                <div class="file-info">
                  <el-icon
                    v-if="item.status === 'success'"
                    style="color: #67c23a"
                  >
                    <SuccessFilled />
                  </el-icon>
                  <el-icon
                    v-if="item.status === 'error'"
                    style="color: #f56c6c"
                  >
                    <CircleCloseFilled />
                  </el-icon>
                  <el-icon
                    v-if="item.status === 'uploading'"
                    class="is-loading"
                    style="color: #e6a23c"
                  >
                    <Loading />
                  </el-icon>
                  <div class="file-name">{{ item.fileName }}</div>
                  <div class="file-actions">
                    <!-- <el-button link type="primary"> 预览 </el-button> -->
                    <el-button
                      v-if="item.status === 'success'"
                      link
                      type="primary"
                      @click="handleDownloadFile(item)"
                    >
                      下载
                    </el-button>
                    <el-button
                      link
                      type="danger"
                      @click="() => handleDeleteFile(item)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <div v-if="item.errorMessage" class="file-hint">
                  {{ item.errorMessage }}
                </div>
              </div>
            </div>
            <!-- 只能上传PDF、DOC、DOCX、JPG、JPEG、PNG -->
            <el-upload
              action="#"
              multiple
              accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,image/jpeg,image/png,image/jpg"
              :max-count="10"
              :show-file-list="false"
              :auto-upload="false"
              :on-change="uploadFile"
            >
              <div class="upload-button">
                <el-icon><Upload /></el-icon>
                <span>上传文件</span>
              </div>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  ArrowLeftBold,
  SuccessFilled,
  CircleCloseFilled,
  Loading,
} from "@element-plus/icons-vue";
import {
  uploadQualificationFile,
  createMaintenanceEnterprise,
  editMaintenanceEnterprise,
  getDictionaryList,
  getMaintenanceEnterpriseDetail,
  updateMaintenanceEnterpriseStatus,
  getFileData,
  deleteFile,
} from "@/api";
import {
  phoneNumberValidator,
  companyNameValidator,
  nameValidator,
} from "@/utils";
import { Upload } from "@element-plus/icons-vue";
import { CommonStatusEnum } from "@/configs";
import { toastError, Message } from "../../utils/message-tool";
import { cloneDeep } from "lodash";
import { downloadFile } from "../../utils";

const route = useRoute();
const router = useRouter();
const formRef = ref();

// 根据路由参数判断页面状态
const enterpriseId = ref("");

// 表单数据
const initFormData = {
  companyName: "",
  legalPerson: "",
  legalPersonPhone: "",
  contactPerson: "",
  contactPhone: "",
  companyAddress: "",
  operationArea: [] as Array<any>,
  qualificationFiles: [] as Array<any>,

  status: "",
};
const formData = ref(cloneDeep(initFormData));

const loading = ref(false);
// 初始化数据
onMounted(async () => {
  enterpriseId.value = route.query.enterpriseId as string;
  resetForm();
  refreshData();
});

async function refreshData() {
  if (enterpriseId.value) {
    loading.value = true;
    getMaintenanceEnterpriseDetail(enterpriseId.value)
      .then((res) => {
        const data = res.data.data;
        for (let key in formData.value) {
          if (data[key]) {
            if (key === "qualificationFiles")
              showFileList.value = cloneDeep(data[key]);
            formData.value[key] = data[key];
          }
        }
        showFileList.value.forEach((item) => {
          item.status = "success";
        });
        loading.value = false;
      })
      .catch((err) => {
        toastError(err, "获取详情失败");
        loading.value = false;
      });
  }
}
/* ============================================ 字典数据 ============================================ */
const dictionaryLoading = ref(false);
const operationAreaOptions = ref<Array<any>>([]);
// 获取字典列表
onMounted(async () => {
  try {
    dictionaryLoading.value = true;
    const res = await getDictionaryList("operationArea");
    operationAreaOptions.value =
      res.data.find((item) => item.groupValue === "operationArea")?.children ||
      [];
    dictionaryLoading.value = false;
  } catch (error) {
    toastError(error, "获取字典列表失败");
    dictionaryLoading.value = false;
  }
});

/* ============================================ 企业状态 ============================================ */
const changeEnterpriseStatus = (status) => {
  submitLoading.value = true;
  updateMaintenanceEnterpriseStatus({
    id: enterpriseId.value,
    status: status,
  })
    .then((res) => {
      Message.success("操作成功");
      refreshData();
      submitLoading.value = false;
    })
    .catch((err) => {
      toastError(err, "操作失败");
      submitLoading.value = false;
    });
};
/* ============================================ 资质文件 ============================================ */
const showFileList = ref<Array<any>>([]);
// 是否是规定的上传类型
const isAllowedFileType = (fileType) => {
  return (
    fileType === "application/pdf" ||
    fileType === "application/msword" ||
    fileType ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
    fileType === "image/jpeg" ||
    fileType === "image/png" ||
    fileType === "image/jpg"
  );
};
// 上传文件
const uploadFile = async (file, fileList) => {
  if (!isAllowedFileType(file.raw.type)) {
    showFileList.value.push({
      uid: file.uid,
      fileId: "",
      fileName: file.name,
      status: "error",
      errorMessage: "只支持上传PDF、DOC、DOCX、JPG、JPEG、PNG文件",
    });
    return;
  }
  showFileList.value.push({
    uid: file.uid,
    fileId: "",
    fileName: file.name,
    status: "uploading",
  });

  try {
    // 使用文件的raw属性，这是实际的File对象
    const res = await uploadQualificationFile(file.raw);

    if (res.data && res.data.fileId) {
      // 添加到文件列表
      formData.value.qualificationFiles.push({
        fileName: file.name,
        fileId: res.data.fileId,
        fileType: file.raw.type,
      });
      const index = showFileList.value.findIndex(
        (item) => item.uid === file.uid
      );
      if (index !== -1) {
        showFileList.value[index].status = "success";
        showFileList.value[index].fileId = res.data.fileId;
      }
    }
  } catch (error) {
    const index = showFileList.value.findIndex((item) => item.uid === file.uid);
    if (index !== -1) showFileList.value[index].status = "error";
    console.error("文件上传失败:", error);
  }
};

// 删除文件
const deleteFileLoading = ref<Array<string>>([]);
const handleDeleteFile = async (file) => {
  try {
    //新上传的删除
    if (file.fileId) deleteFileLoading.value.push(file.fileId);
    else deleteFileLoading.value.push(file.uid);
    if (file.uid && file.fileId) {
      await deleteFile([file.fileId]);
    }
    showFileList.value = showFileList.value.filter(
      (item) =>
        item.fileId !== file.fileId &&
        ((item.uid && item.uid !== file.uid) || !item.uid)
    );
    formData.value.qualificationFiles =
      formData.value.qualificationFiles.filter(
        (item) =>
          item.fileId !== file.fileId &&
          ((item.uid && item.uid !== file.uid) || !item.uid)
      );
  } catch (error) {
    toastError(error, "删除文件失败");
  } finally {
    deleteFileLoading.value = deleteFileLoading.value.filter(
      (item) => item !== file.fileId && item !== file.uid
    );
  }
};

// 下载文件
const handleDownloadFile = (file) => {
  getFileData(file.fileId).then((res) => {
    console.log("下载文件:", res);
    downloadFile({
      fileData: res.data,
      fileName: file.fileName,
      fileType: file.fileType,
    });
  });
};

/* ============================================ 表单存储 ============================================== */
const submitLoading = ref(false);
const requiredRules = {
  required: true,
  message: "该值不可为空",
  trigger: "change",
};
// 表单校验规则
const formRules = {
  companyName: [
    { required: true, message: "请输入公司名称", trigger: "change" },
    { validator: companyNameValidator, trigger: "change" },
  ],
  legalPerson: [
    { required: true, message: "请输入法人姓名", trigger: "change" },
    { validator: nameValidator, trigger: "change" },
  ],
  legalPersonPhone: [{ validator: phoneNumberValidator, trigger: "change" }],
  contactPerson: [
    { required: true, message: "请输入联系人姓名", trigger: "change" },
    { validator: nameValidator, trigger: "change" },
  ],
  contactPhone: [
    { required: true, message: "请输入联系人电话", trigger: "change" },
    { validator: phoneNumberValidator, trigger: "change" },
  ],
  companyAddress: [
    { required: true, message: "请输入公司办公地址", trigger: "change" },
    { max: 200, message: "公司办公地址不能超过200个字符", trigger: "change" },
  ],
  operationArea: [
    { required: true, message: "请选择作业区域", trigger: "change" },
  ],
  qualificationFiles: [
    { required: true, message: "请上传资质文件", trigger: "change" },
  ],
};

function resetForm() {
  formData.value = cloneDeep(initFormData);
  formRef.value?.clearValidate();
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (!enterpriseId.value) createEnterprise();
      else updateEnterprise();
    }
  });
};

// 取消
const handleCancel = () => {
  router.back();
};

// 创建企业
const createEnterprise = async () => {
  submitLoading.value = true;
  const operationAreaLabel = operationAreaOptions.value
    .filter((item) => formData.value.operationArea.includes(item.value))
    .map((item) => item.label)
    .join(",");
  createMaintenanceEnterprise({
    ...formData.value,
    operationAreaLabel,
  })
    .then((res) => {
      Message.success("新建成功");
      router.back();
      submitLoading.value = false;
    })
    .catch((err) => {
      submitLoading.value = false;
      toastError(err, "新建失败");
    });
};

// 更新企业
const updateEnterprise = () => {
  submitLoading.value = true;
  const operationAreaLabel = operationAreaOptions.value
    .filter((item) => formData.value.operationArea.includes(item.value))
    .map((item) => item.label)
    .join(",");
  editMaintenanceEnterprise({
    id: enterpriseId.value,
    ...formData.value,
    operationAreaLabel,
  })
    .then((res) => {
      Message.success("更新成功");
      submitLoading.value = false;
    })
    .catch((err) => {
      submitLoading.value = false;
      toastError(err, "更新失败");
    });
};
</script>

<style lang="less" scoped>
.admission-detail {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  .admission-detail-header {
    padding: 10px 20px;
    box-sizing: border-box;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
  }

  .admission-detail-content {
    padding: 0px 20px 20px;
    box-sizing: border-box;
    .admission-detail-form {
      .upload-container {
        width: 100%;
        .upload-header {
          display: flex;
          flex-direction: column;
          line-height: 1;
          margin: 8px 0 12px;
          gap: 10px;
        }
        .qualification-file-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-bottom: 10px;
          .qualification-file-item {
            width: calc(50% - 5px);
            height: 50px;
            padding: 0px 10px;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .file-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .file-name {
                flex: 1;
                min-width: 0px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin: 0px 6px;
              }
            }
            .file-hint {
              margin-top: 5px;
              line-height: 1;
              color: var(--el-color-danger);
            }
          }
        }
        .upload-button {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 32px;
          border-radius: 4px;
          border: 1px solid var(--el-color-primary);
          background-color: var(--el-color-primary);
          color: #fff;
          padding: 0 20px;
          &:hover {
            background-color: var(--el-color-primary-light-3);
            border-color: var(--el-color-primary-light-3);
          }
          span {
            margin-left: 12px;
          }
        }
      }
    }
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin: 20px 0;
      padding-left: 10px;
      border-left: 4px solid #409eff;
    }
    .settlement-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 18px;
      gap: 18px;
      .settlement-item {
        padding: 18px 18px 0;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .el-form-item {
          flex: 1;
          min-width: 300px;
        }
      }
      .el-button {
        margin-left: 0px;
      }
    }
  }
}
</style>
