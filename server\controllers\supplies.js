const { SuppliesClass } = require("../models/SuppliesClass");
const { v4: uuid } = require("uuid");

// 获取备件类别
module.exports.getSuppliesClass = async (req, res) => {
  try {
    const data = await SuppliesClass.find().lean();
    // 递归组装树结构
    function buildTree(list, parentId = "") {
      return list
        .filter((item) => item.parentId === parentId)
        .sort((a, b) => a.sort - b.sort)
        .map((item) => ({
          ...item,
          children: buildTree(list, item.id),
        }));
    }
    const tree = buildTree(data);
    res.status(200).json({
      success: true,
      data: tree,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取备件类别失败",
      error: error.message,
    });
  }
};

// 添加备件类别
module.exports.addSuppliesClass = async (req, res) => {
  try {
    const { name, remark, code, parentId } = req.body;
    const id = uuid();
    console.log(SuppliesClass);
    //当前类别的其他兄弟数据
    const brothers = await SuppliesClass.find({ parentId });
    await SuppliesClass.create({
      name,
      remark,
      code,
      id,
      parentId,
      sort: brothers.length + 1,
    });
    res.status(200).json({
      success: true,
      message: "添加备件类别成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "添加备件类别失败",
      error: error.message,
    });
  }
};
