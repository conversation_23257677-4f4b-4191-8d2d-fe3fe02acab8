<template>
  <el-icon
    color="#999"
    :style="{ fontSize: props.iconSize + 'px', width: '100%', height: '100%' }"
    @click="showPreview = true"
  >
    <VideoCamera />
  </el-icon>

  <!-- 视频预览 -->
  <div v-if="showPreview" class="video-container">
    <el-icon
      class="close-icon"
      size="24"
      color="#fff"
      @click="showPreview = false"
    >
      <Close />
    </el-icon>
    <video :src="blobUrl" controls style="width: 80%; height: 80%"></video>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { getFileData } from "@/api";
import { VideoCamera, Close } from "@element-plus/icons-vue";

const props = withDefaults(
  defineProps<{
    fileId: string;
    iconSize?: number;
  }>(),
  { iconSize: 20 }
);

const blobUrl = ref("");
const loading = ref(false);
const imageDirection = ref(false);
const showPreview = ref(false);
watch(
  () => props.fileId,
  () => {
    if (!props.fileId) return (blobUrl.value = "");
    loading.value = true;

    getFileData(props.fileId)
      .then((res) => {
        blobUrl.value = URL.createObjectURL(res.data);
        loading.value = false;

        //判断图片方向
        const image = new Image();
        image.src = blobUrl.value;
        image.onload = (_) => {
          if (props.width && props.height) {
            if (image.width / image.height >= props.width / props.height) {
              imageDirection.value = true;
            } else {
              imageDirection.value = false;
            }
          }
        };
      })
      .catch((err) => {
        blobUrl.value = "";
      });
  },
  {
    immediate: true,
  }
);
</script>

<style lang="less" scoped>
.video-container {
  width: 100%;
  height: 100%;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  .close-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
  }
}
</style>
