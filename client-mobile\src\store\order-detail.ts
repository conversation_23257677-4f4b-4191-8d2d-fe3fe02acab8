import { defineStore } from "pinia"

export const useOrderDetailStore = defineStore("ORDER_DETAIL", {
  state: () => ({
    orderInfo: uni.getStorageSync("orderInfo") || {}, //表单详情
    finishRepair: uni.getStorageSync("finishRepair") || {}, //完成维修数据
    signatureConfirm: uni.getStorageSync("signatureConfirm") || false, //工单确认数据
    signatureFrom: uni.getStorageSync("signatureFrom") || "", //签名的上一个页面
    signatureBase64: uni.getStorageSync("signatureBase64") || "", //签名的base64数据
    selectManagerConfirm: uni.getStorageSync("selectManagerConfirm") || false, //选择管理员确认数据
    selectManagerFrom: uni.getStorageSync("selectManagerFrom") || "", //选择管理员的上一个页面
    selectManagerInfo: uni.getStorageSync("selectManagerInfo") || {} //选择管理员数据
  }),
  actions: {
    setOrderInfo(orderInfo: any) {
      // 更新 storage
      uni.setStorageSync("orderInfo", orderInfo)

      // 更新 state
      this.orderInfo = orderInfo
    },
    setFinishRepair(finishRepair: any) {
      // 更新 storage
      uni.setStorageSync("finishRepair", finishRepair)

      // 更新 state
      this.finishRepair = finishRepair
    },
    setSignatureFrom() {
      const nowPage =
        getCurrentPages().length > 0 ? getCurrentPages()[getCurrentPages().length - 1].$page.fullPath : "/"
      // 更新 storage
      uni.setStorageSync("signatureFrom", nowPage)
      uni.setStorageSync("signatureConfirm", false)

      // 更新 state
      this.signatureFrom = nowPage
      this.signatureConfirm = false
    },
    setSignatureBase64(signatureBase64: any) {
      // 更新 storage
      uni.setStorageSync("signatureBase64", signatureBase64)
      uni.setStorageSync("signatureConfirm", true)

      // 更新 state
      this.signatureBase64 = signatureBase64
      this.signatureConfirm = true
    },
    clearSignatureInfo() {
      console.log("clear")
      uni.setStorageSync("signatureFrom", "")
      uni.setStorageSync("signatureConfirm", false)
      uni.setStorageSync("signatureBase64", "")

      this.signatureFrom = ""
      this.signatureConfirm = false
      this.signatureBase64 = ""
    },
    setSelectManagerFrom() {
      const nowPage =
        getCurrentPages().length > 0 ? getCurrentPages()[getCurrentPages().length - 1].$page.fullPath : "/"
      // 更新 storage
      uni.setStorageSync("selectManagerFrom", nowPage)
      uni.setStorageSync("selectManagerConfirm", false)

      // 更新 state
      this.selectManagerFrom = nowPage
      this.selectManagerConfirm = false
    },
    setSelectManagerInfo(selectManagerInfo: any) {
      // 更新 storage
      uni.setStorageSync("selectManagerInfo", selectManagerInfo)
      uni.setStorageSync("selectManagerConfirm", true)

      // 更新 state
      this.selectManagerInfo = selectManagerInfo
      this.selectManagerConfirm = true
    },
    clearSelectManagerInfo() {
      uni.setStorageSync("selectManagerFrom", "")
      uni.setStorageSync("selectManagerConfirm", false)
      uni.setStorageSync("selectManagerInfo", {})

      this.selectManagerFrom = ""
      this.selectManagerConfirm = false
      this.selectManagerInfo = {}
    }
  }
})
