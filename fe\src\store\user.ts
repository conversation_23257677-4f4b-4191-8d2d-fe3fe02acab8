import { defineStore } from "pinia";
import { UserTypeEnum } from "@/configs";

const useUserStore = defineStore("USER_INFO", {
  state: () => {
    return {
      userId: "",
      username: "",
      phone: "",
      userType: UserTypeEnum.PLATFORM,
      unit: "",
      token: "",
      rememberPhone: false,
    };
  },
  actions: {
    // 设置用户信息
    setUserInfo(userInfo: any) {
      this.userId = userInfo.userId || "";
      this.username = userInfo.username || "";
      this.phone = userInfo.phone || "";
      this.userType = userInfo.userType || UserTypeEnum.PLATFORM;
      this.unit = userInfo.unit || "";
      this.token = userInfo.token || "";
    },
    // 清除用户信息（登出时使用）
    clearUserInfo() {
      this.userId = "";
      this.username = "";
      this.userType = UserTypeEnum.PLATFORM;
      this.unit = "";
      this.token = "";
      if (!this.rememberPhone) {
        this.phone = "";
      }
    },
  },
  persist: true,
});

export default useUserStore;
