import { UserStatusEnum } from "@/configs";

// 账号状态转换
export function convertUserStatus(status: string) {
  switch (status) {
    case UserStatusEnum.ENABLED:
      return {
        label: "正常",
        type: "success",
        value: UserStatusEnum.ENABLED,
      };
    case UserStatusEnum.WAITING_AUDIT:
      return {
        label: "待审核",
        type: "warning",
        value: UserStatusEnum.WAITING_AUDIT,
      };
    case UserStatusEnum.AUDIT_FAILED:
      return {
        label: "审核不通过",
        type: "danger",
        value: UserStatusEnum.AUDIT_FAILED,
      };
    case UserStatusEnum.DISABLED:
      return {
        label: "已禁用",
        type: "danger",
        value: UserStatusEnum.DISABLED,
      };
  }
}
