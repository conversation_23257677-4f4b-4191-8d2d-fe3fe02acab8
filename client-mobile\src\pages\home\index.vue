<template>
  <view class="index-container">
    <view class="status-bar"></view>
    <scroll-view
      class="page-scroll-view"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh">
      <view class="banner-box">
        <view class="banner-text">
          <text class="text-title">维保即时修</text>
          <text class="text-hint">维修上门服务</text>
        </view>
        <image class="banner-img" src="/static/banner.png" mode="aspectFill"></image>
      </view>

      <view class="index-content">
        <view class="service-box">
          <view class="left-service">
            <image class="left-service-img" src="/static/home/<USER>" mode="aspectFill"></image>
            <view class="left-action">
              <text>填报报修</text>
              <view class="action-item" @click="navigateTo('/pages/repair-order/detail/index')">
                <text>立即前往</text>
              </view>
            </view>
          </view>
          <view class="right-service">
            <view class="service-item" style="background-color: #dffefd" @click="callRepair">
              <text>电话报修</text>
              <image class="service-item-img" src="/static/home/<USER>" mode="aspectFit"></image>
            </view>
            <view
              class="service-item"
              style="background-color: #fff5e9"
              @click="navigateTo('/pages/repair-order/list/index')">
              <text>报修记录</text>
              <image class="service-item-img" src="/static/home/<USER>" mode="aspectFit"></image>
            </view>
          </view>
        </view>

        <view class="todo-box">
          <view class="todo-title">
            <text>待办</text>
          </view>
          <view v-if="todoList.length === 0" class="empty-tip">
            <image src="/static/empty.png"></image>
            <text>暂无待办事项</text>
          </view>
          <view v-else class="todo-list">
            <view
              class="todo-item"
              v-for="(item, index) in todoList"
              :key="index"
              @click="navigateTo(`/pages/repair-order/detail/index?workOrderId=${item.workOrderId}`)">
              <view class="todo-item-title">
                <view class="todo-item-title-top">
                  <text class="todo-item-title-id">{{ `编号：${item.workOrderId}` }}</text>
                  <text class="todo-item-title-status" :class="`status-${getWorkOrderStatus(item.status).type}`">{{
                    getWorkOrderStatus(item.status).label
                  }}</text>
                </view>
                <text class="todo-item-title-time">{{ formatTime(item.reportTime) }}</text>
              </view>
              <view class="todo-content">
                <view
                  ><text class="todo-label">维修类型：</text
                  ><text class="todo-value">{{ item.serviceClassLabel }}</text></view
                >
                <view style="margin-top: 10rpx"
                  ><text class="todo-label">故障描述：</text><text class="todo-value">{{ item.faultDesc }}</text></view
                >
              </view>
            </view>
          </view>

          <view v-if="hasMore && todoList.length > 0" class="loading-more">
            <text>加载更多...</text>
          </view>
          <view v-if="!hasMore && todoList.length > 0" class="no-more">
            <text>没有更多数据了</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref } from "vue"
import { onShow, onLoad, onUnload } from "@dcloudio/uni-app"
import { useUserStore } from "@/store/user"
import { getWorkOrderListApi, getWorkOrderListByRepairManagerApi } from "@/api/workorder"
import { formatTime, getWorkOrderStatus } from "@/utils"

const userStore = useUserStore()
// 待办列表数据
const todoList = ref([])
const pageSize = 10
const currentPage = ref(1)
const hasMore = ref(true)
const isRefreshing = ref(false)

onLoad(() => {
  uni.$on("repairOrderListRefresh", resetRefresh)
})

onUnload(() => {
  uni.$off("repairOrderListRefresh", resetRefresh)
})

function resetRefresh() {
  currentPage.value = 1
  hasMore.value = true
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 300
  })
  fetchTodoList(1, true)
}

// 获取待办数据（进行中的工单）
const fetchTodoList = async (page = 1, refresh = false) => {
  try {
    // status为进行中的工单状态列表，根据实际业务情况调整
    let filters = `reportUnitId=${userStore.unit}`
    if (!userStore.isAuth) {
      filters += `,reporterId=${userStore.userId}`
    }

    const commonStatus = "WAITING_REPAIR_CONFIRM,WAITING_REPAIR_FINISH_CONFIRM"

    const params = {
      limit: pageSize,
      offset: (page - 1) * pageSize,
      commonStatus,
      filters
    }

    let requestApi = userStore.isAuth ? getWorkOrderListByRepairManagerApi : getWorkOrderListApi
    const response = await requestApi(params)

    if (response.data && response.data.data) {
      const { rows, pageElements } = response.data.data

      if (refresh) {
        todoList.value = rows
      } else {
        todoList.value = [...todoList.value, ...rows]
      }

      // 判断是否还有更多数据
      hasMore.value = todoList.value.length < pageElements.totalElements

      // 更新当前页码
      currentPage.value = page
    }
  } catch (error) {
    console.error("获取待办列表失败:", error)
    uni.showToast({
      title: "获取待办列表失败",
      icon: "none"
    })
  } finally {
    if (refresh) {
      isRefreshing.value = false
    }
  }
}

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true
  currentPage.value = 1
  hasMore.value = true
  fetchTodoList(1, true)
}

// 上拉加载更多
const loadMore = () => {
  if (hasMore.value) {
    fetchTodoList(currentPage.value + 1)
  }
}

// 页面跳转
const navigateTo = url => {
  uni.navigateTo({
    url
  })
}

// 电话报修
const callRepair = () => {
  uni.showActionSheet({
    itemList: ["拨打电话", "复制号码"],
    title: "028-86437777",
    success: res => {
      if (res.tapIndex === 0) {
        // 拨打电话
        uni.makePhoneCall({
          phoneNumber: "028-86437777",
          fail: () => {
            uni.showToast({
              title: "拨打电话失败",
              icon: "none"
            })
          }
        })
      } else if (res.tapIndex === 1) {
        // 复制号码
        uni.setClipboardData({
          data: "028-86437777",
          success: () => {
            uni.showToast({
              title: "电话号码已复制",
              icon: "none"
            })
          }
        })
      }
    }
  })
}

// 刷新数据
onShow(() => {
  currentPage.value = 1
  hasMore.value = true
  fetchTodoList(1, true)
})
</script>

<style lang="scss">
@font-face {
  font-family: "iconfont";
  src: url("/static/iconfont.ttf") format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 28rpx;
  color: #666;
}

.index-container {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;

  .page-scroll-view {
    height: 100%;
  }

  .status-bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: #fff;
  }

  .banner-box {
    width: 100%;
    position: relative;
    .banner-text {
      position: absolute;
      top: 0px;
      bottom: 0px;
      left: 66px;
      width: fit-content;
      height: fit-content;
      margin: auto 0;
      display: flex;
      flex-direction: column;
      background-image: linear-gradient(0deg, #09264e 0%, #004085 100%);
      color: transparent;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: bold;
      z-index: 2;
      .text-title {
        font-size: 64rpx;
        line-height: 75rpx;
      }
      .text-hint {
        margin-top: 10rpx;
        font-size: 40rpx;
        line-height: 47rpx;
      }
    }

    .banner-img {
      z-index: 1;
      width: 100%;
      object-fit: contain;
    }
  }

  .index-content {
    background: linear-gradient(180deg, #ffffff 0%, #f5f5f5 100%);
    padding: 48rpx 32rpx;
    min-height: calc(100% - 400rpx);
  }

  .service-box {
    display: flex;
    gap: 26rpx;
    height: 292rpx;
    .left-service {
      flex: 1;
      min-width: 0px;
      position: relative;
      .left-service-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .left-action {
        position: absolute;
        top: 37rpx;
        left: 40rpx;
        text {
          font-weight: bold;
          font-size: 36rpx;
          color: #ffffff;
        }
        .action-item {
          width: 142rpx;
          height: 50rpx;
          background: rgba(255, 255, 255, 0.21);
          border-radius: 1000rpx 1000rpx 1000rpx 1000rpx;
          border: 1rpx solid #ffffff;
          text-align: center;
          margin-top: 18rpx;
          text {
            font-size: 24rpx;
            line-height: 50rpx;
            font-weight: 500;
          }
        }
      }
    }
    .right-service {
      flex: 1;
      min-width: 0px;
      display: flex;
      flex-direction: column;
      gap: 26rpx;
      .service-item {
        flex: 1;
        min-height: 0px;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20rpx 0 30rpx;
        overflow: hidden;
        image {
          width: 120rpx;
          object-fit: contain;
          margin-top: 20rpx;
        }
        text {
          font-weight: 500;
          font-size: 32rpx;
          color: rgba(0, 0, 0, 0.7);
          line-height: 38rpx;
        }
      }
    }
  }

  .todo-box {
    margin: 68rpx 0rpx;

    .todo-title {
      font-weight: bold;
      font-size: 36rpx;
      color: rgba(0, 0, 0, 0.85);
      line-height: 42rpx;
      margin-bottom: 26rpx;
    }

    .empty-tip {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      color: #999;
      image {
        margin: 20rpx 0;
        width: 200rpx;
        height: 200rpx;
        object-fit: contain;
      }
    }

    .todo-list {
      display: flex;
      flex-direction: column;
      gap: 26rpx;
      .todo-item {
        display: flex;
        flex-direction: column;
        padding: 26rpx 0rpx;
        background: #fff;
        border-radius: 16rpx 16rpx 16rpx 16rpx;

        &:last-child {
          border-bottom: none;
        }

        .todo-item-title {
          display: flex;
          flex-direction: column;
          padding: 0rpx 32rpx 17rpx;
          border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
          .todo-item-title-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .todo-item-title-id {
              font-weight: 500;
              font-size: 32rpx;
              color: rgba(0, 0, 0, 0.85);
            }
            .todo-item-title-status {
              font-size: 24rpx;
              line-height: 28rpx;
            }
            .status {
              &-success {
                color: #1890ff;
              }
              &-warning {
                color: #e69215;
              }
              &-error {
                color: #f56c6c;
              }
              &-info {
                color: #909399;
              }
            }
          }
          .todo-item-title-time {
            font-size: 24rpx;
            color: rgba(0, 0, 0, 0.25);
            margin-top: 12rpx;
          }
        }

        .todo-content {
          padding: 17rpx 32rpx 0rpx;

          .todo-label {
            font-size: 28rpx;
            color: #000;
          }
          .todo-value {
            font-size: 28rpx;
            color: rgba(0, 0, 0, 0.4);
          }
        }

        .todo-status {
          font-size: 26rpx;
          padding: 8rpx 20rpx;
          border-radius: 24rpx;
        }
      }
    }
  }

  .loading-more,
  .no-more {
    text-align: center;
    padding: 20rpx 0;
    font-size: 24rpx;
    color: #999;
  }
}
</style>
