<template>
  <div class="admission-root">
    <div class="admission-header">
      <div class="admission-header-title">
        <span>入驻管理</span>
      </div>
      <div class="admission-header-action">
        <el-button
          type="primary"
          :icon="Plus"
          @click="$router.push('/maintenance-enterprise/admission-detail')"
        >
          新建单位
        </el-button>
      </div>
    </div>
    <div class="admission-container">
      <div class="admission-search">
        <SearchContainer
          @queryBtnClick="handleSearch"
          @resetBtnClick="handleReset"
        >
          <el-form-item label="单位名称">
            <el-input
              v-model.trim="searchForm.companyName"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="全部" value="" />
              <el-option label="正常" :value="CommonStatusEnum.ENABLED" />
              <el-option label="已禁用" :value="CommonStatusEnum.DISABLED" />
            </el-select>
          </el-form-item>
        </SearchContainer>
      </div>
      <div class="admission-content">
        <CommonTable
          :columns="admissionColumns"
          :requestApi="getMaintenanceEnterpriseList"
          :requestParams="requestParams"
          :dataCallback="dataCallback"
        >
          <template #status="{ row }">
            <el-tag
              :type="
                row.status === CommonStatusEnum.ENABLED ? 'success' : 'danger'
              "
            >
              {{ row.status === CommonStatusEnum.ENABLED ? "正常" : "已禁用" }}
            </el-tag>
          </template>
          <template #operations="{ row }">
            <el-button
              link
              type="primary"
              @click="
                $router.push({
                  path: '/maintenance-enterprise/admission-detail',
                  query: { enterpriseId: row.id },
                })
              "
            >
              查看
            </el-button>
            <el-button link type="primary" @click="viewStaff(row)">
              人员台账
            </el-button>
            <el-button link type="primary" @click="handleSettlementConfig(row)">
              结算配置
            </el-button>
          </template>
        </CommonTable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SearchContainer, CommonTable } from "@/base-components";
import { reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { admissionColumns } from "./config";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { getMaintenanceEnterpriseList } from "@/api";
import { CommonStatusEnum } from "@/configs";
const router = useRouter();

const searchForm = reactive({
  companyName: "",
  status: "",
});

const requestParams = reactive({
  filters: "",
});

function dataCallback(res: any) {
  return {
    tableRows: res.data?.data?.rows || [],
    total: res.data?.data?.pageElements?.totalElements || 0,
  };
}

function handleSearch() {
  const filters = [] as string[];
  for (let key in searchForm) {
    if (searchForm[key]) {
      filters.push(`${key}=${searchForm[key]}`);
    }
  }
  requestParams.filters = filters.join(",");
}

function handleReset() {
  searchForm.companyName = "";
  searchForm.status = "";
  requestParams.filters = "";
}

// 查看人员台账
const viewStaff = (row: any) => {
  router.push({
    path: "/maintenance-enterprise/staff",
    query: { unitName: row.companyName },
  });
};

// 跳转到结算配置页面
const handleSettlementConfig = (row: any) => {
  router.push({
    path: "/maintenance-enterprise/settlement-config",
    query: { enterpriseId: row.id },
  });
};
</script>

<style lang="less" scoped>
.admission-root {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  .admission-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    flex-shrink: 0;
  }

  .admission-container {
    flex: 1;
    min-height: 0px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .admission-search {
      flex-shrink: 0;
      background: #f5f5f5;
      border-radius: 10px;
      padding: 0px 20px;
      box-sizing: border-box;
    }

    .admission-content {
      margin-top: 10px;
      flex: 1;
      min-height: 0px;
    }
  }
}
</style>
