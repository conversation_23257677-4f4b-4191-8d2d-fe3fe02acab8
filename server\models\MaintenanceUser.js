const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { aesEncrypt, aesDecrypt } = require("../utils/encryption.js");

const maintenanceSchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
      unique: true,
    },
    username: {
      type: String,
      required: true,
    },
    userType: {
      type: String,
      required: true,
    },
    gender: {
      type: String,
      default: "",
    },
    phone: {
      type: String,
      required: true,
      set: aesEncrypt,
      get: aesDecrypt,
    },
    roles: [
      {
        type: String,
      },
    ],
    status: {
      type: String,
      enum: ["ENABLED", "WAITING_AUDIT", "AUDIT_FAILED", "DISABLED"],
      default: "ENABLED",
    },
    lastLoginTime: {
      type: Number,
      default: () => new Date().getTime(),
    },
    password: {
      type: String,
      required: true,
    },
    unit: {
      type: String,
      default: "",
    },
    areas: [
      {
        type: String,
      },
    ],
    workTypes: [
      {
        type: String,
      },
    ],
    // 资质信息
    qualifications: [
      {
        fileName: { type: String, required: true }, // 文件名
        fileId: { type: String, required: true }, // 文件ID
        fileType: { type: String, required: true }, // 文件类型
        _id: false,
      },
    ],
    rejectReason: {
      type: String,
      default: "",
    },
    auditHistory: [
      {
        status: { type: String, required: true },
        auditTime: { type: Number, required: true },
        auditUser: { type: String, required: true },
        auditReason: { type: String },
      },
    ],
  },
  { timestamps: true }
);

// 加密密码
maintenanceSchema.pre("save", async function (next) {
  if (!this.isModified("password")) {
    next();
  }
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
});

// 生成JWT Token
maintenanceSchema.methods.getSignedJwtToken = function () {
  return jwt.sign(
    { id: this._id, userType: this.userType },
    process.env.JWT_SECRET,
    {
      expiresIn: process.env.JWT_EXPIRE,
    }
  );
};

// 匹配用户输入的密码
maintenanceSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// 开启toJSON和toObject的getters选项,以确保在文档转换为JSON或普通对象时,
// 能正确调用Schema中定义的getter方法(如aesDecrypt)来解密敏感数据
maintenanceSchema.set("toJSON", { getters: true });
maintenanceSchema.set("toObject", { getters: true });

const MaintenanceUser = mongoose.model("MaintenanceUser", maintenanceSchema);

module.exports = {
  MaintenanceUser,
  MaintenanceUserSchema: maintenanceSchema,
};
