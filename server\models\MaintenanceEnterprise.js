const mongoose = require("mongoose");
const { aesEncrypt, aesDecrypt } = require("../utils/encryption.js");

const MaintenanceEnterpriseSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, unique: true }, // 企业ID
    companyName: { type: String, required: true }, // 公司名称
    legalPerson: { type: String, required: true }, // 法人姓名
    legalPersonPhone: { type: String, set: aesEncrypt, get: aesDecrypt }, // 法人联系电话
    contactPersonId: { type: String, required: true }, // 联系人ID
    contactPerson: { type: String, required: true }, // 联系人姓名
    contactPhone: {
      type: String,
      required: true,
      set: aesEncrypt,
      get: aesDecrypt,
    }, // 联系人电话
    companyAddress: { type: String, required: true }, // 公司办公地址
    operationArea: { type: [String], required: true }, // 所属区域，可多选
    operationAreaLabel: { type: String, required: true }, // 所属区域标签
    status: {
      type: String,
      enum: ["ENABLED", "DISABLED"],
      default: "ENABLED",
    }, // 状态

    // 资质信息
    qualificationFiles: [
      {
        fileName: { type: String, required: true }, // 文件名
        fileId: { type: String, required: true }, // 文件ID
        fileType: { type: String, required: true }, // 文件类型
        _id: false,
      },
    ],

    // 结算信息
    settlements: [
      {
        id: { type: String, required: true, unique: true, sparse: true },
        contractId: {
          type: String,
          required: true,
          unique: true,
          sparse: true,
        }, // 关联合同
        contractName: { type: String, required: true },
        contractPeriod: {
          type: [Number],
          required: true,
        },
        status: {
          type: String,
          enum: ["ENABLED", "DISABLED"],
          default: "ENABLED",
        }, // 状态,
        //配置项目的价格
        serviceItems: [
          {
            serviceItemId: { type: String, required: true }, // 服务项目_id
            serviceClassId: { type: String, required: true }, // 服务类型ID
            settlementPrice: { type: Number, required: true }, // 结算价格
            customTaxRate: { type: Number, required: true }, // 税率
            _id: false,
          },
        ],
        _id: false,
      },
    ],
  },
  {
    timestamps: true,
  }
);

// 在Schema定义后，开启toJSON和toObject的getters
MaintenanceEnterpriseSchema.set("toJSON", { getters: true });
MaintenanceEnterpriseSchema.set("toObject", { getters: true });

module.exports = {
  MaintenanceEnterprise: mongoose.model(
    "MaintenanceEnterprise",
    MaintenanceEnterpriseSchema
  ),
  MaintenanceEnterpriseSchema,
};
