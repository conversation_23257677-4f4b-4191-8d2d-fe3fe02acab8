export interface TableColSettingItem extends TableColumnItem {
  id: string;
  checked: boolean;
}

export interface TableColumnItem {
  type?: "selection" | "index" | "select" | "input" | "date" | "autocomplete";
  fixed?: "left" | "right";
  align?: "left" | "right" | "center";
  "header-align"?: "left" | "right" | "center";
  prop?: string;
  label?: string;
  width?: number;
  minWidth?: number;
  isShow?: boolean;
  must?: boolean;
  sortable?: boolean;
  optionName?: string;
  showOverflowTooltip?: boolean;
  [key: string]: any;
}

export * from "./maintenance";
