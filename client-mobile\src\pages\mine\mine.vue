<template>
  <view class="mine-container">
    <view class="status-bar"></view>
    <view class="user-info-box">
      <view class="avatar">
        <image src="/static/avatar.png" mode="aspectFit"></image>
      </view>
      <view class="user-info">
        <view class="username">{{ userStore.username }}</view>
        <view class="user-phone">{{ userStore.phone }}</view>
        <view class="user-company">{{ userStore.companyName }}</view>
      </view>
    </view>

    <view class="menu-list">
      <!-- <view class="menu-item" @click="goToAboutUs">
        <view class="menu-left">
          <view class="menu-icon">
            <uni-icons type="person" size="22" color="#3c9cff"></uni-icons>
          </view>
          <view class="menu-name">个人资料</view>
        </view>
        <view class="menu-right">
          <uni-icons type="right" size="16" color="#c8c9cc"></uni-icons>
        </view>
      </view> -->
      <view class="menu-item" @click="navigateTo('/pages/repair-order/list/index')">
        <view class="menu-left">
          <view class="menu-icon">
            <uni-icons type="list" size="22" color="#3c9cff"></uni-icons>
          </view>
          <view class="menu-name">报修记录</view>
        </view>
        <view class="menu-right">
          <uni-icons type="right" size="16" color="#c8c9cc"></uni-icons>
        </view>
      </view>
      <!-- <view class="menu-item" @click="goToAboutUs">
        <view class="menu-left">
          <view class="menu-icon">
            <uni-icons type="chat" size="22" color="#3c9cff"></uni-icons>
          </view>
          <view class="menu-name">意见反馈</view>
        </view>
        <view class="menu-right">
          <uni-icons type="right" size="16" color="#c8c9cc"></uni-icons>
        </view>
      </view> -->
      <view class="menu-item" @click="navigateTo('/pages/mine/reset-password')">
        <view class="menu-left">
          <view class="menu-icon">
            <uni-icons type="gear" size="22" color="#3c9cff"></uni-icons>
          </view>
          <view class="menu-name">修改密码</view>
        </view>
        <view class="menu-right">
          <uni-icons type="right" size="16" color="#c8c9cc"></uni-icons>
        </view>
      </view>
    </view>

    <view class="logout-btn" @click="handleLogout">退出登录</view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { useUserStore } from "@/store/user"
import { logout } from "@/api"
import { showLoading, hideLoading, showToast } from "@/utils"
const userStore = useUserStore()

// 页面跳转
const navigateTo = (url: string) => {
  uni.navigateTo({
    url
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: "提示",
    content: "确定要退出登录吗？",
    success: res => {
      if (res.confirm) {
        showLoading("退出登录中...")
        logout()
          .then(() => {
            // 清除登录状态
            userStore.clearUserInfo()
            // 跳转到登录页
            uni.redirectTo({
              url: "/pages/login/login"
            })
          })
          .catch((error: any) => {
            showToast(error.message)
          })
          .finally(() => {
            hideLoading()
          })
      }
    }
  })
}

//开发中
const goToAboutUs = () => {
  uni.showToast({
    title: "功能开发中",
    icon: "none"
  })
}
</script>

<style lang="scss">
.mine-container {
  min-height: 100%;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
  box-sizing: border-box;

  .status-bar {
    height: var(--status-bar-height);
    width: 100%;
    background-color: #fff;
  }

  .user-info-box {
    background-color: $uni-color-primary;
    padding: 40rpx 30rpx;
    display: flex;
    align-items: center;

    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      overflow: hidden;
      border: 4rpx solid rgba(255, 255, 255, 0.4);

      image {
        width: 100%;
        height: 100%;
      }
    }

    .user-info {
      margin-left: 30rpx;
      color: #fff;

      .username {
        font-size: 36rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
      }
      .user-company {
        font-size: 28rpx;
        opacity: 0.8;
      }

      .user-phone {
        font-size: 28rpx;
        opacity: 0.8;
        margin-bottom: 8rpx;
      }

      .user-desc {
        font-size: 28rpx;
        opacity: 0.8;
      }
    }
  }

  .menu-list {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    padding: 0 20rpx;

    .menu-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100rpx;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .menu-left {
        display: flex;
        align-items: center;

        .menu-icon {
          width: 50rpx;
          height: 50rpx;
          background-color: rgba($uni-color-primary, 0.1);
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 20rpx;
        }

        .menu-name {
          font-size: 30rpx;
          color: #333;
        }
      }

      .menu-right {
        color: #c8c9cc;
        font-size: 26rpx;
      }
    }
  }

  .logout-btn {
    margin: 40rpx 20rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    background-color: #fff;
    color: $uni-color-error;
    font-size: 32rpx;
    border-radius: 12rpx;
  }
}
</style>
