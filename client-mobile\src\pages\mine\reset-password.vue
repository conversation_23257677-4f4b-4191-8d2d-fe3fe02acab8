<template>
  <view class="reset-container page-container">
    <NavBar title="修改密码" />

    <view class="form-box page-content-with-nav">
      <view class="input-item">
        <view class="input-prefix">
          <uni-icons type="locked" size="20" color="#666"></uni-icons>
        </view>
        <input
          class="uni-input"
          type="password"
          v-model="formData.oldPassword"
          placeholder="请输入旧密码"
          placeholder-class="input-placeholder" />
      </view>
      <view class="input-item">
        <view class="input-prefix">
          <uni-icons type="locked" size="20" color="#666"></uni-icons>
        </view>
        <input
          class="uni-input"
          type="password"
          v-model="formData.newPassword"
          placeholder="请输入至少8位数字、大小写字母和字符的组合"
          placeholder-class="input-placeholder" />
      </view>
      <view class="input-item">
        <view class="input-prefix">
          <uni-icons type="locked" size="20" color="#666"></uni-icons>
        </view>
        <input
          class="uni-input"
          type="password"
          v-model="formData.confirmPassword"
          placeholder="确认新密码"
          placeholder-class="input-placeholder" />
      </view>

      <view class="btn-box">
        <button class="confirm-btn primary-btn" @click="handleReset">确定</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive } from "vue"
import { updatePassword } from "@/api/user"
import { UserTypeEnum } from "@/configs"
import { showToast, mixPasswordValidator, navigateBack } from "@/utils"
import { NavBar } from "@/components"
import { useUserStore } from "@/store/user"

const userStore = useUserStore()

// 表单数据
const formData = reactive({
  oldPassword: "",
  newPassword: "",
  confirmPassword: ""
})

// 修改密码
const handleReset = () => {
  if (!formData.oldPassword) {
    showToast("请输入旧密码")
    return
  }

  if (!formData.newPassword) {
    showToast("请输入新密码")
    return
  }

  const newPasswordError = mixPasswordValidator(formData.newPassword)
  if (newPasswordError) {
    showToast(newPasswordError)
    return
  }

  if (formData.newPassword !== formData.confirmPassword) {
    showToast("两次密码输入不一致")
    return
  }

  uni.showLoading({ title: "提交中" })

  updatePassword({
    phone: userStore.phone,
    oldPassword: formData.oldPassword,
    newPassword: formData.newPassword,
    userType: UserTypeEnum.REPAIR_UNIT
  })
    .then(() => {
      uni.hideLoading()
      showToast("密码修改成功")
      navigateBack("/pages/mine/mine")
    })
    .catch(err => {
      console.log(err)
      uni.hideLoading()
      showToast(err.message || "修改失败，请重试")
    })
}
</script>

<style lang="scss">
.reset-container {
  background-color: #fff;
  .form-box {
    padding: 0 40rpx;

    .input-item {
      display: flex;
      align-items: center;
      height: 88rpx;
      margin-bottom: 30rpx;
      border-bottom: 1px solid #eee;

      .input-prefix {
        margin-right: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40rpx;
      }

      .uni-input {
        flex: 1;
        height: 88rpx;
        font-size: 28rpx;
      }

      .input-placeholder {
        color: #c8c9cc;
      }
    }
  }

  .btn-box {
    margin-top: 80rpx;

    .confirm-btn {
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
      box-sizing: border-box;
    }

    .primary-btn {
      background-color: $uni-color-primary;
      color: #fff;
      border: none;
    }
  }
}

.page-content-with-nav {
  margin-top: 60rpx;
  box-sizing: border-box;
}
</style>
