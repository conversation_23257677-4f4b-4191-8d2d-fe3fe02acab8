const { ServiceItem, ServiceItemSchema } = require("../models/ServiceItems");
const { ServiceClass } = require("../models/ServiceClass");
const { getFilterObj } = require("../utils/filters");
const { generateServiceItemCode } = require("../utils/service-content");
const { getEffectiveSettlementTypeUtil } = require("../utils/workorder");
const { MaintenanceEnterprise } = require("../models/MaintenanceEnterprise");
const { RepairEnterprise } = require("../models/RepairEnterprise");
const { set } = require("mongoose");
const { v4: uuid } = require("uuid");

// 获取服务类别
module.exports.getServiceClass = async (req, res) => {
  try {
    let serviceContent = [];
    serviceContent = await ServiceClass.find().select("-_id id serviceClass");

    res.status(200).json({
      success: true,
      message: "获取服务类别成功",
      data: serviceContent,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取服务类别失败",
      error: error.message,
    });
  }
};

// 添加服务类别
module.exports.addServiceClass = async (req, res) => {
  try {
    const { serviceClass, id } = req.body;
    await ServiceClass.create({ serviceClass, id });
    res.status(200).json({
      success: true,
      message: "添加服务类别成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "添加服务类别失败",
      error: error.message,
    });
  }
};

// 编辑服务类别
module.exports.editServiceClass = async (req, res) => {
  try {
    const { serviceClass, id } = req.body;
    await ServiceClass.findOneAndUpdate({ id }, { serviceClass });
    res.status(200).json({
      success: true,
      message: "编辑服务类别成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "编辑服务类别失败",
      error: error.message,
    });
  }
};

// 删除服务类别
module.exports.deleteServiceClass = async (req, res) => {
  try {
    const { id } = req.params;
    // 删除服务类别
    await ServiceClass.deleteOne({ id });

    // 删除服务类别下的服务项目
    await ServiceItem.deleteMany({ serviceClass: id });

    res.status(200).json({
      success: true,
      message: "删除服务类别成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "删除服务类别失败",
      error: error.message,
    });
  }
};

// 获取所有服务项目
module.exports.getServiceItem = async (req, res) => {
  try {
    let { offset, limit, filters } = req.query;
    offset = Number(offset) || 0;
    limit = Number(limit) || 10;

    let filtersObj = {};
    if (filters) {
      filtersObj = getFilterObj(filters, ServiceItemSchema);
    }

    // 返回数据和总数
    let [serviceItems, total] = await Promise.all([
      ServiceItem.find(filtersObj)
        .skip(offset)
        .limit(limit)
        .sort({ updatedAt: -1 }),
      ServiceItem.countDocuments(filtersObj),
    ]);
    serviceItems = serviceItems.map((item) => item.toObject());

    // 根据serviceClass获取名称
    for (const item of serviceItems) {
      const serviceClass = await ServiceClass.findOne({
        id: item.serviceClass,
      });
      if (serviceClass) {
        item.serviceClassName = serviceClass.serviceClass;
      }
    }

    res.status(200).json({
      success: true,
      message: "获取服务项目成功",
      data: {
        rows: serviceItems,
        pageElements: {
          totalElements: total,
          pageSize: limit,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取服务项目失败",
      error: error.message,
    });
  }
};

// 获取维保方指定合同的服务项目
module.exports.getMaintenanceServiceItem = async (req, res) => {
  try {
    let { offset, limit, filters, maintenanceUnitId, settlementId } = req.query;
    offset = Number(offset) || 0;
    limit = Number(limit) || 10;

    let filtersObj = {};
    if (filters) {
      filtersObj = getFilterObj(filters, ServiceItemSchema);
    }

    // 返回数据和总数
    let [serviceItems, total] = await Promise.all([
      ServiceItem.find(filtersObj)
        .skip(offset)
        .limit(limit)
        .sort({ updatedAt: -1 }),
      ServiceItem.countDocuments(filtersObj),
    ]);
    serviceItems = serviceItems.map((item) => item.toObject());
    // 根据serviceClass获取名称
    for (const item of serviceItems) {
      const serviceClass = await ServiceClass.findOne({
        id: item.serviceClass,
      });
      if (serviceClass) {
        item.serviceClassName = serviceClass.serviceClass;
      }
    }

    // 获取维保单位设置的价格
    const maintenanceEnterprise = await MaintenanceEnterprise.findOne({
      id: maintenanceUnitId,
    });

    if (!maintenanceEnterprise) {
      return res.status(500).json({
        success: false,
        message: "未找到该维保单位",
      });
    }

    // 如果有settlementId，则获取具体结算信息的价格
    let settlement;
    if (settlementId) {
      settlement = maintenanceEnterprise.settlements.find(
        (item) => item.id === settlementId
      );

      if (!settlement) {
        return res.status(500).json({
          success: false,
          message: "未找到该结算信息",
        });
      }
    }

    // 获取自定义的结算价和税率
    if (settlement) {
      for (const item of serviceItems) {
        const serviceItem = settlement.serviceItems.find((priceItem) => {
          return priceItem.serviceItemId === item._id.toString();
        });

        if (serviceItem) {
          item.settlementPrice = serviceItem.settlementPrice;
          item.customTaxRate = serviceItem.customTaxRate;
        }
      }
    }

    res.status(200).json({
      success: true,
      message: "获取维保方服务项目成功",
      data: {
        rows: serviceItems,
        pageElements: {
          totalElements: total,
          pageSize: limit,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取维保方服务项目失败",
      error: error.message,
    });
  }
};

// 获取报修方当前生效的服务项目列表
module.exports.getRepairServiceItem = async (req, res) => {
  try {
    let { offset, limit, filters, repairUnitId } = req.query;
    offset = Number(offset) || 0;
    limit = Number(limit) || 10;

    let filtersObj = {};
    if (filters) {
      filtersObj = getFilterObj(filters, ServiceItemSchema);
    }

    // 返回数据和总数
    let [serviceItems, total] = await Promise.all([
      ServiceItem.find(filtersObj)
        .skip(offset)
        .limit(limit)
        .sort({ updatedAt: -1 }),
      ServiceItem.countDocuments(filtersObj),
    ]);
    serviceItems = serviceItems.map((item) => item.toObject());
    // 根据serviceClass获取名称
    for (const item of serviceItems) {
      const serviceClass = await ServiceClass.findOne({
        id: item.serviceClass,
      });
      if (serviceClass) {
        item.serviceClassName = serviceClass.serviceClass;
      }
    }

    // 获取报修单位的结算配置
    const repairEnterprise = await RepairEnterprise.findOne({
      id: repairUnitId,
    });

    if (!repairEnterprise) {
      return res.status(500).json({
        success: false,
        message: "未找到该报修单位",
      });
    }

    // 将结算方式信息回写到服务项目中
    for (const item of serviceItems) {
      // 默认设置为一单一结
      item.settlementType = "IMMEDIATELY";

      // 为每个服务项目获取对应的结算方式
      const effectiveSettlement = await getEffectiveSettlementTypeUtil(
        repairEnterprise.settlements,
        item._id.toString()
      );

      // 如果找到了生效的结算方式，设置对应的结算信息
      if (effectiveSettlement) {
        item.settlementType = effectiveSettlement.settlementType;
        item.settlementId = effectiveSettlement.id;
        item.contractId = effectiveSettlement.contractId;
        item.contractName = effectiveSettlement.contractName;
        item.contractPeriod = effectiveSettlement.contractPeriod;
        if (effectiveSettlement.settlementType !== "IMMEDIATELY") {
          item.unitPrice = 0;
        }
      }
    }

    res.status(200).json({
      success: true,
      message: "获取报修方服务项目成功",
      data: {
        rows: serviceItems,
        pageElements: {
          totalElements: total,
          pageSize: limit,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "获取报修方服务项目失败",
      error: error.message,
    });
  }
};

// 添加服务项目
module.exports.addServiceItem = async (req, res) => {
  try {
    const {
      serviceItem,
      serviceClass,
      unit,
      unitPrice,
      maxPrice,
      taxRate,
      code,
    } = req.body;

    // 检查code是否存在
    if (await ServiceItem.findOne({ code })) {
      return res.status(500).json({
        success: false,
        message: "服务项目编号已存在",
      });
    }

    // 创建服务项目
    await ServiceItem.create({
      code,
      serviceItem,
      serviceClass,
      unit,
      unitPrice,
      maxPrice,
      taxRate,
    });

    res.status(200).json({
      success: true,
      message: "添加服务项目成功",
      data: { code },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "添加服务项目失败",
      error: error.message,
    });
  }
};

// 编辑服务项目
module.exports.editServiceItem = async (req, res) => {
  try {
    const { _id, ...rest } = req.body;

    // 更新服务项目
    await ServiceItem.findOneAndUpdate({ _id }, { ...rest });

    res.status(200).json({
      success: true,
      message: "编辑服务项目成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "编辑服务项目失败",
      error: error.message,
    });
  }
};

// 删除服务项目
module.exports.deleteServiceItem = async (req, res) => {
  try {
    const { _id } = req.params;

    // 删除服务项目
    await ServiceItem.deleteOne({ _id });

    res.status(200).json({
      success: true,
      message: "删除服务项目成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "删除服务项目失败",
      error: error.message,
    });
  }
};

// 批量设置某个服务类别的所有服务项目的结算价
module.exports.setServiceItemsPrice = async (req, res) => {
  try {
    const { serviceClass, ratio, maintenanceEnterpriseId, settlementId } =
      req.body;

    // 维保企业
    const maintenanceEnterprise = await MaintenanceEnterprise.findOne({
      id: maintenanceEnterpriseId,
    });

    if (!maintenanceEnterprise) {
      return res.status(500).json({
        success: false,
        message: "未找到该维保单位",
      });
    }

    // 具体合同
    const settlement = maintenanceEnterprise.settlements.find(
      (item) => item.id === settlementId
    );

    if (!settlement) {
      return res.status(500).json({
        success: false,
        message: "未找到该结算信息",
      });
    }

    // 当前类别的服务项目
    const serviceItems = await ServiceItem.find({ serviceClass });

    if (!settlement.serviceItems) {
      settlement.serviceItems = [];
    }

    serviceItems.forEach((origin) => {
      const existIndex = settlement.serviceItems.findIndex(
        (item) => item.serviceItemId === origin._id.toString()
      );
      const result = parseFloat((origin.maxPrice * (ratio / 100)).toFixed(2));
      if (existIndex !== -1) {
        // 更新
        settlement.serviceItems[existIndex].settlementPrice = result;
      } else {
        // 新增
        settlement.serviceItems.push({
          serviceItemId: origin._id.toString(),
          settlementPrice: result,
          serviceClassId: origin.serviceClass,
          customTaxRate: origin.taxRate,
        });
      }
    });

    await maintenanceEnterprise.save();

    res.status(200).json({
      success: true,
      message: "设置服务项目结算价成功",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "设置服务项目结算价失败",
      error: error.message,
    });
  }
};
